Feature: Simple TodoMVC Test
  As a QA engineer
  I want to test basic TodoMVC functionality
  So that I can verify the framework works correctly

  @smoke @simple
  Scenario: Basic TodoMVC Navigation and Interaction
    Given I navigate to "https://todomvc.com/examples/react/"
    When I wait for the page to load
    Then I should see ".todoapp"
    And the page title should contain "TodoMVC"
    
    # Add a simple todo
    When I type "Test Framework Demo" into ".new-todo"
    And I press "Enter"
    Then I should see ".todo-list li"
    And ".todo-list li:first-child label" should contain text "Test Framework Demo"
    
    # Mark as completed
    When I click on ".todo-list li:first-child .toggle"
    Then ".todo-list li:first-child" should have class "completed"
    
    # Take a screenshot
    When I take a screenshot named "simple-todomvc-test-completed"
    
    # Verify the todo count
    Then ".todo-count" should contain text "0 items left"

  @smoke @accessibility
  Scenario: Basic Accessibility Test
    Given I navigate to "https://todomvc.com/examples/react/"
    When I wait for the page to load
    Then the page should be accessible
    And I take a screenshot named "accessibility-test-passed"
