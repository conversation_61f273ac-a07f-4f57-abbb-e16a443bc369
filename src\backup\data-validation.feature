@data-validation
Feature: Data Validation
  As a QA engineer
  I want to validate data in the application
  So that I can ensure the application displays correct information

  Background:
    Given I navigate to "https://example.com"
    And I wait for page to load

  @smoke @data-validation
  Scenario: Verify text content
    When I navigate to "https://example.com/products"
    And I wait for page to load
    Then the "#product-title" element should contain text "Product List"
    And the "#product-description" element should have exact text "Browse our products"
    And the "#product-count" element should match regex "\\d+ products found"

  @data-validation
  Scenario: Verify attribute values
    When I navigate to "https://example.com/products"
    And I wait for page to load
    Then the "#product-image" element should have attribute "alt" with value "Product Image"
    And the "#product-link" element should have attribute "href" with value "https://example.com/product/1"
    And the "#product-button" element should have attribute "disabled"
    And the "#product-input" element should not have attribute "readonly"

  @data-validation
  Scenario: Verify element counts
    When I navigate to "https://example.com/products"
    And I wait for page to load
    Then there should be 5 ".product-item" elements
    And there should be more than 3 ".product-image" elements
    And there should be less than 10 ".product-price" elements

  @data-validation
  Scenario: Verify form values
    When I navigate to "https://example.com/contact"
    And I wait for page to load
    When I fill "#name" with "<PERSON> Doe"
    And I fill "#email" with "<EMAIL>"
    And I check "#subscribe"
    Then the "#name" input should have value "John Doe"
    And the "#email" input should have value "<EMAIL>"
    And the "#subscribe" checkbox should be checked
    And the "#newsletter" checkbox should not be checked

  @data-validation
  Scenario: Verify dropdown selection
    When I navigate to "https://example.com/preferences"
    And I wait for page to load
    When I select "English" from "#language"
    Then the "#language" select should have option "English" selected

  @data-validation
  Scenario: Verify list content
    When I navigate to "https://example.com/menu"
    And I wait for page to load
    Then the "#main-menu" list should contain items:
      | Home |
      | Products |
      | About |
      | Contact |
    And the "#sub-menu" list should contain exactly these items in order:
      | Profile |
      | Settings |
      | Logout |
