<testsuites id="" name="" tests="1" failures="1" skipped="0" errors="0" time="38.683821">
<testsuite name="cucumber-wrapper.spec.ts" timestamp="2025-06-11T05:42:48.515Z" hostname="edge" tests="1" failures="1" skipped="0" time="32.974" errors="0">
<testcase name="Self Service Featured Results › TC0101- Login and Check the Default Data Source Order" classname="cucumber-wrapper.spec.ts" time="32.974">
<properties>
<property name="feature" value="Self Service Featured Results">
</property>
<property name="tags" value="@combined, @self-service, @fr">
</property>
<property name="file" value="src\features\self-service-fr.feature">
</property>
</properties>
<failure message="cucumber-wrapper.spec.ts:34:11 TC0101- Login and Check the Default Data Source Order" type="FAILURE">
<![CDATA[  [edge] › cucumber-wrapper.spec.ts:34:11 › Self Service Featured Results › TC0101- <PERSON><PERSON> and Check the Default Data Source Order 

    Error: page.screenshot: Target page, context or browser has been closed

       at ..\src\core\playwright-cucumber-runner.ts:270

      268 |       // Take failure screenshot
      269 |       const failureScreenshot = path.join(testInfo.outputDir, 'failure-screenshot.png');
    > 270 |       await page.screenshot({ path: failureScreenshot, fullPage: true });
          |                  ^
      271 |       await testInfo.attach('Failure Screenshot', {
      272 |         path: failureScreenshot,
      273 |         contentType: 'image/png'
        at PlaywrightCucumberRunner.executeScenario (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:270:18)
        at C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:45:9

    attachment #1: Step 1 - Before: Given I navigate to URL "https://es-settings-staging.kpmg.com/" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--fb638ebdbe80a7756a8e63079085f5659f232f8d.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: Step 1 - After: Given I navigate to URL "https://es-settings-staging.kpmg.com/" (3955ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-3955ms--c301db7883c8ac54fad2dda0014def9f64fa9ef6.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: Step 2 - Before: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-c181d5006af6c480a37f3073811d405ef4d7d4bb.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: Step 2 - After: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000 (7760ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-7760ms--dbcfbedf8ddf18ee8081c74d9174986056ee01a9.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: Step 3 - Before: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com--e62e82a00bce764349f7a3421da54aa70d7c5945.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #6: Step 3 - After: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>" (165ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com-165ms--ccc4322a28c3a78507b4e014c4195af45a0cc374.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #7: Step 4 - Before: And I click on "getByRole('button', { name: 'Next' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-4---Before-And-I-click-on-getByRole-button-name-Next--5af3fa92aa35cd659ae9cfc51f250822ceddc396.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #8: Step 4 - After: And I click on "getByRole('button', { name: 'Next' })" (149ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-4---After-And-I-click-on-getByRole-button-name-Next-149ms--c007bb4fd799ce37174f0d986ee15b0e40044124.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #9: Step 5 - Before: Then I wait for element "getByRole('button', { name: 'Settings' })" to be visible with timeout 60000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-52592266963c7f3754edee9d81ffa66787e15c0e.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #10: video (video/webm) ─────────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #11: trace (application/zip) ────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\trace.zip
    Usage:

        npx playwright show-trace test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🎬 Starting scenario: TC0101- Login and Check the Default Data Source Order
📁 Feature: Self Service Featured Results
🏷️ Tags: @combined, @self-service, @fr
📋 Step 1/48: Given I navigate to URL "https://es-settings-staging.kpmg.com/"
✅ Step completed in 3955ms
📋 Step 2/48: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000
✅ Step completed in 7760ms
📋 Step 3/48: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>"
✅ Step completed in 165ms
📋 Step 4/48: And I click on "getByRole('button', { name: 'Next' })"
✅ Step completed in 149ms
📋 Step 5/48: Then I wait for element "getByRole('button', { name: 'Settings' })" to be visible with timeout 60000
[[31merror[39m]: ❌ Step failed: Then I wait for element "getByRole('button', { name: 'Settings' })" to be visible with timeout 60000

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--fb638ebdbe80a7756a8e63079085f5659f232f8d.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-3955ms--c301db7883c8ac54fad2dda0014def9f64fa9ef6.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-c181d5006af6c480a37f3073811d405ef4d7d4bb.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-7760ms--dbcfbedf8ddf18ee8081c74d9174986056ee01a9.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com--e62e82a00bce764349f7a3421da54aa70d7c5945.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com-165ms--ccc4322a28c3a78507b4e014c4195af45a0cc374.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-4---Before-And-I-click-on-getByRole-button-name-Next--5af3fa92aa35cd659ae9cfc51f250822ceddc396.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-4---After-And-I-click-on-getByRole-button-name-Next-149ms--c007bb4fd799ce37174f0d986ee15b0e40044124.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-52592266963c7f3754edee9d81ffa66787e15c0e.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\video.webm]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[❌ Scenario failed: TC0101- Login and Check the Default Data Source Order locator.waitFor: Target page, context or browser has been closed
Call log:
[2m  - waiting for getByRole('button', { name: 'Settings' }) to be visible[22m
[2m    - waiting for" https://gfs2.glb.kworld.kpmg.com/adfs/ls/?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%26es…" navigation to finish...[22m
[2m    - navigated to "https://gfs2.glb.kworld.kpmg.com/adfs/ls/?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%26es…"[22m
[2m    - waiting for" https://gfs2.glb.kworld.kpmg.com/adfs/ls/wia?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%2…" navigation to finish...[22m
[2m    - navigated to "https://gfs2.glb.kworld.kpmg.com/adfs/ls/wia?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%2…"[22m
[2m    - waiting for navigation to finish...[22m
[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P8QIuVg8NsOEQ7Nf3s512kuvL5G7OxMKOPmxzhEMydDLJ5qH-4HAuc01XR…"[22m
[2m    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...[22m
[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P-cFXrRPM0fOc4CVwfbjlzlc4VBw37FZ5g_OY9tIE14qqTI2_ajrzNwWaQ…"[22m
[2m    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...[22m
[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P88UQ75CI-owpCWOB_Pz5G2URf8ettxDsrcbeMC5E_tLTH6Mz9Fesda5aP…"[22m
[2m    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...[22m
[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P9xVue0Fy26j3ilvJ_X9QgorPTVk2k2OUMMBq0i1BMi-yjf3tGWtKjwU8V…"[22m
[2m    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...[22m
[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P9zH864M85G3xj2XC2VI--Wkt4zXZqXvzTJROLRib3FIjNjPInITG9memX…"[22m
[2m    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...[22m
[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P_vhH2fCBb2qS11Vx9o7o9nbk1QKm5QWfl5x5y6JB7pSHU6EKDY49vJ5Nq…"[22m

    at PlaywrightWorld.waitForElement [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-world.ts:436:19[90m)[39m
    at Object.handler [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\step-definition-registry.ts:189:21[90m)[39m
    at StepDefinitionRegistry.executeStep [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\step-definition-registry.ts:361:23[90m)[39m
    at PlaywrightCucumberRunner.executeStep [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-cucumber-runner.ts:196:31[90m)[39m
    at PlaywrightCucumberRunner.executeScenario [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-cucumber-runner.ts:231:20[90m)[39m
    at [90mC:\workspace\Playwright_Automation_Framework\[39mtests\cucumber-wrapper.spec.ts:45:9 {
  [[32mSymbol(step)[39m]: {
    stepId: [32m'pw:api@31'[39m,
    location: {
      file: [32m'C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts'[39m,
      line: [33m436[39m,
      column: [33m19[39m,
      function: [32m'PlaywrightWorld.waitForElement'[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m"locator.getByRole('button', { name: 'Settings' }).waitFor"[39m,
    apiName: [32m'locator.waitFor'[39m,
    params: {
      selector: [32m'internal:role=button[name="Settings"i]'[39m,
      strict: [33mtrue[39m,
      omitReturnValue: [33mtrue[39m,
      state: [32m'visible'[39m,
      timeout: [33m60000[39m
    },
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@31'[39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1749620603574[39m,
    error: {
      message: [32m'Error: locator.waitFor: Target page, context or browser has been closed\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for getByRole('button', { name: 'Settings' }) to be visible\x1B[22m\n"[39m +
        [32m'\x1B[2m    - waiting for" https://gfs2.glb.kworld.kpmg.com/adfs/ls/?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%26es…" navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://gfs2.glb.kworld.kpmg.com/adfs/ls/?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%26es…"\x1B[22m\n'[39m +
        [32m'\x1B[2m    - waiting for" https://gfs2.glb.kworld.kpmg.com/adfs/ls/wia?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%2…" navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://gfs2.glb.kworld.kpmg.com/adfs/ls/wia?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%2…"\x1B[22m\n'[39m +
        [32m'\x1B[2m    - waiting for navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P8QIuVg8NsOEQ7Nf3s512kuvL5G7OxMKOPmxzhEMydDLJ5qH-4HAuc01XR…"\x1B[22m\n'[39m +
        [32m'\x1B[2m    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P-cFXrRPM0fOc4CVwfbjlzlc4VBw37FZ5g_OY9tIE14qqTI2_ajrzNwWaQ…"\x1B[22m\n'[39m +
        [32m'\x1B[2m    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P88UQ75CI-owpCWOB_Pz5G2URf8ettxDsrcbeMC5E_tLTH6Mz9Fesda5aP…"\x1B[22m\n'[39m +
        [32m'\x1B[2m    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P9xVue0Fy26j3ilvJ_X9QgorPTVk2k2OUMMBq0i1BMi-yjf3tGWtKjwU8V…"\x1B[22m\n'[39m +
        [32m'\x1B[2m    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P9zH864M85G3xj2XC2VI--Wkt4zXZqXvzTJROLRib3FIjNjPInITG9memX…"\x1B[22m\n'[39m +
        [32m'\x1B[2m    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P_vhH2fCBb2qS11Vx9o7o9nbk1QKm5QWfl5x5y6JB7pSHU6EKDY49vJ5Nq…"\x1B[22m\n'[39m,
      stack: [32m'Error: locator.waitFor: Target page, context or browser has been closed\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for getByRole('button', { name: 'Settings' }) to be visible\x1B[22m\n"[39m +
        [32m'\x1B[2m    - waiting for" https://gfs2.glb.kworld.kpmg.com/adfs/ls/?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%26es…" navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://gfs2.glb.kworld.kpmg.com/adfs/ls/?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%26es…"\x1B[22m\n'[39m +
        [32m'\x1B[2m    - waiting for" https://gfs2.glb.kworld.kpmg.com/adfs/ls/wia?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%2…" navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://gfs2.glb.kworld.kpmg.com/adfs/ls/wia?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%2…"\x1B[22m\n'[39m +
        [32m'\x1B[2m    - waiting for navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P8QIuVg8NsOEQ7Nf3s512kuvL5G7OxMKOPmxzhEMydDLJ5qH-4HAuc01XR…"\x1B[22m\n'[39m +
        [32m'\x1B[2m    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P-cFXrRPM0fOc4CVwfbjlzlc4VBw37FZ5g_OY9tIE14qqTI2_ajrzNwWaQ…"\x1B[22m\n'[39m +
        [32m'\x1B[2m    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P88UQ75CI-owpCWOB_Pz5G2URf8ettxDsrcbeMC5E_tLTH6Mz9Fesda5aP…"\x1B[22m\n'[39m +
        [32m'\x1B[2m    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P9xVue0Fy26j3ilvJ_X9QgorPTVk2k2OUMMBq0i1BMi-yjf3tGWtKjwU8V…"\x1B[22m\n'[39m +
        [32m'\x1B[2m    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P9zH864M85G3xj2XC2VI--Wkt4zXZqXvzTJROLRib3FIjNjPInITG9memX…"\x1B[22m\n'[39m +
        [32m'\x1B[2m    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...\x1B[22m\n'[39m +
        [32m'\x1B[2m    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P_vhH2fCBb2qS11Vx9o7o9nbk1QKm5QWfl5x5y6JB7pSHU6EKDY49vJ5Nq…"\x1B[22m\n'[39m +
        [32m'\n'[39m +
        [32m'    at PlaywrightWorld.waitForElement (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts:436:19)\n'[39m +
        [32m'    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:189:21)\n'[39m +
        [32m'    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:361:23)\n'[39m +
        [32m'    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:196:31)\n'[39m +
        [32m'    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:231:20)\n'[39m +
        [32m'    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9'[39m,
      cause: [90mundefined[39m
    }
  }
}
]]>
</system-err>
</testcase>
</testsuite>
</testsuites>