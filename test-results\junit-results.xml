<testsuites id="" name="" tests="1" failures="0" skipped="0" errors="0" time="9.499165000000001">
<testsuite name="viewport-test.spec.ts" timestamp="2025-06-11T05:48:59.615Z" hostname="edge" tests="1" failures="0" skipped="0" time="4.982" errors="0">
<testcase name="Verify viewport configuration" classname="viewport-test.spec.ts" time="4.982">
<system-out>
<![CDATA[=== Viewport Test ===
Actual viewport: 1920x1080
Window inner size: 1920x1080
Window outer size: 1936x1100
Screen size: 1920x1080
Expected viewport: 1920x1080
✅ Viewport test passed!

[[ATTACHMENT|playwright-data\viewport-test-Verify-viewport-configuration-edge\video.webm]]

[[ATTACHMENT|playwright-data\viewport-test-Verify-viewport-configuration-edge\trace.zip]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>