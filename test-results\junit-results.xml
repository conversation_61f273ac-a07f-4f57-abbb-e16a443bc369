<testsuites id="" name="" tests="1" failures="1" skipped="0" errors="0" time="116.823975">
<testsuite name="cucumber-wrapper.spec.ts" timestamp="2025-06-11T05:51:27.869Z" hostname="edge" tests="1" failures="1" skipped="0" time="107.189" errors="0">
<testcase name="Self Service Featured Results › TC0101- Login and Check the Default Data Source Order" classname="cucumber-wrapper.spec.ts" time="107.189">
<properties>
<property name="feature" value="Self Service Featured Results">
</property>
<property name="tags" value="@combined, @self-service, @fr">
</property>
<property name="file" value="src\features\self-service-fr.feature">
</property>
</properties>
<failure message="cucumber-wrapper.spec.ts:34:11 TC0101- Login and Check the Default Data Source Order" type="FAILURE">
<![CDATA[  [edge] › cucumber-wrapper.spec.ts:34:11 › Self Service Featured Results › TC0101- Login and Check the Default Data Source Order 

    Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 101 elements:
        1) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('.MuiChip-label').first()
        2) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()
        3) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">terms</span> aka getByText('terms').nth(1)
        4) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka getByRole('row', { name: 'Edit Share 75 FeaturedResult' }).getByRole('button').nth(2)
        5) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()
        6) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')
        7) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">test</span> aka locator('span').filter({ hasText: /^test$/ }).nth(1)
        8) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">terms</span> aka getByText('terms').nth(2)
        9) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka getByRole('row', { name: 'Edit Share 74 FeaturedResult' }).getByRole('button').nth(2)
        10) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')
        ...

    Call log:
      - expect.toHaveText with timeout 30000ms
      - waiting for locator('.MuiChip-label')


       at ..\src\core\step-definition-registry.ts:154

      152 |       async (world: PlaywrightWorld, selector: string, text: string) => {
      153 |         const element = world.getLocator(selector);
    > 154 |         await expect(element).toHaveText(text);
          |                               ^
      155 |       },
      156 |       'Verify element has exact text (with "the" and "element")'
      157 |     );
        at Object.handler (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:154:31)
        at StepDefinitionRegistry.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:361:23)
        at PlaywrightCucumberRunner.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:196:31)
        at PlaywrightCucumberRunner.executeScenario (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:231:20)
        at C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:45:9

    attachment #1: Step 1 - Before: Given I navigate to URL "https://es-settings-staging.kpmg.com/" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--fb638ebdbe80a7756a8e63079085f5659f232f8d.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: Step 1 - After: Given I navigate to URL "https://es-settings-staging.kpmg.com/" (3403ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-3403ms--c301db7883c8ac54fad2dda0014def9f64fa9ef6.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: Step 2 - Before: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-c181d5006af6c480a37f3073811d405ef4d7d4bb.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: Step 2 - After: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000 (7391ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-7391ms--dbcfbedf8ddf18ee8081c74d9174986056ee01a9.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: Step 3 - Before: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com--e62e82a00bce764349f7a3421da54aa70d7c5945.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #6: Step 3 - After: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>" (76ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com-76ms--ccc4322a28c3a78507b4e014c4195af45a0cc374.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #7: Step 4 - Before: And I click on "getByRole('button', { name: 'Next' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-4---Before-And-I-click-on-getByRole-button-name-Next--5af3fa92aa35cd659ae9cfc51f250822ceddc396.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #8: Step 4 - After: And I click on "getByRole('button', { name: 'Next' })" (143ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-4---After-And-I-click-on-getByRole-button-name-Next-143ms--c007bb4fd799ce37174f0d986ee15b0e40044124.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #9: Step 5 - Before: Then I wait for element "getByRole('button', { name: 'Settings' })" to be visible with timeout 60000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-52592266963c7f3754edee9d81ffa66787e15c0e.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #10: Step 5 - After: Then I wait for element "getByRole('button', { name: 'Settings' })" to be visible with timeout 60000 (12797ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-5---After-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-12797ms--2ca1f66bf3e72b190e2f83211e4510c325579be3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #11: Step 6 - Before: And I wait for element "getByRole('button', { name: 'Create a request on behalf of' })" to be visible with timeout 30000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-6---Before-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-0957de0148fa2a9e786ceb186434aefa3c91c425.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #12: Step 6 - After: And I wait for element "getByRole('button', { name: 'Create a request on behalf of' })" to be visible with timeout 30000 (3167ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-6---After-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-3167ms--36c8b5f343da45056eb899a268a5b9ce58f51a6a.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #13: Step 7 - Before: And I wait for page title to contain "KPMG Find - Settings" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-7---Before-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings--2830df252472b9cc100607a4ff267e3799bb792b.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #14: Step 7 - After: And I wait for page title to contain "KPMG Find - Settings" (13ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-7---After-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings-13ms--440b71e03148f2c9736050313443160bfbc30b80.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #15: Step 8 - Before: When I click on "getByRole('button', { name: 'Create a request on behalf of' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-8---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--4eedcce63c9f33ebacac9db4e82df0999591233d.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #16: Step 8 - After: When I click on "getByRole('button', { name: 'Create a request on behalf of' })" (307ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-8---After-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of-307ms--de7613aeff7fcc77cea345305fc3df5a7a784d23.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #17: Step 9 - Before: Then I wait for element "getByRole('heading', { name: 'Create a requests on behalf' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-9---Before-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-bd4ca85717a9d378d4d720c3cd304ba8a6a57294.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #18: Step 9 - After: Then I wait for element "getByRole('heading', { name: 'Create a requests on behalf' })" to be visible with timeout 10000 (39ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-9---After-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-39ms--d1d4b443945eb98438c260cee200c7c6e0edf538.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #19: Step 10 - Before: Then the "locator('#displayName-label')" element should have exact text "Selected User" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-10---Before-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User--b08e829dda5494d267706751167100a153d21ec3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #20: Step 10 - After: Then the "locator('#displayName-label')" element should have exact text "Selected User" (65ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-10---After-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User-65ms--6c0beee0559e01d3164a0afceb2a5017fa85635e.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #21: Step 11 - Before: And element "getByRole('combobox', { name: 'FeaturedResult' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-11---Before-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-7bedcb2fba06419cdb4ad65ae27440ff22b67482.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #22: Step 11 - After: And element "getByRole('combobox', { name: 'FeaturedResult' })" should be visible (47ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-11---After-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-47ms--7d0dc856e97a788d11d8d4be8ed1b5bc1f3ecbf8.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #23: Step 12 - Before: And element "getByRole('button', { name: 'Cancel' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-12---Before-And-element-getByRole-button-name-Cancel-should-be-visible-957be121fb9008568d5fd748f3a224aebe5b8c2f.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #24: Step 12 - After: And element "getByRole('button', { name: 'Cancel' })" should be visible (45ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-12---After-And-element-getByRole-button-name-Cancel-should-be-visible-45ms--cff0ca0bc17760c97bc8f15dce927b9dfbdaa262.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #25: Step 13 - Before: When I click on "getByRole('combobox', { name: 'FeaturedResult' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-13---Before-When-I-click-on-getByRole-combobox-name-FeaturedResult--3906cf22d48323e8a2cf8ad26a942a20f3664778.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #26: Step 13 - After: When I click on "getByRole('combobox', { name: 'FeaturedResult' })" (172ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-13---After-When-I-click-on-getByRole-combobox-name-FeaturedResult-172ms--7a7fe71c67cc54b656ea3c6ac43856c436e18c59.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #27: Step 14 - Before: Then I wait for "locator('#menu-').getByText('FeaturedResult')" to be visible with timeout 30000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-14---Before-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-a70df5b58cc72fdaf34cf91f3563b23443712d47.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #28: Step 14 - After: Then I wait for "locator('#menu-').getByText('FeaturedResult')" to be visible with timeout 30000 (35ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-14---After-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-35ms--6f35a48d8f243ce03ea159355546dc6f50a9cb0d.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #29: Step 15 - Before: When I click on "locator('#menu-').getByText('FeaturedResult')" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-15---Before-When-I-click-on-locator-menu--getByText-FeaturedResult--94e86062326fdba1eedfafecd4ffe23d90507b6c.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #30: Step 15 - After: When I click on "locator('#menu-').getByText('FeaturedResult')" (114ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-15---After-When-I-click-on-locator-menu--getByText-FeaturedResult-114ms--15f40eb865014f05a4e97ea3a43874192eaba18c.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #31: Step 16 - Before: When I click on "getByRole('textbox', { name: 'Selected User' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-16---Before-When-I-click-on-getByRole-textbox-name-Selected-User--0a41f955234b107eefbd2e2e62a51b4c9fcc84e6.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #32: Step 16 - After: When I click on "getByRole('textbox', { name: 'Selected User' })" (210ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-16---After-When-I-click-on-getByRole-textbox-name-Selected-User-210ms--3cc67b571247b005772296153e96f77946c35435.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #33: Step 17 - Before: Then I wait for element "getByRole('heading', { name: 'Select user for the request' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-17---Before-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-a5ab2b811b518f8cc1256e6805e474ce5d1f7db0.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #34: Step 17 - After: Then I wait for element "getByRole('heading', { name: 'Select user for the request' })" to be visible with timeout 10000 (48ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-17---After-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-48ms--802bb6066aec4ef6d7823437ad37955e882d6b3c.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #35: Step 18 - Before: And element "getByRole('dialog', { name: 'Select user for the request' }).locator('svg')" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-18---Before-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-87102f9c9c1e0adef109f27e2255f6ca41060bd2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #36: Step 18 - After: And element "getByRole('dialog', { name: 'Select user for the request' }).locator('svg')" should be visible (44ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-18---After-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-44ms--4b45c44d0cf31d2242e9283a1228d096e70a4856.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #37: Step 19 - Before: And element "getByRole('searchbox', { name: 'description' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-19---Before-And-element-getByRole-searchbox-name-description-should-be-visible-5fc58f0060555ae6533dc2a6adca3161f9e50828.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #38: Step 19 - After: And element "getByRole('searchbox', { name: 'description' })" should be visible (41ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-19---After-And-element-getByRole-searchbox-name-description-should-be-visible-41ms--6270a7bcce1bd38b0084311bf14186854054b6c5.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #39: Step 20 - Before: When I fill "getByRole('searchbox', { name: 'description' })" with "<EMAIL>" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-20---Before-When-I-fill-getByRole-searchbox-name-description-with-jagannathak-kpmg-com--296d645610b9e778c7872b128c4e1c6a1a9ca400.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #40: Step 20 - After: When I fill "getByRole('searchbox', { name: 'description' })" with "<EMAIL>" (114ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-20---After-When-I-fill-getByRole-searchbox-name-description-with-jagannathak-kpmg-com-114ms--56ae2a46bcd48db7d37e94182e6e324866ec1b34.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #41: Step 21 - Before: Then I wait for element "getByLabel('K, Jagannatha')" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-21---Before-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-a667d3d5601147b2e0f9c177cf464aea28f37db2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #42: Step 21 - After: Then I wait for element "getByLabel('K, Jagannatha')" to be visible with timeout 10000 (2098ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-21---After-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-2098ms--774fd23deb7b32dd1d94fdc1fedd6e89367d13ac.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #43: Step 22 - Before: And I wait for element "getByLabel('<EMAIL>')" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-22---Before-And-I-wait-for-element-getByLabel-jagannathak-kpmg-com-to-be-visible-with-timeout-10000-1f2b2e4a680ee4a4f529f09fd70658cd7723b221.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #44: Step 22 - After: And I wait for element "getByLabel('<EMAIL>')" to be visible with timeout 10000 (40ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-22---After-And-I-wait-for-element-getByLabel-jagannathak-kpmg-com-to-be-visible-with-timeout-10000-40ms--6f1c4ba2191561abd9c7ccebe899bfb152696fca.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #45: Step 23 - Before: When I click on "getByLabel('K, Jagannatha')" (image/png) ────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-23---Before-When-I-click-on-getByLabel-K-Jagannatha--f661b8e8d60d06df5027af944e95ed28700c38e4.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #46: Step 23 - After: When I click on "getByLabel('K, Jagannatha')" (189ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-23---After-When-I-click-on-getByLabel-K-Jagannatha-189ms--0c6e1891dc25b4d26d413ef7b70e2dad2495819e.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #47: Step 24 - Before: Then I wait for element "getByRole('button', { name: 'Select' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-24---Before-Then-I-wait-for-element-getByRole-button-name-Select-to-be-visible-with-timeout-10000-bb5eb8e5f77fbcb0f91246899856baa37082b5f1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #48: Step 24 - After: Then I wait for element "getByRole('button', { name: 'Select' })" to be visible with timeout 10000 (48ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-24---After-Then-I-wait-for-element-getByRole-button-name-Select-to-be-visible-with-timeout-10000-48ms--a32d4742234e522f6b78dd690c5c9661c3d80978.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #49: Step 25 - Before: When I click on "getByRole('button', { name: 'Select' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-25---Before-When-I-click-on-getByRole-button-name-Select--784c074621942dc2c510460852500b9379eef943.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #50: Step 25 - After: When I click on "getByRole('button', { name: 'Select' })" (154ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-25---After-When-I-click-on-getByRole-button-name-Select-154ms--030a565e88e25eb1ea6138b70e48ffaf67eadc8f.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #51: Step 26 - Before: Then I wait for element "getByRole('button', { name: 'Create' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-26---Before-Then-I-wait-for-element-getByRole-button-name-Create-to-be-visible-with-timeout-10000-b763542a98a9e9a27c3bbec0d1f93bb240d98f18.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #52: Step 26 - After: Then I wait for element "getByRole('button', { name: 'Create' })" to be visible with timeout 10000 (47ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-26---After-Then-I-wait-for-element-getByRole-button-name-Create-to-be-visible-with-timeout-10000-47ms--39ba764a1889bba09a3995d9209d7a26f25ccd31.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #53: Step 27 - Before: When I click on "getByRole('button', { name: 'Create' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-27---Before-When-I-click-on-getByRole-button-name-Create--8e7a60b33ee67790229e72b208aadbc4897f118c.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #54: Step 27 - After: When I click on "getByRole('button', { name: 'Create' })" (274ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-27---After-When-I-click-on-getByRole-button-name-Create-274ms--f638d07db4935971a66beae72b711a0f5d596704.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #55: Step 28 - Before: Then I wait for element "getByText('New Featured Result for K,')" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-28---Before-Then-I-wait-for-element-getByText-New-Featured-Result-for-K-to-be-visible-with-timeout-10000-f6477c9e86f1705a8d160c93e8ce515b3d921c2f.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #56: Step 28 - After: Then I wait for element "getByText('New Featured Result for K,')" to be visible with timeout 10000 (44ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-28---After-Then-I-wait-for-element-getByText-New-Featured-Result-for-K-to-be-visible-with-timeout-10000-44ms--a0012631de2fb079e13fe26732633ed1185319e5.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #57: Step 29 - Before: When I fill "getByRole('textbox', { name: 'Title *' })" with "Test Title" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-29---Before-When-I-fill-getByRole-textbox-name-Title-with-Test-Title--82933b98f89d9cf88893dc264e823f8c7d2ce9b6.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #58: Step 29 - After: When I fill "getByRole('textbox', { name: 'Title *' })" with "Test Title" (262ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-29---After-When-I-fill-getByRole-textbox-name-Title-with-Test-Title-262ms--db9cf60d14043dae766f1c16ef0b9ff8f637d4ab.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #59: Step 30 - Before: And I click on "getByRole('textbox', { name: 'Description' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-30---Before-And-I-click-on-getByRole-textbox-name-Description--a67b68a467ab99acbd172c45b7b496d39517c125.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #60: Step 30 - After: And I click on "getByRole('textbox', { name: 'Description' })" (161ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-30---After-And-I-click-on-getByRole-textbox-name-Description-161ms--79ba7af4788bc9bc66bf08949db892e7b07a603f.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #61: Step 31 - Before: And I fill "getByRole('textbox', { name: 'Description' })" with "Test Description" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-31---Before-And-I-fill-getByRole-textbox-name-Description-with-Test-Description--3cd56fb99987ed2c19025e3616e82dce1ce4a012.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #62: Step 31 - After: And I fill "getByRole('textbox', { name: 'Description' })" with "Test Description" (233ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-31---After-And-I-fill-getByRole-textbox-name-Description-with-Test-Description-233ms--91d3e7d084b18b235bbeadce25e6d9fb69e6b616.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #63: Step 32 - Before: And I fill "getByRole('textbox', { name: 'Url', exact: true })" with "https://www.google.com" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-32---Before-And-I-fill-getByRole-textbox-name-Url-exact-true-with-https-www-google-com--7355a88b6740ec610b38bc906ef1e290fb7850d8.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #64: Step 32 - After: And I fill "getByRole('textbox', { name: 'Url', exact: true })" with "https://www.google.com" (287ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-32---After-And-I-fill-getByRole-textbox-name-Url-exact-true-with-https-www-google-com-287ms--486aae39feab3e6029bd4ab984ea60e34df4a880.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #65: Step 33 - Before: And I fill "getByRole('textbox', { name: 'Search terms (provide a comma' })" with "test, terms" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-33---Before-And-I-fill-getByRole-textbox-name-Search-terms-provide-a-comma-with-test-terms--906e0aa12cc436c993c49482b69dc63ef297ede6.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #66: Step 33 - After: And I fill "getByRole('textbox', { name: 'Search terms (provide a comma' })" with "test, terms" (245ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-33---After-And-I-fill-getByRole-textbox-name-Search-terms-provide-a-comma-with-test-terms-245ms--c9be61477a7cfdef1555a0b2c9ab47593f132f1b.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #67: Step 34 - Before: And I fill "getByRole('textbox', { name: 'Comments' })" with "Test Comments" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-34---Before-And-I-fill-getByRole-textbox-name-Comments-with-Test-Comments--933b0a81cb3cecc18c576997317b639b717447e3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #68: Step 34 - After: And I fill "getByRole('textbox', { name: 'Comments' })" with "Test Comments" (164ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-34---After-And-I-fill-getByRole-textbox-name-Comments-with-Test-Comments-164ms--a46037c757140a99aec49b80aab1a7f3cc89e3f1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #69: Step 35 - Before: Then element "getByRole('button', { name: 'Publish' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-35---Before-Then-element-getByRole-button-name-Publish-should-be-visible-4859addfb544867be65570cf12cf5040e8f19c0e.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #70: Step 35 - After: Then element "getByRole('button', { name: 'Publish' })" should be visible (50ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-35---After-Then-element-getByRole-button-name-Publish-should-be-visible-50ms--bc84e347bf3cd98a93bb1249e85c11d45cad322e.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #71: Step 36 - Before: And element "getByRole('button', { name: 'Create' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-36---Before-And-element-getByRole-button-name-Create-should-be-visible-60b14abc7c54b9557b7c1009cd3391299d90478c.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #72: Step 36 - After: And element "getByRole('button', { name: 'Create' })" should be visible (49ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-36---After-And-element-getByRole-button-name-Create-should-be-visible-49ms--5b75414da0bb9149eaf05b206259c0d24b904eab.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #73: Step 37 - Before: When I click on "getByRole('button', { name: 'Create' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-37---Before-When-I-click-on-getByRole-button-name-Create--0685233e6b4b5df3684db5313cc2d69cea7c3901.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #74: Step 37 - After: When I click on "getByRole('button', { name: 'Create' })" (188ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-37---After-When-I-click-on-getByRole-button-name-Create-188ms--e6b4bc7979739bd87dea1595d10b925579a24fd2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #75: Step 38 - Before: Then I wait for element "getByRole('heading', { name: 'Confirm creating request!' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-38---Before-Then-I-wait-for-element-getByRole-heading-name-Confirm-creating-request-to-be-visible-with-timeout-10000-f040403e64c6f578dff52635336dc503f23a964c.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #76: Step 38 - After: Then I wait for element "getByRole('heading', { name: 'Confirm creating request!' })" to be visible with timeout 10000 (55ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-38---After-Then-I-wait-for-element-getByRole-heading-name-Confirm-creating-request-to-be-visible-with-timeout-10000-55ms--2361ea02963cb7c36cc8b7f6cd3115ed1d2daa19.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #77: Step 39 - Before: And element "getByRole('button', { name: 'No' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-39---Before-And-element-getByRole-button-name-No-should-be-visible-f8292252ad93b237c47fcb89cf63e18438a1ef39.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #78: Step 39 - After: And element "getByRole('button', { name: 'No' })" should be visible (48ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-39---After-And-element-getByRole-button-name-No-should-be-visible-48ms--3d04cc3ecc7dfae7e05e26c5a6575b89e3757b7e.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #79: Step 40 - Before: And element "getByRole('button', { name: 'Yes' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-40---Before-And-element-getByRole-button-name-Yes-should-be-visible-b72c4e8d2b2d70108907846b86815ffa2402addc.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #80: Step 40 - After: And element "getByRole('button', { name: 'Yes' })" should be visible (46ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-40---After-And-element-getByRole-button-name-Yes-should-be-visible-46ms--ae100be68515a51e3ad8aaf9b5a6d1d500b3be8a.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #81: Step 41 - Before: When I click on "getByRole('button', { name: 'Yes' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-41---Before-When-I-click-on-getByRole-button-name-Yes--bdd7ea5003bc4df16177a92c5e8f7e7fdc5b374a.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #82: Step 41 - After: When I click on "getByRole('button', { name: 'Yes' })" (249ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-41---After-When-I-click-on-getByRole-button-name-Yes-249ms--5d42406a28fe7bb256248c03b730b0fee8395ed1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #83: Step 42 - Before: Then I wait for element "getByRole('heading', { name: 'Created request' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-42---Before-Then-I-wait-for-element-getByRole-heading-name-Created-request-to-be-visible-with-timeout-10000-7b2a99e87f7ed9e397530d5a037529e8bd2f459b.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #84: Step 42 - After: Then I wait for element "getByRole('heading', { name: 'Created request' })" to be visible with timeout 10000 (3271ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-42---After-Then-I-wait-for-element-getByRole-heading-name-Created-request-to-be-visible-with-timeout-10000-3271ms--dbcd180575d769b28edee8336855745b38f26371.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #85: Step 43 - Before: And element "getByText('Successfully created')" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-43---Before-And-element-getByText-Successfully-created-should-be-visible-4d65ace2813b8bb08dc075fb312c5639326d59dc.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #86: Step 43 - After: And element "getByText('Successfully created')" should be visible (61ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-43---After-And-element-getByText-Successfully-created-should-be-visible-61ms--66bded44ce792ed3e967bc0a7ef2ce111c979f0c.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #87: Step 44 - Before: And element "getByRole('button', { name: 'Close' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-44---Before-And-element-getByRole-button-name-Close-should-be-visible-43a7c2b34180deaef4cc694fd086a48cdeb73636.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #88: Step 44 - After: And element "getByRole('button', { name: 'Close' })" should be visible (74ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-44---After-And-element-getByRole-button-name-Close-should-be-visible-74ms--03c3bae3c57115c34e14dcafd4970e658ea25c0e.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #89: Step 45 - Before: And I click on "getByRole('button', { name: 'Close' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-45---Before-And-I-click-on-getByRole-button-name-Close--18e335649770501dcb5d367a6475f45f7652944f.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #90: Step 45 - After: And I click on "getByRole('button', { name: 'Close' })" (142ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-45---After-And-I-click-on-getByRole-button-name-Close-142ms--ef4a32e8361edfed98a3aa0ce7b7580a78612bbc.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #91: Step 46 - Before: Then the "locator('.MuiChip-label').first()" element should have exact text "FeaturedResult" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-46---Before-Then-the-locator-MuiChip-label-first-element-should-have-exact-text-FeaturedResult--deea9332765fc95ab58fdc37bf0a4fa92337f12b.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #92: Failure Screenshot (image/png) ─────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Failure-Screenshot-68a893639dca7abc0f3f6e6663cdaf94bc586cc7.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #93: screenshot (image/png) ─────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #94: video (video/webm) ─────────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\error-context.md

    attachment #96: trace (application/zip) ────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\trace.zip
    Usage:

        npx playwright show-trace test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🎬 Starting scenario: TC0101- Login and Check the Default Data Source Order
📁 Feature: Self Service Featured Results
🏷️ Tags: @combined, @self-service, @fr
📋 Step 1/48: Given I navigate to URL "https://es-settings-staging.kpmg.com/"
✅ Step completed in 3403ms
📋 Step 2/48: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000
✅ Step completed in 7391ms
📋 Step 3/48: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>"
✅ Step completed in 76ms
📋 Step 4/48: And I click on "getByRole('button', { name: 'Next' })"
✅ Step completed in 143ms
📋 Step 5/48: Then I wait for element "getByRole('button', { name: 'Settings' })" to be visible with timeout 60000
✅ Step completed in 12797ms
📋 Step 6/48: And I wait for element "getByRole('button', { name: 'Create a request on behalf of' })" to be visible with timeout 30000
✅ Step completed in 3167ms
📋 Step 7/48: And I wait for page title to contain "KPMG Find - Settings"
✅ Step completed in 13ms
📋 Step 8/48: When I click on "getByRole('button', { name: 'Create a request on behalf of' })"
✅ Step completed in 307ms
📋 Step 9/48: Then I wait for element "getByRole('heading', { name: 'Create a requests on behalf' })" to be visible with timeout 10000
✅ Step completed in 39ms
📋 Step 10/48: Then the "locator('#displayName-label')" element should have exact text "Selected User"
✅ Step completed in 65ms
📋 Step 11/48: And element "getByRole('combobox', { name: 'FeaturedResult' })" should be visible
✅ Step completed in 47ms
📋 Step 12/48: And element "getByRole('button', { name: 'Cancel' })" should be visible
✅ Step completed in 45ms
📋 Step 13/48: When I click on "getByRole('combobox', { name: 'FeaturedResult' })"
✅ Step completed in 172ms
📋 Step 14/48: Then I wait for "locator('#menu-').getByText('FeaturedResult')" to be visible with timeout 30000
✅ Step completed in 35ms
📋 Step 15/48: When I click on "locator('#menu-').getByText('FeaturedResult')"
✅ Step completed in 114ms
📋 Step 16/48: When I click on "getByRole('textbox', { name: 'Selected User' })"
✅ Step completed in 210ms
📋 Step 17/48: Then I wait for element "getByRole('heading', { name: 'Select user for the request' })" to be visible with timeout 10000
✅ Step completed in 48ms
📋 Step 18/48: And element "getByRole('dialog', { name: 'Select user for the request' }).locator('svg')" should be visible
✅ Step completed in 44ms
📋 Step 19/48: And element "getByRole('searchbox', { name: 'description' })" should be visible
✅ Step completed in 41ms
📋 Step 20/48: When I fill "getByRole('searchbox', { name: 'description' })" with "<EMAIL>"
✅ Step completed in 114ms
📋 Step 21/48: Then I wait for element "getByLabel('K, Jagannatha')" to be visible with timeout 10000
✅ Step completed in 2098ms
📋 Step 22/48: And I wait for element "getByLabel('<EMAIL>')" to be visible with timeout 10000
✅ Step completed in 40ms
📋 Step 23/48: When I click on "getByLabel('K, Jagannatha')"
✅ Step completed in 189ms
📋 Step 24/48: Then I wait for element "getByRole('button', { name: 'Select' })" to be visible with timeout 10000
✅ Step completed in 48ms
📋 Step 25/48: When I click on "getByRole('button', { name: 'Select' })"
✅ Step completed in 154ms
📋 Step 26/48: Then I wait for element "getByRole('button', { name: 'Create' })" to be visible with timeout 10000
✅ Step completed in 47ms
📋 Step 27/48: When I click on "getByRole('button', { name: 'Create' })"
✅ Step completed in 274ms
📋 Step 28/48: Then I wait for element "getByText('New Featured Result for K,')" to be visible with timeout 10000
✅ Step completed in 44ms
📋 Step 29/48: When I fill "getByRole('textbox', { name: 'Title *' })" with "Test Title"
✅ Step completed in 262ms
📋 Step 30/48: And I click on "getByRole('textbox', { name: 'Description' })"
✅ Step completed in 161ms
📋 Step 31/48: And I fill "getByRole('textbox', { name: 'Description' })" with "Test Description"
✅ Step completed in 233ms
📋 Step 32/48: And I fill "getByRole('textbox', { name: 'Url', exact: true })" with "https://www.google.com"
✅ Step completed in 287ms
📋 Step 33/48: And I fill "getByRole('textbox', { name: 'Search terms (provide a comma' })" with "test, terms"
✅ Step completed in 245ms
📋 Step 34/48: And I fill "getByRole('textbox', { name: 'Comments' })" with "Test Comments"
✅ Step completed in 164ms
📋 Step 35/48: Then element "getByRole('button', { name: 'Publish' })" should be visible
✅ Step completed in 50ms
📋 Step 36/48: And element "getByRole('button', { name: 'Create' })" should be visible
✅ Step completed in 49ms
📋 Step 37/48: When I click on "getByRole('button', { name: 'Create' })"
✅ Step completed in 188ms
📋 Step 38/48: Then I wait for element "getByRole('heading', { name: 'Confirm creating request!' })" to be visible with timeout 10000
✅ Step completed in 55ms
📋 Step 39/48: And element "getByRole('button', { name: 'No' })" should be visible
✅ Step completed in 48ms
📋 Step 40/48: And element "getByRole('button', { name: 'Yes' })" should be visible
✅ Step completed in 46ms
📋 Step 41/48: When I click on "getByRole('button', { name: 'Yes' })"
✅ Step completed in 249ms
📋 Step 42/48: Then I wait for element "getByRole('heading', { name: 'Created request' })" to be visible with timeout 10000
✅ Step completed in 3271ms
📋 Step 43/48: And element "getByText('Successfully created')" should be visible
✅ Step completed in 61ms
📋 Step 44/48: And element "getByRole('button', { name: 'Close' })" should be visible
✅ Step completed in 74ms
📋 Step 45/48: And I click on "getByRole('button', { name: 'Close' })"
✅ Step completed in 142ms
📋 Step 46/48: Then the "locator('.MuiChip-label').first()" element should have exact text "FeaturedResult"
[[31merror[39m]: ❌ Step failed: Then the "locator('.MuiChip-label').first()" element should have exact text "FeaturedResult"

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--fb638ebdbe80a7756a8e63079085f5659f232f8d.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-3403ms--c301db7883c8ac54fad2dda0014def9f64fa9ef6.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-c181d5006af6c480a37f3073811d405ef4d7d4bb.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-7391ms--dbcfbedf8ddf18ee8081c74d9174986056ee01a9.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com--e62e82a00bce764349f7a3421da54aa70d7c5945.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com-76ms--ccc4322a28c3a78507b4e014c4195af45a0cc374.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-4---Before-And-I-click-on-getByRole-button-name-Next--5af3fa92aa35cd659ae9cfc51f250822ceddc396.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-4---After-And-I-click-on-getByRole-button-name-Next-143ms--c007bb4fd799ce37174f0d986ee15b0e40044124.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-52592266963c7f3754edee9d81ffa66787e15c0e.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-5---After-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-12797ms--2ca1f66bf3e72b190e2f83211e4510c325579be3.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-6---Before-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-0957de0148fa2a9e786ceb186434aefa3c91c425.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-6---After-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-3167ms--36c8b5f343da45056eb899a268a5b9ce58f51a6a.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-7---Before-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings--2830df252472b9cc100607a4ff267e3799bb792b.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-7---After-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings-13ms--440b71e03148f2c9736050313443160bfbc30b80.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-8---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--4eedcce63c9f33ebacac9db4e82df0999591233d.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-8---After-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of-307ms--de7613aeff7fcc77cea345305fc3df5a7a784d23.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-9---Before-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-bd4ca85717a9d378d4d720c3cd304ba8a6a57294.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-9---After-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-39ms--d1d4b443945eb98438c260cee200c7c6e0edf538.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-10---Before-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User--b08e829dda5494d267706751167100a153d21ec3.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-10---After-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User-65ms--6c0beee0559e01d3164a0afceb2a5017fa85635e.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-11---Before-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-7bedcb2fba06419cdb4ad65ae27440ff22b67482.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-11---After-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-47ms--7d0dc856e97a788d11d8d4be8ed1b5bc1f3ecbf8.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-12---Before-And-element-getByRole-button-name-Cancel-should-be-visible-957be121fb9008568d5fd748f3a224aebe5b8c2f.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-12---After-And-element-getByRole-button-name-Cancel-should-be-visible-45ms--cff0ca0bc17760c97bc8f15dce927b9dfbdaa262.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-13---Before-When-I-click-on-getByRole-combobox-name-FeaturedResult--3906cf22d48323e8a2cf8ad26a942a20f3664778.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-13---After-When-I-click-on-getByRole-combobox-name-FeaturedResult-172ms--7a7fe71c67cc54b656ea3c6ac43856c436e18c59.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-14---Before-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-a70df5b58cc72fdaf34cf91f3563b23443712d47.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-14---After-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-35ms--6f35a48d8f243ce03ea159355546dc6f50a9cb0d.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-15---Before-When-I-click-on-locator-menu--getByText-FeaturedResult--94e86062326fdba1eedfafecd4ffe23d90507b6c.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-15---After-When-I-click-on-locator-menu--getByText-FeaturedResult-114ms--15f40eb865014f05a4e97ea3a43874192eaba18c.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-16---Before-When-I-click-on-getByRole-textbox-name-Selected-User--0a41f955234b107eefbd2e2e62a51b4c9fcc84e6.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-16---After-When-I-click-on-getByRole-textbox-name-Selected-User-210ms--3cc67b571247b005772296153e96f77946c35435.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-17---Before-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-a5ab2b811b518f8cc1256e6805e474ce5d1f7db0.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-17---After-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-48ms--802bb6066aec4ef6d7823437ad37955e882d6b3c.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-18---Before-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-87102f9c9c1e0adef109f27e2255f6ca41060bd2.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-18---After-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-44ms--4b45c44d0cf31d2242e9283a1228d096e70a4856.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-19---Before-And-element-getByRole-searchbox-name-description-should-be-visible-5fc58f0060555ae6533dc2a6adca3161f9e50828.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-19---After-And-element-getByRole-searchbox-name-description-should-be-visible-41ms--6270a7bcce1bd38b0084311bf14186854054b6c5.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-20---Before-When-I-fill-getByRole-searchbox-name-description-with-jagannathak-kpmg-com--296d645610b9e778c7872b128c4e1c6a1a9ca400.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-20---After-When-I-fill-getByRole-searchbox-name-description-with-jagannathak-kpmg-com-114ms--56ae2a46bcd48db7d37e94182e6e324866ec1b34.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-21---Before-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-a667d3d5601147b2e0f9c177cf464aea28f37db2.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-21---After-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-2098ms--774fd23deb7b32dd1d94fdc1fedd6e89367d13ac.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-22---Before-And-I-wait-for-element-getByLabel-jagannathak-kpmg-com-to-be-visible-with-timeout-10000-1f2b2e4a680ee4a4f529f09fd70658cd7723b221.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-22---After-And-I-wait-for-element-getByLabel-jagannathak-kpmg-com-to-be-visible-with-timeout-10000-40ms--6f1c4ba2191561abd9c7ccebe899bfb152696fca.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-23---Before-When-I-click-on-getByLabel-K-Jagannatha--f661b8e8d60d06df5027af944e95ed28700c38e4.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-23---After-When-I-click-on-getByLabel-K-Jagannatha-189ms--0c6e1891dc25b4d26d413ef7b70e2dad2495819e.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-24---Before-Then-I-wait-for-element-getByRole-button-name-Select-to-be-visible-with-timeout-10000-bb5eb8e5f77fbcb0f91246899856baa37082b5f1.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-24---After-Then-I-wait-for-element-getByRole-button-name-Select-to-be-visible-with-timeout-10000-48ms--a32d4742234e522f6b78dd690c5c9661c3d80978.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-25---Before-When-I-click-on-getByRole-button-name-Select--784c074621942dc2c510460852500b9379eef943.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-25---After-When-I-click-on-getByRole-button-name-Select-154ms--030a565e88e25eb1ea6138b70e48ffaf67eadc8f.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-26---Before-Then-I-wait-for-element-getByRole-button-name-Create-to-be-visible-with-timeout-10000-b763542a98a9e9a27c3bbec0d1f93bb240d98f18.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-26---After-Then-I-wait-for-element-getByRole-button-name-Create-to-be-visible-with-timeout-10000-47ms--39ba764a1889bba09a3995d9209d7a26f25ccd31.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-27---Before-When-I-click-on-getByRole-button-name-Create--8e7a60b33ee67790229e72b208aadbc4897f118c.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-27---After-When-I-click-on-getByRole-button-name-Create-274ms--f638d07db4935971a66beae72b711a0f5d596704.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-28---Before-Then-I-wait-for-element-getByText-New-Featured-Result-for-K-to-be-visible-with-timeout-10000-f6477c9e86f1705a8d160c93e8ce515b3d921c2f.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-28---After-Then-I-wait-for-element-getByText-New-Featured-Result-for-K-to-be-visible-with-timeout-10000-44ms--a0012631de2fb079e13fe26732633ed1185319e5.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-29---Before-When-I-fill-getByRole-textbox-name-Title-with-Test-Title--82933b98f89d9cf88893dc264e823f8c7d2ce9b6.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-29---After-When-I-fill-getByRole-textbox-name-Title-with-Test-Title-262ms--db9cf60d14043dae766f1c16ef0b9ff8f637d4ab.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-30---Before-And-I-click-on-getByRole-textbox-name-Description--a67b68a467ab99acbd172c45b7b496d39517c125.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-30---After-And-I-click-on-getByRole-textbox-name-Description-161ms--79ba7af4788bc9bc66bf08949db892e7b07a603f.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-31---Before-And-I-fill-getByRole-textbox-name-Description-with-Test-Description--3cd56fb99987ed2c19025e3616e82dce1ce4a012.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-31---After-And-I-fill-getByRole-textbox-name-Description-with-Test-Description-233ms--91d3e7d084b18b235bbeadce25e6d9fb69e6b616.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-32---Before-And-I-fill-getByRole-textbox-name-Url-exact-true-with-https-www-google-com--7355a88b6740ec610b38bc906ef1e290fb7850d8.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-32---After-And-I-fill-getByRole-textbox-name-Url-exact-true-with-https-www-google-com-287ms--486aae39feab3e6029bd4ab984ea60e34df4a880.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-33---Before-And-I-fill-getByRole-textbox-name-Search-terms-provide-a-comma-with-test-terms--906e0aa12cc436c993c49482b69dc63ef297ede6.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-33---After-And-I-fill-getByRole-textbox-name-Search-terms-provide-a-comma-with-test-terms-245ms--c9be61477a7cfdef1555a0b2c9ab47593f132f1b.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-34---Before-And-I-fill-getByRole-textbox-name-Comments-with-Test-Comments--933b0a81cb3cecc18c576997317b639b717447e3.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-34---After-And-I-fill-getByRole-textbox-name-Comments-with-Test-Comments-164ms--a46037c757140a99aec49b80aab1a7f3cc89e3f1.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-35---Before-Then-element-getByRole-button-name-Publish-should-be-visible-4859addfb544867be65570cf12cf5040e8f19c0e.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-35---After-Then-element-getByRole-button-name-Publish-should-be-visible-50ms--bc84e347bf3cd98a93bb1249e85c11d45cad322e.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-36---Before-And-element-getByRole-button-name-Create-should-be-visible-60b14abc7c54b9557b7c1009cd3391299d90478c.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-36---After-And-element-getByRole-button-name-Create-should-be-visible-49ms--5b75414da0bb9149eaf05b206259c0d24b904eab.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-37---Before-When-I-click-on-getByRole-button-name-Create--0685233e6b4b5df3684db5313cc2d69cea7c3901.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-37---After-When-I-click-on-getByRole-button-name-Create-188ms--e6b4bc7979739bd87dea1595d10b925579a24fd2.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-38---Before-Then-I-wait-for-element-getByRole-heading-name-Confirm-creating-request-to-be-visible-with-timeout-10000-f040403e64c6f578dff52635336dc503f23a964c.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-38---After-Then-I-wait-for-element-getByRole-heading-name-Confirm-creating-request-to-be-visible-with-timeout-10000-55ms--2361ea02963cb7c36cc8b7f6cd3115ed1d2daa19.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-39---Before-And-element-getByRole-button-name-No-should-be-visible-f8292252ad93b237c47fcb89cf63e18438a1ef39.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-39---After-And-element-getByRole-button-name-No-should-be-visible-48ms--3d04cc3ecc7dfae7e05e26c5a6575b89e3757b7e.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-40---Before-And-element-getByRole-button-name-Yes-should-be-visible-b72c4e8d2b2d70108907846b86815ffa2402addc.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-40---After-And-element-getByRole-button-name-Yes-should-be-visible-46ms--ae100be68515a51e3ad8aaf9b5a6d1d500b3be8a.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-41---Before-When-I-click-on-getByRole-button-name-Yes--bdd7ea5003bc4df16177a92c5e8f7e7fdc5b374a.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-41---After-When-I-click-on-getByRole-button-name-Yes-249ms--5d42406a28fe7bb256248c03b730b0fee8395ed1.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-42---Before-Then-I-wait-for-element-getByRole-heading-name-Created-request-to-be-visible-with-timeout-10000-7b2a99e87f7ed9e397530d5a037529e8bd2f459b.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-42---After-Then-I-wait-for-element-getByRole-heading-name-Created-request-to-be-visible-with-timeout-10000-3271ms--dbcd180575d769b28edee8336855745b38f26371.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-43---Before-And-element-getByText-Successfully-created-should-be-visible-4d65ace2813b8bb08dc075fb312c5639326d59dc.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-43---After-And-element-getByText-Successfully-created-should-be-visible-61ms--66bded44ce792ed3e967bc0a7ef2ce111c979f0c.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-44---Before-And-element-getByRole-button-name-Close-should-be-visible-43a7c2b34180deaef4cc694fd086a48cdeb73636.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-44---After-And-element-getByRole-button-name-Close-should-be-visible-74ms--03c3bae3c57115c34e14dcafd4970e658ea25c0e.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-45---Before-And-I-click-on-getByRole-button-name-Close--18e335649770501dcb5d367a6475f45f7652944f.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-45---After-And-I-click-on-getByRole-button-name-Close-142ms--ef4a32e8361edfed98a3aa0ce7b7580a78612bbc.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Step-46---Before-Then-the-locator-MuiChip-label-first-element-should-have-exact-text-FeaturedResult--deea9332765fc95ab58fdc37bf0a4fa92337f12b.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\attachments\Failure-Screenshot-68a893639dca7abc0f3f6e6663cdaf94bc586cc7.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\test-failed-1.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\video.webm]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\error-context.md]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[❌ Scenario failed: TC0101- Login and Check the Default Data Source Order expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 101 elements:
    1) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('.MuiChip-label').first()
    2) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()
    3) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">terms</span> aka getByText('terms').nth(1)
    4) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka getByRole('row', { name: 'Edit Share 75 FeaturedResult' }).getByRole('button').nth(2)
    5) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()
    6) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')
    7) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">test</span> aka locator('span').filter({ hasText: /^test$/ }).nth(1)
    8) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">terms</span> aka getByText('terms').nth(2)
    9) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka getByRole('row', { name: 'Edit Share 74 FeaturedResult' }).getByRole('button').nth(2)
    10) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')
    ...

Call log:
[2m  - expect.toHaveText with timeout 30000ms[22m
[2m  - waiting for locator('.MuiChip-label')[22m

    at Object.handler [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\step-definition-registry.ts:154:31[90m)[39m
    at StepDefinitionRegistry.executeStep [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\step-definition-registry.ts:361:23[90m)[39m
    at PlaywrightCucumberRunner.executeStep [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-cucumber-runner.ts:196:31[90m)[39m
    at PlaywrightCucumberRunner.executeScenario [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-cucumber-runner.ts:231:20[90m)[39m
    at [90mC:\workspace\Playwright_Automation_Framework\[39mtests\cucumber-wrapper.spec.ts:45:9 {
  [[32mSymbol(step)[39m]: {
    stepId: [32m'expect@236'[39m,
    category: [32m'expect'[39m,
    title: [32m'expect.toHaveText'[39m,
    params: { expected: [32m'FeaturedResult'[39m },
    infectParentStepsWithError: [90mundefined[39m,
    boxedStack: [90mundefined[39m,
    location: {
      file: [32m'C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts'[39m,
      line: [33m154[39m,
      column: [33m31[39m,
      function: [32m'Object.handler'[39m
    },
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'expect@236'[39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1749621186326[39m,
    error: {
      message: [32m"Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 101 elements:\n"[39m +
        [32m`    1) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('.MuiChip-label').first()\n`[39m +
        [32m`    2) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()\n`[39m +
        [32m`    3) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">terms</span> aka getByText('terms').nth(1)\n`[39m +
        [32m`    4) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka getByRole('row', { name: 'Edit Share 75 FeaturedResult' }).getByRole('button').nth(2)\n`[39m +
        [32m`    5) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()\n`[39m +
        [32m`    6) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n`[39m +
        [32m`    7) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">test</span> aka locator('span').filter({ hasText: /^test$/ }).nth(1)\n`[39m +
        [32m`    8) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">terms</span> aka getByText('terms').nth(2)\n`[39m +
        [32m`    9) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka getByRole('row', { name: 'Edit Share 74 FeaturedResult' }).getByRole('button').nth(2)\n`[39m +
        [32m`    10) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')\n`[39m +
        [32m'    ...\n'[39m +
        [32m'\n'[39m +
        [32m'Call log:\n'[39m +
        [32m'\x1B[2m  - expect.toHaveText with timeout 30000ms\x1B[22m\n'[39m +
        [32m"\x1B[2m  - waiting for locator('.MuiChip-label')\x1B[22m\n"[39m,
      stack: [32m"Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 101 elements:\n"[39m +
        [32m`    1) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('.MuiChip-label').first()\n`[39m +
        [32m`    2) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()\n`[39m +
        [32m`    3) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">terms</span> aka getByText('terms').nth(1)\n`[39m +
        [32m`    4) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka getByRole('row', { name: 'Edit Share 75 FeaturedResult' }).getByRole('button').nth(2)\n`[39m +
        [32m`    5) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()\n`[39m +
        [32m`    6) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n`[39m +
        [32m`    7) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">test</span> aka locator('span').filter({ hasText: /^test$/ }).nth(1)\n`[39m +
        [32m`    8) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">terms</span> aka getByText('terms').nth(2)\n`[39m +
        [32m`    9) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka getByRole('row', { name: 'Edit Share 74 FeaturedResult' }).getByRole('button').nth(2)\n`[39m +
        [32m`    10) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')\n`[39m +
        [32m'    ...\n'[39m +
        [32m'\n'[39m +
        [32m'Call log:\n'[39m +
        [32m'\x1B[2m  - expect.toHaveText with timeout 30000ms\x1B[22m\n'[39m +
        [32m"\x1B[2m  - waiting for locator('.MuiChip-label')\x1B[22m\n"[39m +
        [32m'\n'[39m +
        [32m'    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:154:31)\n'[39m +
        [32m'    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:361:23)\n'[39m +
        [32m'    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:196:31)\n'[39m +
        [32m'    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:231:20)\n'[39m +
        [32m'    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9'[39m,
      cause: [90mundefined[39m
    }
  }
}
]]>
</system-err>
</testcase>
</testsuite>
</testsuites>