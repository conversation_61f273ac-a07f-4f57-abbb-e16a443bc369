<testsuites id="" name="" tests="1" failures="1" skipped="0" errors="0" time="63.64856400000001">
<testsuite name="cucumber-wrapper.spec.ts" timestamp="2025-06-11T07:42:40.599Z" hostname="edge" tests="1" failures="1" skipped="0" time="57.524" errors="0">
<testcase name="Self Service Featured Results › TC0101- Create Featured Result with Test Data" classname="cucumber-wrapper.spec.ts" time="57.524">
<properties>
<property name="feature" value="Self Service Featured Results">
</property>
<property name="tags" value="@combined, @self-service, @fr, @smoke, @create-featured-result">
</property>
<property name="file" value="src\features\self-service-fr.feature">
</property>
</properties>
<failure message="cucumber-wrapper.spec.ts:34:11 TC0101- Create Featured Result with Test Data" type="FAILURE">
<![CDATA[  [edge] › cucumber-wrapper.spec.ts:34:11 › Self Service Featured Results › TC0101- Create Featured Result with Test Data 

    TimeoutError: locator.waitFor: Timeout 10000ms exceeded.
    Call log:
      - waiting for getByLabel('K, Jagannatha') to be visible


       at ..\src\core\playwright-world.ts:697

      695 |     this.logger.info(`⏳ Waiting for element: ${selector}`);
      696 |     const element = this.parseLocator(selector);
    > 697 |     await element.waitFor({ state: 'visible', timeout });
          |                   ^
      698 |
      699 |     // Action highlight after element becomes visible (red)
      700 |     if (EnvConfig.HIGHLIGHT_ELEMENTS) {
        at PlaywrightWorld.waitForElement (C:\workspace\Playwright_Automation_Framework\src\core\playwright-world.ts:697:19)
        at Object.handler (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:189:21)
        at StepDefinitionRegistry.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:361:23)
        at PlaywrightCucumberRunner.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:225:31)
        at PlaywrightCucumberRunner.executeScenario (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:303:20)
        at C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:45:9

    attachment #1: Background Step 1 - Before: Given I navigate to URL "https://es-settings-staging.kpmg.com/" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--1ed918cd9d00c8331e9570e5c951a15860c8b78b.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: Background Step 1 - After: Given I navigate to URL "https://es-settings-staging.kpmg.com/" (3879ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-3879ms--2d47c1632dd701d7641365727d5b000bb40c8933.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: Background Step 2 - Before: And I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-2---Before-And-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-e645b6ea45ab0e08ccb6307d976cc30115fb60c4.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: Background Step 2 - After: And I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000 (6251ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-2---After-And-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-6251ms--685f233c9bdd62d5ec28c517376fb49ab4e73f02.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: Background Step 3 - Before: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com--e508fdc3e5316574a1fe8b5419e442b454b07df5.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #6: Background Step 3 - After: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>" (88ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com-88ms--d331ee7be14eb349aac3bafb4d6905ef66ccb352.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #7: Background Step 4 - Before: And I click on "getByRole('button', { name: 'Next' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-4---Before-And-I-click-on-getByRole-button-name-Next--4524f1a2802fc3df67503ada045d1834a9d74352.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #8: Background Step 4 - After: And I click on "getByRole('button', { name: 'Next' })" (166ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-4---After-And-I-click-on-getByRole-button-name-Next-166ms--cda1e7babd7bbfef4b1dec9261ebe9015f56b42b.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #9: Background Step 5 - Before: Then I wait for element "getByRole('button', { name: 'Settings' })" to be visible with timeout 60000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-47b2be8c572c1f9718a04b0972e27d58ed07b4f9.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #10: Background Step 5 - After: Then I wait for element "getByRole('button', { name: 'Settings' })" to be visible with timeout 60000 (12962ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-5---After-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-12962ms--645cba9334fa0369926e83cf7e866eb904f64d7a.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #11: Background Step 6 - Before: And I wait for element "getByRole('button', { name: 'Create a request on behalf of' })" to be visible with timeout 30000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-6---Before-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-ad00efba73dcfb2cd51bbfb06cffd1959d8c339f.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #12: Background Step 6 - After: And I wait for element "getByRole('button', { name: 'Create a request on behalf of' })" to be visible with timeout 30000 (2837ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-6---After-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-2837ms--5f00919785b8035d846136de2ff4db89b91f6af7.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #13: Background Step 7 - Before: And I wait for page title to contain "KPMG Find - Settings" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-7---Before-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings--0239d938f4c55037a15df7a26cc1e73a3ec4b4c3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #14: Background Step 7 - After: And I wait for page title to contain "KPMG Find - Settings" (8ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-7---After-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings-8ms--8c92114b20cdcbb0e96f36bf0395a176ee8768bd.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #15: Step 1 - Before: When I click on "getByRole('button', { name: 'Create a request on behalf of' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-1---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--f2828ab2648124860832dd644cd1767783c0dec8.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #16: Step 1 - After: When I click on "getByRole('button', { name: 'Create a request on behalf of' })" (257ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-1---After-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of-257ms--06477d74cbaefe5df5a87efdabf42c5fd1c0a10b.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #17: Step 2 - Before: Then I wait for element "getByRole('heading', { name: 'Create a requests on behalf' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-2---Before-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-b24003d3b0e9cf8cc2a5a972d4d6330dc6080ab8.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #18: Step 2 - After: Then I wait for element "getByRole('heading', { name: 'Create a requests on behalf' })" to be visible with timeout 10000 (80ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-2---After-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-80ms--07a41814d029951c1f7881c704d6771ca186ca84.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #19: Step 3 - Before: And the "locator('#displayName-label')" element should have exact text "Selected User" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-3---Before-And-the-locator-displayName-label-element-should-have-exact-text-Selected-User--bf340e2c3b83032fcaaf4cadf9e04aab0cb79cd3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #20: Step 3 - After: And the "locator('#displayName-label')" element should have exact text "Selected User" (39ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-3---After-And-the-locator-displayName-label-element-should-have-exact-text-Selected-User-39ms--bf86f760271704dbce04fee33364385aa7c268a5.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #21: Step 4 - Before: And element "getByRole('combobox', { name: 'FeaturedResult' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-4---Before-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-a35c12eb1651736b8d9d060685cec96d3c92c1a0.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #22: Step 4 - After: And element "getByRole('combobox', { name: 'FeaturedResult' })" should be visible (60ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-4---After-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-60ms--bd8c227cc60bd97bf9a74c08b270dba264b39ede.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #23: Step 5 - Before: And element "getByRole('button', { name: 'Cancel' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-5---Before-And-element-getByRole-button-name-Cancel-should-be-visible-069f0384df40cfa2238de3769ebe2e8817c87121.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #24: Step 5 - After: And element "getByRole('button', { name: 'Cancel' })" should be visible (85ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-5---After-And-element-getByRole-button-name-Cancel-should-be-visible-85ms--a694e77e54c65df095ce2ac9515dd7c6bc5f5d62.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #25: Step 6 - Before: When I click on "getByRole('combobox', { name: 'FeaturedResult' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-6---Before-When-I-click-on-getByRole-combobox-name-FeaturedResult--59c57a1cbf3372a032792b4e366f7392b6dd9811.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #26: Step 6 - After: When I click on "getByRole('combobox', { name: 'FeaturedResult' })" (245ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-6---After-When-I-click-on-getByRole-combobox-name-FeaturedResult-245ms--ce435f584089f50105395564653eed96b865e0af.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #27: Step 7 - Before: And I wait for "locator('#menu-').getByText('FeaturedResult')" to be visible with timeout 30000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-7---Before-And-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-9acdcc0a3df57547bff1f45788a6e47ae7b94eb6.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #28: Step 7 - After: And I wait for "locator('#menu-').getByText('FeaturedResult')" to be visible with timeout 30000 (50ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-7---After-And-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-50ms--078df80df95e60b956aaf2d4a1d6cfae271b3abf.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #29: Step 8 - Before: And I click on "locator('#menu-').getByText('FeaturedResult')" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-8---Before-And-I-click-on-locator-menu--getByText-FeaturedResult--8984c6eb37da62b6fe24f8020e8a86929d852076.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #30: Step 8 - After: And I click on "locator('#menu-').getByText('FeaturedResult')" (157ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-8---After-And-I-click-on-locator-menu--getByText-FeaturedResult-157ms--14439f5f6e28200efadce6c12cd11b144b577aab.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #31: Step 9 - Before: And I click on "getByRole('textbox', { name: 'Selected User' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-9---Before-And-I-click-on-getByRole-textbox-name-Selected-User--758761d760b7f8ed8ad12402c0ff4319afb9e75a.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #32: Step 9 - After: And I click on "getByRole('textbox', { name: 'Selected User' })" (167ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-9---After-And-I-click-on-getByRole-textbox-name-Selected-User-167ms--5952c13f9f36c7f25a81a7de81f8e4593227d6b3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #33: Step 10 - Before: Then I wait for element "getByRole('heading', { name: 'Select user for the request' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-10---Before-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-ee40225c8e6bcf18620ec6a629b7fda755c098cd.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #34: Step 10 - After: Then I wait for element "getByRole('heading', { name: 'Select user for the request' })" to be visible with timeout 10000 (53ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-10---After-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-53ms--c9c577a9a3af63534dc537a2dcc83d5450234823.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #35: Step 11 - Before: And element "getByRole('dialog', { name: 'Select user for the request' }).locator('svg')" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-11---Before-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-c8bfbd64a2fe0559a3affcc099f5c73717cb5b4f.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #36: Step 11 - After: And element "getByRole('dialog', { name: 'Select user for the request' }).locator('svg')" should be visible (54ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-11---After-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-54ms--7074c9afe29722b27e7d8581436675c1ec5978cd.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #37: Step 12 - Before: And element "getByRole('searchbox', { name: 'description' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-12---Before-And-element-getByRole-searchbox-name-description-should-be-visible-325271eceb1032d714dc2addc3c8178f0127568a.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #38: Step 12 - After: And element "getByRole('searchbox', { name: 'description' })" should be visible (107ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-12---After-And-element-getByRole-searchbox-name-description-should-be-visible-107ms--9920e1bf6c789dacf8181eafd547de11357b6a80.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #39: Step 13 - Before: When I fill "getByRole('searchbox', { name: 'description' })" with "<username>" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-13---Before-When-I-fill-getByRole-searchbox-name-description-with-username--ac0b9dc364042201118550a9f2f0700d5e61e233.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #40: Step 13 - After: When I fill "getByRole('searchbox', { name: 'description' })" with "<username>" (143ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-13---After-When-I-fill-getByRole-searchbox-name-description-with-username-143ms--6965e568a6b19ef762c2027b3832bef7ef5e8a19.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #41: Step 14 - Before: Then I wait for element "getByLabel('K, Jagannatha')" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-14---Before-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-c07f0bc2d1efb1416305d2e72aeaf49df41d15f7.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #42: Failure Screenshot (image/png) ─────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Failure-Screenshot-542d11fe5ab6020a20eb743e1391b31b77491061.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #43: screenshot (image/png) ─────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #44: video (video/webm) ─────────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\error-context.md

    attachment #46: trace (application/zip) ────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\trace.zip
    Usage:

        npx playwright show-trace test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🎬 Starting scenario: TC0101- Create Featured Result with Test Data
📁 Feature: Self Service Featured Results
🏷️ Tags: @combined, @self-service, @fr, @smoke, @create-featured-result
🏗️ Executing 7 background steps
📋 Background Step 1/7: Given I navigate to URL "https://es-settings-staging.kpmg.com/"
✅ Background step completed in 3879ms
📋 Background Step 2/7: And I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000
✅ Background step completed in 6251ms
📋 Background Step 3/7: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>"
✅ Background step completed in 88ms
📋 Background Step 4/7: And I click on "getByRole('button', { name: 'Next' })"
✅ Background step completed in 166ms
📋 Background Step 5/7: Then I wait for element "getByRole('button', { name: 'Settings' })" to be visible with timeout 60000
✅ Background step completed in 12962ms
📋 Background Step 6/7: And I wait for element "getByRole('button', { name: 'Create a request on behalf of' })" to be visible with timeout 30000
✅ Background step completed in 2837ms
📋 Background Step 7/7: And I wait for page title to contain "KPMG Find - Settings"
✅ Background step completed in 8ms
🏗️ Background steps completed successfully
📋 Step 1/46: When I click on "getByRole('button', { name: 'Create a request on behalf of' })"
✅ Step completed in 257ms
📋 Step 2/46: Then I wait for element "getByRole('heading', { name: 'Create a requests on behalf' })" to be visible with timeout 10000
✅ Step completed in 80ms
📋 Step 3/46: And the "locator('#displayName-label')" element should have exact text "Selected User"
✅ Step completed in 39ms
📋 Step 4/46: And element "getByRole('combobox', { name: 'FeaturedResult' })" should be visible
✅ Step completed in 60ms
📋 Step 5/46: And element "getByRole('button', { name: 'Cancel' })" should be visible
✅ Step completed in 85ms
📋 Step 6/46: When I click on "getByRole('combobox', { name: 'FeaturedResult' })"
✅ Step completed in 245ms
📋 Step 7/46: And I wait for "locator('#menu-').getByText('FeaturedResult')" to be visible with timeout 30000
✅ Step completed in 50ms
📋 Step 8/46: And I click on "locator('#menu-').getByText('FeaturedResult')"
✅ Step completed in 157ms
📋 Step 9/46: And I click on "getByRole('textbox', { name: 'Selected User' })"
✅ Step completed in 167ms
📋 Step 10/46: Then I wait for element "getByRole('heading', { name: 'Select user for the request' })" to be visible with timeout 10000
✅ Step completed in 53ms
📋 Step 11/46: And element "getByRole('dialog', { name: 'Select user for the request' }).locator('svg')" should be visible
✅ Step completed in 54ms
📋 Step 12/46: And element "getByRole('searchbox', { name: 'description' })" should be visible
✅ Step completed in 107ms
📋 Step 13/46: When I fill "getByRole('searchbox', { name: 'description' })" with "<username>"
✅ Step completed in 143ms
📋 Step 14/46: Then I wait for element "getByLabel('K, Jagannatha')" to be visible with timeout 10000
[[31merror[39m]: ❌ Step failed: Then I wait for element "getByLabel('K, Jagannatha')" to be visible with timeout 10000

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--1ed918cd9d00c8331e9570e5c951a15860c8b78b.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-3879ms--2d47c1632dd701d7641365727d5b000bb40c8933.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-2---Before-And-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-e645b6ea45ab0e08ccb6307d976cc30115fb60c4.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-2---After-And-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-6251ms--685f233c9bdd62d5ec28c517376fb49ab4e73f02.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com--e508fdc3e5316574a1fe8b5419e442b454b07df5.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com-88ms--d331ee7be14eb349aac3bafb4d6905ef66ccb352.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-4---Before-And-I-click-on-getByRole-button-name-Next--4524f1a2802fc3df67503ada045d1834a9d74352.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-4---After-And-I-click-on-getByRole-button-name-Next-166ms--cda1e7babd7bbfef4b1dec9261ebe9015f56b42b.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-47b2be8c572c1f9718a04b0972e27d58ed07b4f9.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-5---After-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-12962ms--645cba9334fa0369926e83cf7e866eb904f64d7a.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-6---Before-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-ad00efba73dcfb2cd51bbfb06cffd1959d8c339f.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-6---After-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-2837ms--5f00919785b8035d846136de2ff4db89b91f6af7.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-7---Before-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings--0239d938f4c55037a15df7a26cc1e73a3ec4b4c3.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Background-Step-7---After-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings-8ms--8c92114b20cdcbb0e96f36bf0395a176ee8768bd.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-1---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--f2828ab2648124860832dd644cd1767783c0dec8.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-1---After-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of-257ms--06477d74cbaefe5df5a87efdabf42c5fd1c0a10b.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-2---Before-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-b24003d3b0e9cf8cc2a5a972d4d6330dc6080ab8.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-2---After-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-80ms--07a41814d029951c1f7881c704d6771ca186ca84.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-3---Before-And-the-locator-displayName-label-element-should-have-exact-text-Selected-User--bf340e2c3b83032fcaaf4cadf9e04aab0cb79cd3.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-3---After-And-the-locator-displayName-label-element-should-have-exact-text-Selected-User-39ms--bf86f760271704dbce04fee33364385aa7c268a5.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-4---Before-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-a35c12eb1651736b8d9d060685cec96d3c92c1a0.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-4---After-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-60ms--bd8c227cc60bd97bf9a74c08b270dba264b39ede.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-5---Before-And-element-getByRole-button-name-Cancel-should-be-visible-069f0384df40cfa2238de3769ebe2e8817c87121.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-5---After-And-element-getByRole-button-name-Cancel-should-be-visible-85ms--a694e77e54c65df095ce2ac9515dd7c6bc5f5d62.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-6---Before-When-I-click-on-getByRole-combobox-name-FeaturedResult--59c57a1cbf3372a032792b4e366f7392b6dd9811.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-6---After-When-I-click-on-getByRole-combobox-name-FeaturedResult-245ms--ce435f584089f50105395564653eed96b865e0af.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-7---Before-And-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-9acdcc0a3df57547bff1f45788a6e47ae7b94eb6.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-7---After-And-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-50ms--078df80df95e60b956aaf2d4a1d6cfae271b3abf.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-8---Before-And-I-click-on-locator-menu--getByText-FeaturedResult--8984c6eb37da62b6fe24f8020e8a86929d852076.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-8---After-And-I-click-on-locator-menu--getByText-FeaturedResult-157ms--14439f5f6e28200efadce6c12cd11b144b577aab.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-9---Before-And-I-click-on-getByRole-textbox-name-Selected-User--758761d760b7f8ed8ad12402c0ff4319afb9e75a.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-9---After-And-I-click-on-getByRole-textbox-name-Selected-User-167ms--5952c13f9f36c7f25a81a7de81f8e4593227d6b3.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-10---Before-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-ee40225c8e6bcf18620ec6a629b7fda755c098cd.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-10---After-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-53ms--c9c577a9a3af63534dc537a2dcc83d5450234823.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-11---Before-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-c8bfbd64a2fe0559a3affcc099f5c73717cb5b4f.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-11---After-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-54ms--7074c9afe29722b27e7d8581436675c1ec5978cd.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-12---Before-And-element-getByRole-searchbox-name-description-should-be-visible-325271eceb1032d714dc2addc3c8178f0127568a.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-12---After-And-element-getByRole-searchbox-name-description-should-be-visible-107ms--9920e1bf6c789dacf8181eafd547de11357b6a80.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-13---Before-When-I-fill-getByRole-searchbox-name-description-with-username--ac0b9dc364042201118550a9f2f0700d5e61e233.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-13---After-When-I-fill-getByRole-searchbox-name-description-with-username-143ms--6965e568a6b19ef762c2027b3832bef7ef5e8a19.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-14---Before-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-c07f0bc2d1efb1416305d2e72aeaf49df41d15f7.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Failure-Screenshot-542d11fe5ab6020a20eb743e1391b31b77491061.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\test-failed-1.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\video.webm]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\error-context.md]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[❌ Scenario failed: TC0101- Create Featured Result with Test Data locator.waitFor: Timeout 10000ms exceeded.
Call log:
[2m  - waiting for getByLabel('K, Jagannatha') to be visible[22m

    at PlaywrightWorld.waitForElement [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-world.ts:697:19[90m)[39m
    at Object.handler [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\step-definition-registry.ts:189:21[90m)[39m
    at StepDefinitionRegistry.executeStep [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\step-definition-registry.ts:361:23[90m)[39m
    at PlaywrightCucumberRunner.executeStep [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-cucumber-runner.ts:225:31[90m)[39m
    at PlaywrightCucumberRunner.executeScenario [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-cucumber-runner.ts:303:20[90m)[39m
    at [90mC:\workspace\Playwright_Automation_Framework\[39mtests\cucumber-wrapper.spec.ts:45:9 {
  name: [32m'TimeoutError'[39m,
  [[32mSymbol(step)[39m]: {
    stepId: [32m'pw:api@128'[39m,
    location: {
      file: [32m'C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts'[39m,
      line: [33m697[39m,
      column: [33m19[39m,
      function: [32m'PlaywrightWorld.waitForElement'[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m"locator.getByLabel('K, Jagannatha').waitFor"[39m,
    apiName: [32m'locator.waitFor'[39m,
    params: {
      selector: [32m'internal:label="K, Jagannatha"i'[39m,
      strict: [33mtrue[39m,
      omitReturnValue: [33mtrue[39m,
      state: [32m'visible'[39m,
      timeout: [33m10000[39m
    },
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@128'[39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1749627816951[39m,
    error: {
      message: [32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for getByLabel('K, Jagannatha') to be visible\x1B[22m\n"[39m,
      stack: [32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for getByLabel('K, Jagannatha') to be visible\x1B[22m\n"[39m +
        [32m'\n'[39m +
        [32m'    at PlaywrightWorld.waitForElement (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts:697:19)\n'[39m +
        [32m'    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:189:21)\n'[39m +
        [32m'    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:361:23)\n'[39m +
        [32m'    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:225:31)\n'[39m +
        [32m'    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:303:20)\n'[39m +
        [32m'    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9'[39m,
      cause: [90mundefined[39m
    }
  }
}
]]>
</system-err>
</testcase>
</testsuite>
</testsuites>