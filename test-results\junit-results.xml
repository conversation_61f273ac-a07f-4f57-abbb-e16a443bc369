<testsuites id="" name="" tests="1" failures="1" skipped="0" errors="0" time="43.804824">
<testsuite name="cucumber-wrapper.spec.ts" timestamp="2025-06-11T07:37:03.633Z" hostname="edge" tests="1" failures="1" skipped="0" time="37.023" errors="0">
<testcase name="Self Service Featured Results › TC0101- Create Featured Result with Test Data" classname="cucumber-wrapper.spec.ts" time="37.023">
<properties>
<property name="feature" value="Self Service Featured Results">
</property>
<property name="tags" value="@combined, @self-service, @fr, @smoke, @create-featured-result">
</property>
<property name="file" value="src\features\self-service-fr.feature">
</property>
</properties>
<failure message="cucumber-wrapper.spec.ts:34:11 TC0101- Create Featured Result with Test Data" type="FAILURE">
<![CDATA[  [edge] › cucumber-wrapper.spec.ts:34:11 › Self Service Featured Results › TC0101- Create Featured Result with Test Data 

    TimeoutError: locator.click: Timeout 15000ms exceeded.
    Call log:
      - waiting for getByRole('button', { name: 'Create a request on behalf of' })


       at ..\src\core\playwright-world.ts:660

      658 |     }
      659 |
    > 660 |     await element.click();
          |                   ^
      661 |   }
      662 |
      663 |   /**
        at PlaywrightWorld.clickElement (C:\workspace\Playwright_Automation_Framework\src\core\playwright-world.ts:660:19)
        at Object.handler (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:62:9)
        at StepDefinitionRegistry.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:361:9)
        at PlaywrightCucumberRunner.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:197:7)
        at PlaywrightCucumberRunner.executeScenario (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:232:9)
        at C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:45:9

    Error: browserContext._wrapApiCall: Test ended.

    attachment #1: Step 1 - Before: When I click on "getByRole('button', { name: 'Create a request on behalf of' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-1---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--f2828ab2648124860832dd644cd1767783c0dec8.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: Failure Screenshot (image/png) ──────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Failure-Screenshot-542d11fe5ab6020a20eb743e1391b31b77491061.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\error-context.md

    attachment #5: trace (application/zip) ─────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\trace.zip
    Usage:

        npx playwright show-trace test-results\playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🎬 Starting scenario: TC0101- Create Featured Result with Test Data
📁 Feature: Self Service Featured Results
🏷️ Tags: @combined, @self-service, @fr, @smoke, @create-featured-result
📋 Step 1/46: When I click on "getByRole('button', { name: 'Create a request on behalf of' })"
[[31merror[39m]: ❌ Step failed: When I click on "getByRole('button', { name: 'Create a request on behalf of' })"

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Step-1---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--f2828ab2648124860832dd644cd1767783c0dec8.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\attachments\Failure-Screenshot-542d11fe5ab6020a20eb743e1391b31b77491061.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\test-failed-1.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\error-context.md]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[❌ Scenario failed: TC0101- Create Featured Result with Test Data locator.click: Timeout 15000ms exceeded.
Call log:
[2m  - waiting for getByRole('button', { name: 'Create a request on behalf of' })[22m

    at PlaywrightWorld.clickElement [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-world.ts:660:19[90m)[39m
    at Object.handler [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\step-definition-registry.ts:62:9[90m)[39m
    at StepDefinitionRegistry.executeStep [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\step-definition-registry.ts:361:9[90m)[39m
    at PlaywrightCucumberRunner.executeStep [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-cucumber-runner.ts:197:7[90m)[39m
    at PlaywrightCucumberRunner.executeScenario [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-cucumber-runner.ts:232:9[90m)[39m
    at [90mC:\workspace\Playwright_Automation_Framework\[39mtests\cucumber-wrapper.spec.ts:45:9 {
  name: [32m'TimeoutError'[39m,
  [[32mSymbol(step)[39m]: {
    stepId: [32m'pw:api@11'[39m,
    location: {
      file: [32m'C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts'[39m,
      line: [33m660[39m,
      column: [33m19[39m,
      function: [32m'PlaywrightWorld.clickElement'[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m"locator.getByRole('button', { name: 'Create a request on behalf of' }).click"[39m,
    apiName: [32m'locator.click'[39m,
    params: {
      selector: [32m'internal:role=button[name="Create a request on behalf of"i]'[39m,
      strict: [33mtrue[39m
    },
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@11'[39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1749627441661[39m,
    error: {
      message: [32m'TimeoutError: locator.click: Timeout 15000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for getByRole('button', { name: 'Create a request on behalf of' })\x1B[22m\n"[39m,
      stack: [32m'TimeoutError: locator.click: Timeout 15000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for getByRole('button', { name: 'Create a request on behalf of' })\x1B[22m\n"[39m +
        [32m'\n'[39m +
        [32m'    at PlaywrightWorld.clickElement (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts:660:19)\n'[39m +
        [32m'    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:62:9)\n'[39m +
        [32m'    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:361:9)\n'[39m +
        [32m'    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:197:7)\n'[39m +
        [32m'    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:232:9)\n'[39m +
        [32m'    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9'[39m,
      cause: [90mundefined[39m
    }
  }
}
]]>
</system-err>
</testcase>
</testsuite>
</testsuites>