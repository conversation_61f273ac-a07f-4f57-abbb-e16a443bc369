<testsuites id="" name="" tests="1" failures="1" skipped="0" errors="0" time="52.405732">
<testsuite name="cucumber-wrapper.spec.ts" timestamp="2025-06-11T04:24:56.534Z" hostname="chromium" tests="1" failures="1" skipped="0" time="46.106" errors="0">
<testcase name="Self Service Featured Results › TC0101- Login and Check the Default Data Source Order" classname="cucumber-wrapper.spec.ts" time="46.106">
<properties>
<property name="feature" value="Self Service Featured Results">
</property>
<property name="tags" value="@combined, @self-service, @fr">
</property>
<property name="file" value="src\features\self-service-fr.feature">
</property>
</properties>
<failure message="cucumber-wrapper.spec.ts:34:11 TC0101- Login and Check the Default Data Source Order" type="FAILURE">
<![CDATA[  [chromium] › cucumber-wrapper.spec.ts:34:11 › Self Service Featured Results › TC0101- Login and Check the Default Data Source Order 

    Error: locator.waitFor: Unexpected token "locator(" while parsing css selector "locator('#menu-')". Did you mean to CSS.escape it?
    Call log:
      - waiting for locator('#menu-') >> internal:text="FeaturedResult"i to be visible


       at ..\src\core\playwright-world.ts:335

      333 |     this.logger.info(`⏳ Waiting for element: ${selector}`);
      334 |     const element = this.parseLocator(selector);
    > 335 |     await element.waitFor({ state: 'visible', timeout });
          |                   ^
      336 |   }
      337 |
      338 |   /**
        at PlaywrightWorld.waitForElement (C:\workspace\Playwright_Automation_Framework\src\core\playwright-world.ts:335:19)
        at Object.handler (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:170:21)
        at StepDefinitionRegistry.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:334:23)
        at PlaywrightCucumberRunner.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:196:31)
        at PlaywrightCucumberRunner.executeScenario (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:231:20)
        at C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:45:9

    attachment #1: Step 1 - Before: Given I navigate to URL "https://es-settings-staging.kpmg.com/" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--9f5ae14be54d00e3a62c2b16aaf7cc1484e376f8.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: Step 1 - After: Given I navigate to URL "https://es-settings-staging.kpmg.com/" (3975ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-3975ms--a1a087f0e3c500721ff9ae627164f2a156e6def3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: Step 2 - Before: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-1d25b4a7f9215540528bcae43fdce44b2e129411.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: Step 2 - After: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000 (8539ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-8539ms--73617745d97bb32ec3dc8ae51580cc93e4a45228.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: Step 3 - Before: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com--316da82eef654cae4ad12d47f8fe1ffa375efaa6.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #6: Step 3 - After: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>" (85ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com-85ms--4a9f4d18cec09338fddd537da3174525295c5e1c.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #7: Step 4 - Before: And I click on "getByRole('button', { name: 'Next' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-4---Before-And-I-click-on-getByRole-button-name-Next--996138976132e5a6f4d8dbc56204b69cf5c74dd9.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #8: Step 4 - After: And I click on "getByRole('button', { name: 'Next' })" (120ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-4---After-And-I-click-on-getByRole-button-name-Next-120ms--3f82437afad13e30321af3d78ac8f5fa484586c5.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #9: Step 5 - Before: Then I wait for element "getByRole('button', { name: 'Settings' })" to be visible with timeout 60000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-b7eb0d29174c8085f8fb05af67b2219b6e33c240.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #10: Step 5 - After: Then I wait for element "getByRole('button', { name: 'Settings' })" to be visible with timeout 60000 (11918ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-5---After-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-11918ms--3dd4df820ca306c48c56e1222ae6fb296e3fa9cb.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #11: Step 6 - Before: And I wait for element "getByRole('button', { name: 'Create a request on behalf of' })" to be visible with timeout 30000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-6---Before-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-9a62bc5c83bd200eb2a7dff944a05f818f65627a.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #12: Step 6 - After: And I wait for element "getByRole('button', { name: 'Create a request on behalf of' })" to be visible with timeout 30000 (3329ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-6---After-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-3329ms--17fc87cf7487f9ec6179fdd411bb5055ed61bea4.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #13: Step 7 - Before: And I wait for page title to contain "KPMG Find - Settings" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-7---Before-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings--e0bfffde38f7f27a22628c6df8759960773211f9.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #14: Step 7 - After: And I wait for page title to contain "KPMG Find - Settings" (13ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-7---After-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings-13ms--4b83bbf3501667bb17e22b4c76e9fb6dc6462bf1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #15: Step 8 - Before: When I click on "getByRole('button', { name: 'Create a request on behalf of' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-8---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--589f5cbca4ff4d2eb36011127d110c26f4ff274a.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #16: Step 8 - After: When I click on "getByRole('button', { name: 'Create a request on behalf of' })" (260ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-8---After-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of-260ms--03f7be8836af0bea3f8d59039c40bcb3f07cf292.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #17: Step 9 - Before: Then I wait for element "getByRole('heading', { name: 'Create a requests on behalf' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-9---Before-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-04edb0a85796d1d977f53b8c4037f0ff4bbd750a.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #18: Step 9 - After: Then I wait for element "getByRole('heading', { name: 'Create a requests on behalf' })" to be visible with timeout 10000 (43ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-9---After-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-43ms--c971768b63e560fabc0ff2286d7a76cb56abac01.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #19: Step 10 - Before: Then the "locator('#displayName-label')" element should have exact text "Selected User" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-10---Before-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User--165c17b6050f87ae4a620d027e9fd607edf2912b.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #20: Step 10 - After: Then the "locator('#displayName-label')" element should have exact text "Selected User" (543ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-10---After-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User-543ms--feb725ac02ac5f75d4bed7dedb852bf3829125f9.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #21: Step 11 - Before: And element "getByRole('combobox', { name: 'FeaturedResult' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-11---Before-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-f466302481ff8688838ef58344d6d1badc735b93.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #22: Step 11 - After: And element "getByRole('combobox', { name: 'FeaturedResult' })" should be visible (542ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-11---After-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-542ms--f84da7efb281bbb123b9457fafd025189069862f.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #23: Step 12 - Before: And element "getByRole('button', { name: 'Cancel' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-12---Before-And-element-getByRole-button-name-Cancel-should-be-visible-3dd38e4bd0b48ac6cd8e82e706f3ceaa7cb695e4.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #24: Step 12 - After: And element "getByRole('button', { name: 'Cancel' })" should be visible (527ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-12---After-And-element-getByRole-button-name-Cancel-should-be-visible-527ms--58ad1c6ff6ad0e844b8e1bb1067a683d738f6616.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #25: Step 13 - Before: When I click on "getByRole('combobox', { name: 'FeaturedResult' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-13---Before-When-I-click-on-getByRole-combobox-name-FeaturedResult--b44ff2847df8960e31125198a9f555b9005c3f2c.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #26: Step 13 - After: When I click on "getByRole('combobox', { name: 'FeaturedResult' })" (123ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-13---After-When-I-click-on-getByRole-combobox-name-FeaturedResult-123ms--f94b88f5efa8be97575230b453269d0c8c81733e.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #27: Step 14 - Before: Then I wait for "locator('#menu-').getByText('FeaturedResult')" to be visible with timeout 30000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-14---Before-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-b63538221cab462f800c2d0ec0aa353bd3fd08f7.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #28: Failure Screenshot (image/png) ─────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Failure-Screenshot-8bb9cb48c026463c729a9ab3c7ce68e5cf4ff1b8.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #29: screenshot (image/png) ─────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #30: video (video/webm) ─────────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\error-context.md

    attachment #32: trace (application/zip) ────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\trace.zip
    Usage:

        npx playwright show-trace test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🎬 Starting scenario: TC0101- Login and Check the Default Data Source Order
📁 Feature: Self Service Featured Results
🏷️ Tags: @combined, @self-service, @fr
📋 Step 1/27: Given I navigate to URL "https://es-settings-staging.kpmg.com/"
✅ Step completed in 3975ms
📋 Step 2/27: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000
✅ Step completed in 8539ms
📋 Step 3/27: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>"
✅ Step completed in 85ms
📋 Step 4/27: And I click on "getByRole('button', { name: 'Next' })"
✅ Step completed in 120ms
📋 Step 5/27: Then I wait for element "getByRole('button', { name: 'Settings' })" to be visible with timeout 60000
✅ Step completed in 11918ms
📋 Step 6/27: And I wait for element "getByRole('button', { name: 'Create a request on behalf of' })" to be visible with timeout 30000
✅ Step completed in 3329ms
📋 Step 7/27: And I wait for page title to contain "KPMG Find - Settings"
✅ Step completed in 13ms
📋 Step 8/27: When I click on "getByRole('button', { name: 'Create a request on behalf of' })"
✅ Step completed in 260ms
📋 Step 9/27: Then I wait for element "getByRole('heading', { name: 'Create a requests on behalf' })" to be visible with timeout 10000
✅ Step completed in 43ms
📋 Step 10/27: Then the "locator('#displayName-label')" element should have exact text "Selected User"
[[33mwarn[39m]: ⚠️ No step definition found for: "Then the "locator('#displayName-label')" element should have exact text "Selected User""
✅ Step completed in 543ms
📋 Step 11/27: And element "getByRole('combobox', { name: 'FeaturedResult' })" should be visible
[[33mwarn[39m]: ⚠️ No step definition found for: "And element "getByRole('combobox', { name: 'FeaturedResult' })" should be visible"
✅ Step completed in 542ms
📋 Step 12/27: And element "getByRole('button', { name: 'Cancel' })" should be visible
[[33mwarn[39m]: ⚠️ No step definition found for: "And element "getByRole('button', { name: 'Cancel' })" should be visible"
✅ Step completed in 527ms
📋 Step 13/27: When I click on "getByRole('combobox', { name: 'FeaturedResult' })"
✅ Step completed in 123ms
📋 Step 14/27: Then I wait for "locator('#menu-').getByText('FeaturedResult')" to be visible with timeout 30000
[[31merror[39m]: ❌ Step failed: Then I wait for "locator('#menu-').getByText('FeaturedResult')" to be visible with timeout 30000

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--9f5ae14be54d00e3a62c2b16aaf7cc1484e376f8.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-3975ms--a1a087f0e3c500721ff9ae627164f2a156e6def3.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-1d25b4a7f9215540528bcae43fdce44b2e129411.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-8539ms--73617745d97bb32ec3dc8ae51580cc93e4a45228.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com--316da82eef654cae4ad12d47f8fe1ffa375efaa6.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com-85ms--4a9f4d18cec09338fddd537da3174525295c5e1c.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-4---Before-And-I-click-on-getByRole-button-name-Next--996138976132e5a6f4d8dbc56204b69cf5c74dd9.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-4---After-And-I-click-on-getByRole-button-name-Next-120ms--3f82437afad13e30321af3d78ac8f5fa484586c5.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-b7eb0d29174c8085f8fb05af67b2219b6e33c240.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-5---After-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-11918ms--3dd4df820ca306c48c56e1222ae6fb296e3fa9cb.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-6---Before-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-9a62bc5c83bd200eb2a7dff944a05f818f65627a.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-6---After-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-3329ms--17fc87cf7487f9ec6179fdd411bb5055ed61bea4.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-7---Before-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings--e0bfffde38f7f27a22628c6df8759960773211f9.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-7---After-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings-13ms--4b83bbf3501667bb17e22b4c76e9fb6dc6462bf1.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-8---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--589f5cbca4ff4d2eb36011127d110c26f4ff274a.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-8---After-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of-260ms--03f7be8836af0bea3f8d59039c40bcb3f07cf292.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-9---Before-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-04edb0a85796d1d977f53b8c4037f0ff4bbd750a.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-9---After-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-43ms--c971768b63e560fabc0ff2286d7a76cb56abac01.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-10---Before-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User--165c17b6050f87ae4a620d027e9fd607edf2912b.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-10---After-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User-543ms--feb725ac02ac5f75d4bed7dedb852bf3829125f9.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-11---Before-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-f466302481ff8688838ef58344d6d1badc735b93.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-11---After-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-542ms--f84da7efb281bbb123b9457fafd025189069862f.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-12---Before-And-element-getByRole-button-name-Cancel-should-be-visible-3dd38e4bd0b48ac6cd8e82e706f3ceaa7cb695e4.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-12---After-And-element-getByRole-button-name-Cancel-should-be-visible-527ms--58ad1c6ff6ad0e844b8e1bb1067a683d738f6616.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-13---Before-When-I-click-on-getByRole-combobox-name-FeaturedResult--b44ff2847df8960e31125198a9f555b9005c3f2c.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-13---After-When-I-click-on-getByRole-combobox-name-FeaturedResult-123ms--f94b88f5efa8be97575230b453269d0c8c81733e.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-14---Before-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-b63538221cab462f800c2d0ec0aa353bd3fd08f7.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Failure-Screenshot-8bb9cb48c026463c729a9ab3c7ce68e5cf4ff1b8.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\test-failed-1.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\video.webm]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\error-context.md]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[❌ Scenario failed: TC0101- Login and Check the Default Data Source Order locator.waitFor: Unexpected token "locator(" while parsing css selector "locator('#menu-')". Did you mean to CSS.escape it?
Call log:
[2m  - waiting for locator('#menu-') >> internal:text="FeaturedResult"i to be visible[22m

    at PlaywrightWorld.waitForElement [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-world.ts:335:19[90m)[39m
    at Object.handler [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\step-definition-registry.ts:170:21[90m)[39m
    at StepDefinitionRegistry.executeStep [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\step-definition-registry.ts:334:23[90m)[39m
    at PlaywrightCucumberRunner.executeStep [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-cucumber-runner.ts:196:31[90m)[39m
    at PlaywrightCucumberRunner.executeScenario [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-cucumber-runner.ts:231:20[90m)[39m
    at [90mC:\workspace\Playwright_Automation_Framework\[39mtests\cucumber-wrapper.spec.ts:45:9 {
  name: [32m'Error'[39m,
  [[32mSymbol(step)[39m]: {
    stepId: [32m'pw:api@76'[39m,
    location: {
      file: [32m'C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts'[39m,
      line: [33m335[39m,
      column: [33m19[39m,
      function: [32m'PlaywrightWorld.waitForElement'[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m`locator.waitFor(locator('#menu-') >> internal:text="FeaturedResult"i)`[39m,
    apiName: [32m'locator.waitFor'[39m,
    params: {
      selector: [32m`locator('#menu-') >> internal:text="FeaturedResult"i`[39m,
      strict: [33mtrue[39m,
      omitReturnValue: [33mtrue[39m,
      state: [32m'visible'[39m,
      timeout: [33m30000[39m
    },
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@76'[39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1749615941088[39m,
    error: {
      message: [32m`Error: locator.waitFor: Unexpected token "locator(" while parsing css selector "locator('#menu-')". Did you mean to CSS.escape it?\n`[39m +
        [32m'Call log:\n'[39m +
        [32m`\x1B[2m  - waiting for locator('#menu-') >> internal:text="FeaturedResult"i to be visible\x1B[22m\n`[39m,
      stack: [32m`Error: locator.waitFor: Unexpected token "locator(" while parsing css selector "locator('#menu-')". Did you mean to CSS.escape it?\n`[39m +
        [32m'Call log:\n'[39m +
        [32m`\x1B[2m  - waiting for locator('#menu-') >> internal:text="FeaturedResult"i to be visible\x1B[22m\n`[39m +
        [32m'\n'[39m +
        [32m'    at PlaywrightWorld.waitForElement (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts:335:19)\n'[39m +
        [32m'    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:170:21)\n'[39m +
        [32m'    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:334:23)\n'[39m +
        [32m'    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:196:31)\n'[39m +
        [32m'    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:231:20)\n'[39m +
        [32m'    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9'[39m,
      cause: [90mundefined[39m
    }
  }
}
]]>
</system-err>
</testcase>
</testsuite>
</testsuites>