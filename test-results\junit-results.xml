<testsuites id="" name="" tests="1" failures="1" skipped="0" errors="0" time="101.69023800000001">
<testsuite name="cucumber-wrapper.spec.ts" timestamp="2025-06-11T05:30:21.884Z" hostname="chromium" tests="1" failures="1" skipped="0" time="90.887" errors="0">
<testcase name="Self Service Featured Results › TC0101- Login and Check the Default Data Source Order" classname="cucumber-wrapper.spec.ts" time="90.887">
<properties>
<property name="feature" value="Self Service Featured Results">
</property>
<property name="tags" value="@combined, @self-service, @fr">
</property>
<property name="file" value="src\features\self-service-fr.feature">
</property>
</properties>
<failure message="cucumber-wrapper.spec.ts:34:11 TC0101- Login and Check the Default Data Source Order" type="FAILURE">
<![CDATA[  [chromium] › cucumber-wrapper.spec.ts:34:11 › Self Service Featured Results › TC0101- Login and Check the Default Data Source Order 

    Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 102 elements:
        1) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('.MuiChip-label').first()
        2) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()
        3) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">terms</span> aka getByText('terms', { exact: true })
        4) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka locator('td:nth-child(10) > div > .MuiButtonBase-root').first()
        5) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()
        6) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">AdWord</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')
        7) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">gggggggggggggggg</span> aka getByText('gggggggggggggggg')
        8) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka locator('tr:nth-child(3) > td:nth-child(10) > div > .MuiButtonBase-root')
        9) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')
        10) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('tr:nth-child(4) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')
        ...

    Call log:
      - expect.toHaveText with timeout 30000ms
      - waiting for locator('.MuiChip-label')


       at ..\src\core\step-definition-registry.ts:154

      152 |       async (world: PlaywrightWorld, selector: string, text: string) => {
      153 |         const element = world.getLocator(selector);
    > 154 |         await expect(element).toHaveText(text);
          |                               ^
      155 |       },
      156 |       'Verify element has exact text (with "the" and "element")'
      157 |     );
        at Object.handler (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:154:31)
        at StepDefinitionRegistry.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:361:23)
        at PlaywrightCucumberRunner.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:196:31)
        at PlaywrightCucumberRunner.executeScenario (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:231:20)
        at C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:45:9

    attachment #1: Step 1 - Before: Given I navigate to URL "https://es-settings-staging.kpmg.com/" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--9f5ae14be54d00e3a62c2b16aaf7cc1484e376f8.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: Step 1 - After: Given I navigate to URL "https://es-settings-staging.kpmg.com/" (3301ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-3301ms--a1a087f0e3c500721ff9ae627164f2a156e6def3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: Step 2 - Before: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-1d25b4a7f9215540528bcae43fdce44b2e129411.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: Step 2 - After: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000 (6899ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-6899ms--73617745d97bb32ec3dc8ae51580cc93e4a45228.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: Step 3 - Before: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com--316da82eef654cae4ad12d47f8fe1ffa375efaa6.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #6: Step 3 - After: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>" (138ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com-138ms--4a9f4d18cec09338fddd537da3174525295c5e1c.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #7: Step 4 - Before: And I click on "getByRole('button', { name: 'Next' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-4---Before-And-I-click-on-getByRole-button-name-Next--996138976132e5a6f4d8dbc56204b69cf5c74dd9.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #8: Step 4 - After: And I click on "getByRole('button', { name: 'Next' })" (136ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-4---After-And-I-click-on-getByRole-button-name-Next-136ms--3f82437afad13e30321af3d78ac8f5fa484586c5.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #9: Step 5 - Before: Then I wait for element "getByRole('button', { name: 'Settings' })" to be visible with timeout 60000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-b7eb0d29174c8085f8fb05af67b2219b6e33c240.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #10: Step 5 - After: Then I wait for element "getByRole('button', { name: 'Settings' })" to be visible with timeout 60000 (13285ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-5---After-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-13285ms--3dd4df820ca306c48c56e1222ae6fb296e3fa9cb.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #11: Step 6 - Before: And I wait for element "getByRole('button', { name: 'Create a request on behalf of' })" to be visible with timeout 30000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-6---Before-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-9a62bc5c83bd200eb2a7dff944a05f818f65627a.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #12: Step 6 - After: And I wait for element "getByRole('button', { name: 'Create a request on behalf of' })" to be visible with timeout 30000 (3760ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-6---After-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-3760ms--17fc87cf7487f9ec6179fdd411bb5055ed61bea4.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #13: Step 7 - Before: And I wait for page title to contain "KPMG Find - Settings" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-7---Before-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings--e0bfffde38f7f27a22628c6df8759960773211f9.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #14: Step 7 - After: And I wait for page title to contain "KPMG Find - Settings" (10ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-7---After-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings-10ms--4b83bbf3501667bb17e22b4c76e9fb6dc6462bf1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #15: Step 8 - Before: When I click on "getByRole('button', { name: 'Create a request on behalf of' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-8---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--589f5cbca4ff4d2eb36011127d110c26f4ff274a.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #16: Step 8 - After: When I click on "getByRole('button', { name: 'Create a request on behalf of' })" (231ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-8---After-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of-231ms--03f7be8836af0bea3f8d59039c40bcb3f07cf292.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #17: Step 9 - Before: Then I wait for element "getByRole('heading', { name: 'Create a requests on behalf' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-9---Before-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-04edb0a85796d1d977f53b8c4037f0ff4bbd750a.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #18: Step 9 - After: Then I wait for element "getByRole('heading', { name: 'Create a requests on behalf' })" to be visible with timeout 10000 (54ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-9---After-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-54ms--c971768b63e560fabc0ff2286d7a76cb56abac01.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #19: Step 10 - Before: Then the "locator('#displayName-label')" element should have exact text "Selected User" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-10---Before-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User--165c17b6050f87ae4a620d027e9fd607edf2912b.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #20: Step 10 - After: Then the "locator('#displayName-label')" element should have exact text "Selected User" (71ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-10---After-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User-71ms--feb725ac02ac5f75d4bed7dedb852bf3829125f9.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #21: Step 11 - Before: And element "getByRole('combobox', { name: 'FeaturedResult' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-11---Before-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-f466302481ff8688838ef58344d6d1badc735b93.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #22: Step 11 - After: And element "getByRole('combobox', { name: 'FeaturedResult' })" should be visible (51ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-11---After-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-51ms--f84da7efb281bbb123b9457fafd025189069862f.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #23: Step 12 - Before: And element "getByRole('button', { name: 'Cancel' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-12---Before-And-element-getByRole-button-name-Cancel-should-be-visible-3dd38e4bd0b48ac6cd8e82e706f3ceaa7cb695e4.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #24: Step 12 - After: And element "getByRole('button', { name: 'Cancel' })" should be visible (41ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-12---After-And-element-getByRole-button-name-Cancel-should-be-visible-41ms--58ad1c6ff6ad0e844b8e1bb1067a683d738f6616.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #25: Step 13 - Before: When I click on "getByRole('combobox', { name: 'FeaturedResult' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-13---Before-When-I-click-on-getByRole-combobox-name-FeaturedResult--b44ff2847df8960e31125198a9f555b9005c3f2c.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #26: Step 13 - After: When I click on "getByRole('combobox', { name: 'FeaturedResult' })" (168ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-13---After-When-I-click-on-getByRole-combobox-name-FeaturedResult-168ms--f94b88f5efa8be97575230b453269d0c8c81733e.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #27: Step 14 - Before: Then I wait for "locator('#menu-').getByText('FeaturedResult')" to be visible with timeout 30000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-14---Before-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-b63538221cab462f800c2d0ec0aa353bd3fd08f7.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #28: Step 14 - After: Then I wait for "locator('#menu-').getByText('FeaturedResult')" to be visible with timeout 30000 (42ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-14---After-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-42ms--00989910ab77e431a1340290b365d1acde0c36d1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #29: Step 15 - Before: When I click on "locator('#menu-').getByText('FeaturedResult')" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-15---Before-When-I-click-on-locator-menu--getByText-FeaturedResult--d3599577eff0509e5507af39e850dad2e5d8d263.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #30: Step 15 - After: When I click on "locator('#menu-').getByText('FeaturedResult')" (113ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-15---After-When-I-click-on-locator-menu--getByText-FeaturedResult-113ms--9875c948c18489dbe34e5ed4b757d4f451df60d2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #31: Step 16 - Before: When I click on "getByRole('textbox', { name: 'Selected User' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-16---Before-When-I-click-on-getByRole-textbox-name-Selected-User--e7453252d51fec73a60460d99d29ff0cc4471d0e.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #32: Step 16 - After: When I click on "getByRole('textbox', { name: 'Selected User' })" (177ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-16---After-When-I-click-on-getByRole-textbox-name-Selected-User-177ms--b9914e79665458f3c1b4e8d3130e0144e074bd60.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #33: Step 17 - Before: Then I wait for element "getByRole('heading', { name: 'Select user for the request' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-17---Before-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-081a4f6989bf3959043643f2ee92f933d294559d.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #34: Step 17 - After: Then I wait for element "getByRole('heading', { name: 'Select user for the request' })" to be visible with timeout 10000 (49ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-17---After-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-49ms--2d5b1362c025503f2b83d8744564bd6523341c42.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #35: Step 18 - Before: And element "getByRole('dialog', { name: 'Select user for the request' }).locator('svg')" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-18---Before-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-c8cb60666241e60459e04119164baf19ede36dec.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #36: Step 18 - After: And element "getByRole('dialog', { name: 'Select user for the request' }).locator('svg')" should be visible (50ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-18---After-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-50ms--0b241fc56d9a2dbbfadd6b051a7b40c998e51f97.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #37: Step 19 - Before: And element "getByRole('searchbox', { name: 'description' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-19---Before-And-element-getByRole-searchbox-name-description-should-be-visible-a3728c020b254f2103b34b0dc6af2b8cd2f29e7e.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #38: Step 19 - After: And element "getByRole('searchbox', { name: 'description' })" should be visible (42ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-19---After-And-element-getByRole-searchbox-name-description-should-be-visible-42ms--956e1319ff9389a01034dc21c463ebce2e24301c.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #39: Step 20 - Before: When I fill "getByRole('searchbox', { name: 'description' })" with "<EMAIL>" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-20---Before-When-I-fill-getByRole-searchbox-name-description-with-jagannathak-kpmg-com--6c8452e9192231788e4c5f1b13f164438f545565.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #40: Step 20 - After: When I fill "getByRole('searchbox', { name: 'description' })" with "<EMAIL>" (103ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-20---After-When-I-fill-getByRole-searchbox-name-description-with-jagannathak-kpmg-com-103ms--4229ab33ae24538a749e13b5cc8fbe2b09cb9835.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #41: Step 21 - Before: Then I wait for element "getByLabel('K, Jagannatha')" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-21---Before-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-b84c83400fb05aee3210d524537b31cf2168e238.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #42: Step 21 - After: Then I wait for element "getByLabel('K, Jagannatha')" to be visible with timeout 10000 (2039ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-21---After-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-2039ms--d4c4b0791a0d6e6f73a25da980f328fbdc70b5aa.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #43: Step 22 - Before: And I wait for element "getByLabel('<EMAIL>')" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-22---Before-And-I-wait-for-element-getByLabel-jagannathak-kpmg-com-to-be-visible-with-timeout-10000-f62adb5618e58a83aa622f261fe25140be482880.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #44: Step 22 - After: And I wait for element "getByLabel('<EMAIL>')" to be visible with timeout 10000 (35ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-22---After-And-I-wait-for-element-getByLabel-jagannathak-kpmg-com-to-be-visible-with-timeout-10000-35ms--b058dc2951ccf78260cf3f1a942f7e24e4da7322.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #45: Step 23 - Before: When I click on "getByLabel('K, Jagannatha')" (image/png) ────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-23---Before-When-I-click-on-getByLabel-K-Jagannatha--45924ca3f70ae46b34dd928e2b059beb8b8cb12d.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #46: Step 23 - After: When I click on "getByLabel('K, Jagannatha')" (154ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-23---After-When-I-click-on-getByLabel-K-Jagannatha-154ms--634c8214023995af38bd6fcbb95f2c7f73bfeae2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #47: Step 24 - Before: Then I wait for element "getByRole('button', { name: 'Select' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-24---Before-Then-I-wait-for-element-getByRole-button-name-Select-to-be-visible-with-timeout-10000-7fca1bf9d98139bddf1fe4675a4490de6770efec.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #48: Step 24 - After: Then I wait for element "getByRole('button', { name: 'Select' })" to be visible with timeout 10000 (39ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-24---After-Then-I-wait-for-element-getByRole-button-name-Select-to-be-visible-with-timeout-10000-39ms--7d54beb71eec6671431e2b2d2c6eeb87fbfd5b8d.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #49: Step 25 - Before: When I click on "getByRole('button', { name: 'Select' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-25---Before-When-I-click-on-getByRole-button-name-Select--400568b0d60065a269429e72fe5e133e86c46636.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #50: Step 25 - After: When I click on "getByRole('button', { name: 'Select' })" (142ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-25---After-When-I-click-on-getByRole-button-name-Select-142ms--d696b7e8bce66eb0c769f7f4d83c5147db7d296e.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #51: Step 26 - Before: Then I wait for element "getByRole('button', { name: 'Create' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-26---Before-Then-I-wait-for-element-getByRole-button-name-Create-to-be-visible-with-timeout-10000-b66a5d42c0b9a82dafe8f1b46714d7078892f182.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #52: Step 26 - After: Then I wait for element "getByRole('button', { name: 'Create' })" to be visible with timeout 10000 (44ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-26---After-Then-I-wait-for-element-getByRole-button-name-Create-to-be-visible-with-timeout-10000-44ms--2001504c604814f105db554e4d183e4a4a125e6b.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #53: Step 27 - Before: When I click on "getByRole('button', { name: 'Create' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-27---Before-When-I-click-on-getByRole-button-name-Create--596b90d80a4e62fbdf015e73ac819352ee436e05.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #54: Step 27 - After: When I click on "getByRole('button', { name: 'Create' })" (362ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-27---After-When-I-click-on-getByRole-button-name-Create-362ms--3956549cd504e824b1bacfc087ce2934770cdb43.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #55: Step 28 - Before: Then I wait for element "getByText('New Featured Result for K,')" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-28---Before-Then-I-wait-for-element-getByText-New-Featured-Result-for-K-to-be-visible-with-timeout-10000-eca7c457e9201706de469e352df7466f7a98f973.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #56: Step 28 - After: Then I wait for element "getByText('New Featured Result for K,')" to be visible with timeout 10000 (47ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-28---After-Then-I-wait-for-element-getByText-New-Featured-Result-for-K-to-be-visible-with-timeout-10000-47ms--a54486937dd245f6934085cf5097abfd6470baf3.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #57: Step 29 - Before: When I fill "getByRole('textbox', { name: 'Title *' })" with "Test Title" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-29---Before-When-I-fill-getByRole-textbox-name-Title-with-Test-Title--a8a99d6b56efa29d08bca24ceff604609976ff38.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #58: Step 29 - After: When I fill "getByRole('textbox', { name: 'Title *' })" with "Test Title" (229ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-29---After-When-I-fill-getByRole-textbox-name-Title-with-Test-Title-229ms--5e2ab7f14f487a6af872ce0c560d69f1c9bff221.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #59: Step 30 - Before: And I click on "getByRole('textbox', { name: 'Description' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-30---Before-And-I-click-on-getByRole-textbox-name-Description--e61ead3a03a4bbcc8a47f4872d18708eab216c3f.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #60: Step 30 - After: And I click on "getByRole('textbox', { name: 'Description' })" (168ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-30---After-And-I-click-on-getByRole-textbox-name-Description-168ms--245e13188e6a0de3b343ea72b51b77a968f8fb2c.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #61: Step 31 - Before: And I fill "getByRole('textbox', { name: 'Description' })" with "Test Description" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-31---Before-And-I-fill-getByRole-textbox-name-Description-with-Test-Description--1fbed80c2c7d54f270046552a90ddc4a2ec9d616.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #62: Step 31 - After: And I fill "getByRole('textbox', { name: 'Description' })" with "Test Description" (203ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-31---After-And-I-fill-getByRole-textbox-name-Description-with-Test-Description-203ms--07d3214ce7a76d1b386c38da2e721f406bec396a.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #63: Step 32 - Before: And I fill "getByRole('textbox', { name: 'Url', exact: true })" with "https://www.google.com" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-32---Before-And-I-fill-getByRole-textbox-name-Url-exact-true-with-https-www-google-com--0fac4f7246be7e4e5aa70c3ebc8e44578a03b7e6.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #64: Step 32 - After: And I fill "getByRole('textbox', { name: 'Url', exact: true })" with "https://www.google.com" (254ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-32---After-And-I-fill-getByRole-textbox-name-Url-exact-true-with-https-www-google-com-254ms--46807cbf42d2b29114b3eaa616ba1fdf37145268.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #65: Step 33 - Before: And I fill "getByRole('textbox', { name: 'Search terms (provide a comma' })" with "test, terms" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-33---Before-And-I-fill-getByRole-textbox-name-Search-terms-provide-a-comma-with-test-terms--4cafcc584a163ab6d2bd0862f1342ece2dc39716.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #66: Step 33 - After: And I fill "getByRole('textbox', { name: 'Search terms (provide a comma' })" with "test, terms" (276ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-33---After-And-I-fill-getByRole-textbox-name-Search-terms-provide-a-comma-with-test-terms-276ms--f60507a3cb842edb85c6b7e000c6e82799dae553.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #67: Step 34 - Before: And I fill "getByRole('textbox', { name: 'Comments' })" with "Test Comments" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-34---Before-And-I-fill-getByRole-textbox-name-Comments-with-Test-Comments--45a71673334d9de37457af5abfdaf5b05a9feaf7.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #68: Step 34 - After: And I fill "getByRole('textbox', { name: 'Comments' })" with "Test Comments" (147ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-34---After-And-I-fill-getByRole-textbox-name-Comments-with-Test-Comments-147ms--7f4f47fe2f8e3a362086ed7d2868f406f317f933.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #69: Step 35 - Before: Then element "getByRole('button', { name: 'Publish' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-35---Before-Then-element-getByRole-button-name-Publish-should-be-visible-6835b491a677a33dc1dbe5e4a670d81df2d9549d.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #70: Step 35 - After: Then element "getByRole('button', { name: 'Publish' })" should be visible (49ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-35---After-Then-element-getByRole-button-name-Publish-should-be-visible-49ms--b7b326176d3bf4c8b96d756100f8491b787d6a3e.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #71: Step 36 - Before: And element "getByRole('button', { name: 'Create' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-36---Before-And-element-getByRole-button-name-Create-should-be-visible-d9d88089f52ad99cc5f72dcb602d44a16ef8e928.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #72: Step 36 - After: And element "getByRole('button', { name: 'Create' })" should be visible (42ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-36---After-And-element-getByRole-button-name-Create-should-be-visible-42ms--e7eafd2cebeccbaeefafc28edf965e6266f6451c.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #73: Step 37 - Before: When I click on "getByRole('button', { name: 'Create' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-37---Before-When-I-click-on-getByRole-button-name-Create--7bd7ca021991cd700acc633397bdba4239f7dff8.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #74: Step 37 - After: When I click on "getByRole('button', { name: 'Create' })" (206ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-37---After-When-I-click-on-getByRole-button-name-Create-206ms--29cb6afa95e3fc2f0aca37d72282e7c5b6c473f6.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #75: Step 38 - Before: Then I wait for element "getByRole('heading', { name: 'Confirm creating request!' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-38---Before-Then-I-wait-for-element-getByRole-heading-name-Confirm-creating-request-to-be-visible-with-timeout-10000-b1e831114dd4be67ef4bd22f426ffb2ae83e5790.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #76: Step 38 - After: Then I wait for element "getByRole('heading', { name: 'Confirm creating request!' })" to be visible with timeout 10000 (32ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-38---After-Then-I-wait-for-element-getByRole-heading-name-Confirm-creating-request-to-be-visible-with-timeout-10000-32ms--15d60d077a173e402e4b0239499df1579c599d79.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #77: Step 39 - Before: And element "getByRole('button', { name: 'No' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-39---Before-And-element-getByRole-button-name-No-should-be-visible-d2f9662249f127b1e1f8e524693a2527cecc7a76.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #78: Step 39 - After: And element "getByRole('button', { name: 'No' })" should be visible (36ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-39---After-And-element-getByRole-button-name-No-should-be-visible-36ms--defa640cf72353ab68fe453e8407f157d70fa765.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #79: Step 40 - Before: And element "getByRole('button', { name: 'Yes' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-40---Before-And-element-getByRole-button-name-Yes-should-be-visible-ba2aee08fde3833a3aa0d46271b5927c9110880b.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #80: Step 40 - After: And element "getByRole('button', { name: 'Yes' })" should be visible (38ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-40---After-And-element-getByRole-button-name-Yes-should-be-visible-38ms--8013f6409bef3675f0807165fd3669a25fd4a7ab.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #81: Step 41 - Before: When I click on "getByRole('button', { name: 'Yes' })" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-41---Before-When-I-click-on-getByRole-button-name-Yes--d693cbfb0afa60e2b3a713f562d8a9f41ba8b866.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #82: Step 41 - After: When I click on "getByRole('button', { name: 'Yes' })" (167ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-41---After-When-I-click-on-getByRole-button-name-Yes-167ms--c1f0dd6d17ec8e1f384eb22e83edd0292c4e2023.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #83: Step 42 - Before: Then I wait for element "getByRole('heading', { name: 'Created request' })" to be visible with timeout 10000 (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-42---Before-Then-I-wait-for-element-getByRole-heading-name-Created-request-to-be-visible-with-timeout-10000-2b12609c6d42a78672038a34622f7a49434ba900.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #84: Step 42 - After: Then I wait for element "getByRole('heading', { name: 'Created request' })" to be visible with timeout 10000 (2579ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-42---After-Then-I-wait-for-element-getByRole-heading-name-Created-request-to-be-visible-with-timeout-10000-2579ms--3f11a2b503fdfedc515de2b911f5f1237631ecd7.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #85: Step 43 - Before: And element "getByText('Successfully created')" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-43---Before-And-element-getByText-Successfully-created-should-be-visible-3268e5be292811ac0a7b860d648a17e334a4430b.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #86: Step 43 - After: And element "getByText('Successfully created')" should be visible (44ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-43---After-And-element-getByText-Successfully-created-should-be-visible-44ms--455c0eb52bef5c93c2a36c2366bd605e3a8a2558.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #87: Step 44 - Before: And element "getByRole('button', { name: 'Close' })" should be visible (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-44---Before-And-element-getByRole-button-name-Close-should-be-visible-557a3cd94590df52634cc859295f237397c27adf.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #88: Step 44 - After: And element "getByRole('button', { name: 'Close' })" should be visible (30ms) (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-44---After-And-element-getByRole-button-name-Close-should-be-visible-30ms--e5f56bdf7a2f80ab6b787d7499222644e15dcbaf.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #89: Step 45 - Before: Then the "locator('.MuiChip-label').first()" element should have exact text "FeaturedResult" (image/png) 
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-45---Before-Then-the-locator-MuiChip-label-first-element-should-have-exact-text-FeaturedResult--25d3b624d1f7bdc9d5541fae7e1d922380c01a38.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #90: Failure Screenshot (image/png) ─────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Failure-Screenshot-8bb9cb48c026463c729a9ab3c7ce68e5cf4ff1b8.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #91: screenshot (image/png) ─────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #92: video (video/webm) ─────────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\error-context.md

    attachment #94: trace (application/zip) ────────────────────────────────────────────────────────
    test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\trace.zip
    Usage:

        npx playwright show-trace test-results\playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[🎬 Starting scenario: TC0101- Login and Check the Default Data Source Order
📁 Feature: Self Service Featured Results
🏷️ Tags: @combined, @self-service, @fr
📋 Step 1/47: Given I navigate to URL "https://es-settings-staging.kpmg.com/"
✅ Step completed in 3301ms
📋 Step 2/47: Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000
✅ Step completed in 6899ms
📋 Step 3/47: When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>"
✅ Step completed in 138ms
📋 Step 4/47: And I click on "getByRole('button', { name: 'Next' })"
✅ Step completed in 136ms
📋 Step 5/47: Then I wait for element "getByRole('button', { name: 'Settings' })" to be visible with timeout 60000
✅ Step completed in 13285ms
📋 Step 6/47: And I wait for element "getByRole('button', { name: 'Create a request on behalf of' })" to be visible with timeout 30000
✅ Step completed in 3760ms
📋 Step 7/47: And I wait for page title to contain "KPMG Find - Settings"
✅ Step completed in 10ms
📋 Step 8/47: When I click on "getByRole('button', { name: 'Create a request on behalf of' })"
✅ Step completed in 231ms
📋 Step 9/47: Then I wait for element "getByRole('heading', { name: 'Create a requests on behalf' })" to be visible with timeout 10000
✅ Step completed in 54ms
📋 Step 10/47: Then the "locator('#displayName-label')" element should have exact text "Selected User"
✅ Step completed in 71ms
📋 Step 11/47: And element "getByRole('combobox', { name: 'FeaturedResult' })" should be visible
✅ Step completed in 51ms
📋 Step 12/47: And element "getByRole('button', { name: 'Cancel' })" should be visible
✅ Step completed in 41ms
📋 Step 13/47: When I click on "getByRole('combobox', { name: 'FeaturedResult' })"
✅ Step completed in 168ms
📋 Step 14/47: Then I wait for "locator('#menu-').getByText('FeaturedResult')" to be visible with timeout 30000
✅ Step completed in 42ms
📋 Step 15/47: When I click on "locator('#menu-').getByText('FeaturedResult')"
✅ Step completed in 113ms
📋 Step 16/47: When I click on "getByRole('textbox', { name: 'Selected User' })"
✅ Step completed in 177ms
📋 Step 17/47: Then I wait for element "getByRole('heading', { name: 'Select user for the request' })" to be visible with timeout 10000
✅ Step completed in 49ms
📋 Step 18/47: And element "getByRole('dialog', { name: 'Select user for the request' }).locator('svg')" should be visible
✅ Step completed in 50ms
📋 Step 19/47: And element "getByRole('searchbox', { name: 'description' })" should be visible
✅ Step completed in 42ms
📋 Step 20/47: When I fill "getByRole('searchbox', { name: 'description' })" with "<EMAIL>"
✅ Step completed in 103ms
📋 Step 21/47: Then I wait for element "getByLabel('K, Jagannatha')" to be visible with timeout 10000
✅ Step completed in 2039ms
📋 Step 22/47: And I wait for element "getByLabel('<EMAIL>')" to be visible with timeout 10000
✅ Step completed in 35ms
📋 Step 23/47: When I click on "getByLabel('K, Jagannatha')"
✅ Step completed in 154ms
📋 Step 24/47: Then I wait for element "getByRole('button', { name: 'Select' })" to be visible with timeout 10000
✅ Step completed in 39ms
📋 Step 25/47: When I click on "getByRole('button', { name: 'Select' })"
✅ Step completed in 142ms
📋 Step 26/47: Then I wait for element "getByRole('button', { name: 'Create' })" to be visible with timeout 10000
✅ Step completed in 44ms
📋 Step 27/47: When I click on "getByRole('button', { name: 'Create' })"
✅ Step completed in 362ms
📋 Step 28/47: Then I wait for element "getByText('New Featured Result for K,')" to be visible with timeout 10000
✅ Step completed in 47ms
📋 Step 29/47: When I fill "getByRole('textbox', { name: 'Title *' })" with "Test Title"
✅ Step completed in 229ms
📋 Step 30/47: And I click on "getByRole('textbox', { name: 'Description' })"
✅ Step completed in 168ms
📋 Step 31/47: And I fill "getByRole('textbox', { name: 'Description' })" with "Test Description"
✅ Step completed in 203ms
📋 Step 32/47: And I fill "getByRole('textbox', { name: 'Url', exact: true })" with "https://www.google.com"
✅ Step completed in 254ms
📋 Step 33/47: And I fill "getByRole('textbox', { name: 'Search terms (provide a comma' })" with "test, terms"
✅ Step completed in 276ms
📋 Step 34/47: And I fill "getByRole('textbox', { name: 'Comments' })" with "Test Comments"
✅ Step completed in 147ms
📋 Step 35/47: Then element "getByRole('button', { name: 'Publish' })" should be visible
✅ Step completed in 49ms
📋 Step 36/47: And element "getByRole('button', { name: 'Create' })" should be visible
✅ Step completed in 42ms
📋 Step 37/47: When I click on "getByRole('button', { name: 'Create' })"
✅ Step completed in 206ms
📋 Step 38/47: Then I wait for element "getByRole('heading', { name: 'Confirm creating request!' })" to be visible with timeout 10000
✅ Step completed in 32ms
📋 Step 39/47: And element "getByRole('button', { name: 'No' })" should be visible
✅ Step completed in 36ms
📋 Step 40/47: And element "getByRole('button', { name: 'Yes' })" should be visible
✅ Step completed in 38ms
📋 Step 41/47: When I click on "getByRole('button', { name: 'Yes' })"
✅ Step completed in 167ms
📋 Step 42/47: Then I wait for element "getByRole('heading', { name: 'Created request' })" to be visible with timeout 10000
✅ Step completed in 2579ms
📋 Step 43/47: And element "getByText('Successfully created')" should be visible
✅ Step completed in 44ms
📋 Step 44/47: And element "getByRole('button', { name: 'Close' })" should be visible
✅ Step completed in 30ms
📋 Step 45/47: Then the "locator('.MuiChip-label').first()" element should have exact text "FeaturedResult"
[[31merror[39m]: ❌ Step failed: Then the "locator('.MuiChip-label').first()" element should have exact text "FeaturedResult"

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--9f5ae14be54d00e3a62c2b16aaf7cc1484e376f8.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-3301ms--a1a087f0e3c500721ff9ae627164f2a156e6def3.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-1d25b4a7f9215540528bcae43fdce44b2e129411.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-6899ms--73617745d97bb32ec3dc8ae51580cc93e4a45228.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com--316da82eef654cae4ad12d47f8fe1ffa375efaa6.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagannathak-kpmg-com-138ms--4a9f4d18cec09338fddd537da3174525295c5e1c.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-4---Before-And-I-click-on-getByRole-button-name-Next--996138976132e5a6f4d8dbc56204b69cf5c74dd9.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-4---After-And-I-click-on-getByRole-button-name-Next-136ms--3f82437afad13e30321af3d78ac8f5fa484586c5.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-b7eb0d29174c8085f8fb05af67b2219b6e33c240.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-5---After-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-13285ms--3dd4df820ca306c48c56e1222ae6fb296e3fa9cb.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-6---Before-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-9a62bc5c83bd200eb2a7dff944a05f818f65627a.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-6---After-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-3760ms--17fc87cf7487f9ec6179fdd411bb5055ed61bea4.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-7---Before-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings--e0bfffde38f7f27a22628c6df8759960773211f9.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-7---After-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings-10ms--4b83bbf3501667bb17e22b4c76e9fb6dc6462bf1.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-8---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--589f5cbca4ff4d2eb36011127d110c26f4ff274a.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-8---After-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of-231ms--03f7be8836af0bea3f8d59039c40bcb3f07cf292.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-9---Before-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-04edb0a85796d1d977f53b8c4037f0ff4bbd750a.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-9---After-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-54ms--c971768b63e560fabc0ff2286d7a76cb56abac01.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-10---Before-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User--165c17b6050f87ae4a620d027e9fd607edf2912b.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-10---After-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User-71ms--feb725ac02ac5f75d4bed7dedb852bf3829125f9.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-11---Before-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-f466302481ff8688838ef58344d6d1badc735b93.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-11---After-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-51ms--f84da7efb281bbb123b9457fafd025189069862f.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-12---Before-And-element-getByRole-button-name-Cancel-should-be-visible-3dd38e4bd0b48ac6cd8e82e706f3ceaa7cb695e4.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-12---After-And-element-getByRole-button-name-Cancel-should-be-visible-41ms--58ad1c6ff6ad0e844b8e1bb1067a683d738f6616.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-13---Before-When-I-click-on-getByRole-combobox-name-FeaturedResult--b44ff2847df8960e31125198a9f555b9005c3f2c.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-13---After-When-I-click-on-getByRole-combobox-name-FeaturedResult-168ms--f94b88f5efa8be97575230b453269d0c8c81733e.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-14---Before-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-b63538221cab462f800c2d0ec0aa353bd3fd08f7.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-14---After-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-42ms--00989910ab77e431a1340290b365d1acde0c36d1.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-15---Before-When-I-click-on-locator-menu--getByText-FeaturedResult--d3599577eff0509e5507af39e850dad2e5d8d263.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-15---After-When-I-click-on-locator-menu--getByText-FeaturedResult-113ms--9875c948c18489dbe34e5ed4b757d4f451df60d2.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-16---Before-When-I-click-on-getByRole-textbox-name-Selected-User--e7453252d51fec73a60460d99d29ff0cc4471d0e.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-16---After-When-I-click-on-getByRole-textbox-name-Selected-User-177ms--b9914e79665458f3c1b4e8d3130e0144e074bd60.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-17---Before-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-081a4f6989bf3959043643f2ee92f933d294559d.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-17---After-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-49ms--2d5b1362c025503f2b83d8744564bd6523341c42.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-18---Before-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-c8cb60666241e60459e04119164baf19ede36dec.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-18---After-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-50ms--0b241fc56d9a2dbbfadd6b051a7b40c998e51f97.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-19---Before-And-element-getByRole-searchbox-name-description-should-be-visible-a3728c020b254f2103b34b0dc6af2b8cd2f29e7e.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-19---After-And-element-getByRole-searchbox-name-description-should-be-visible-42ms--956e1319ff9389a01034dc21c463ebce2e24301c.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-20---Before-When-I-fill-getByRole-searchbox-name-description-with-jagannathak-kpmg-com--6c8452e9192231788e4c5f1b13f164438f545565.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-20---After-When-I-fill-getByRole-searchbox-name-description-with-jagannathak-kpmg-com-103ms--4229ab33ae24538a749e13b5cc8fbe2b09cb9835.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-21---Before-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-b84c83400fb05aee3210d524537b31cf2168e238.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-21---After-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-2039ms--d4c4b0791a0d6e6f73a25da980f328fbdc70b5aa.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-22---Before-And-I-wait-for-element-getByLabel-jagannathak-kpmg-com-to-be-visible-with-timeout-10000-f62adb5618e58a83aa622f261fe25140be482880.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-22---After-And-I-wait-for-element-getByLabel-jagannathak-kpmg-com-to-be-visible-with-timeout-10000-35ms--b058dc2951ccf78260cf3f1a942f7e24e4da7322.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-23---Before-When-I-click-on-getByLabel-K-Jagannatha--45924ca3f70ae46b34dd928e2b059beb8b8cb12d.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-23---After-When-I-click-on-getByLabel-K-Jagannatha-154ms--634c8214023995af38bd6fcbb95f2c7f73bfeae2.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-24---Before-Then-I-wait-for-element-getByRole-button-name-Select-to-be-visible-with-timeout-10000-7fca1bf9d98139bddf1fe4675a4490de6770efec.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-24---After-Then-I-wait-for-element-getByRole-button-name-Select-to-be-visible-with-timeout-10000-39ms--7d54beb71eec6671431e2b2d2c6eeb87fbfd5b8d.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-25---Before-When-I-click-on-getByRole-button-name-Select--400568b0d60065a269429e72fe5e133e86c46636.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-25---After-When-I-click-on-getByRole-button-name-Select-142ms--d696b7e8bce66eb0c769f7f4d83c5147db7d296e.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-26---Before-Then-I-wait-for-element-getByRole-button-name-Create-to-be-visible-with-timeout-10000-b66a5d42c0b9a82dafe8f1b46714d7078892f182.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-26---After-Then-I-wait-for-element-getByRole-button-name-Create-to-be-visible-with-timeout-10000-44ms--2001504c604814f105db554e4d183e4a4a125e6b.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-27---Before-When-I-click-on-getByRole-button-name-Create--596b90d80a4e62fbdf015e73ac819352ee436e05.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-27---After-When-I-click-on-getByRole-button-name-Create-362ms--3956549cd504e824b1bacfc087ce2934770cdb43.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-28---Before-Then-I-wait-for-element-getByText-New-Featured-Result-for-K-to-be-visible-with-timeout-10000-eca7c457e9201706de469e352df7466f7a98f973.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-28---After-Then-I-wait-for-element-getByText-New-Featured-Result-for-K-to-be-visible-with-timeout-10000-47ms--a54486937dd245f6934085cf5097abfd6470baf3.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-29---Before-When-I-fill-getByRole-textbox-name-Title-with-Test-Title--a8a99d6b56efa29d08bca24ceff604609976ff38.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-29---After-When-I-fill-getByRole-textbox-name-Title-with-Test-Title-229ms--5e2ab7f14f487a6af872ce0c560d69f1c9bff221.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-30---Before-And-I-click-on-getByRole-textbox-name-Description--e61ead3a03a4bbcc8a47f4872d18708eab216c3f.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-30---After-And-I-click-on-getByRole-textbox-name-Description-168ms--245e13188e6a0de3b343ea72b51b77a968f8fb2c.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-31---Before-And-I-fill-getByRole-textbox-name-Description-with-Test-Description--1fbed80c2c7d54f270046552a90ddc4a2ec9d616.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-31---After-And-I-fill-getByRole-textbox-name-Description-with-Test-Description-203ms--07d3214ce7a76d1b386c38da2e721f406bec396a.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-32---Before-And-I-fill-getByRole-textbox-name-Url-exact-true-with-https-www-google-com--0fac4f7246be7e4e5aa70c3ebc8e44578a03b7e6.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-32---After-And-I-fill-getByRole-textbox-name-Url-exact-true-with-https-www-google-com-254ms--46807cbf42d2b29114b3eaa616ba1fdf37145268.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-33---Before-And-I-fill-getByRole-textbox-name-Search-terms-provide-a-comma-with-test-terms--4cafcc584a163ab6d2bd0862f1342ece2dc39716.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-33---After-And-I-fill-getByRole-textbox-name-Search-terms-provide-a-comma-with-test-terms-276ms--f60507a3cb842edb85c6b7e000c6e82799dae553.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-34---Before-And-I-fill-getByRole-textbox-name-Comments-with-Test-Comments--45a71673334d9de37457af5abfdaf5b05a9feaf7.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-34---After-And-I-fill-getByRole-textbox-name-Comments-with-Test-Comments-147ms--7f4f47fe2f8e3a362086ed7d2868f406f317f933.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-35---Before-Then-element-getByRole-button-name-Publish-should-be-visible-6835b491a677a33dc1dbe5e4a670d81df2d9549d.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-35---After-Then-element-getByRole-button-name-Publish-should-be-visible-49ms--b7b326176d3bf4c8b96d756100f8491b787d6a3e.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-36---Before-And-element-getByRole-button-name-Create-should-be-visible-d9d88089f52ad99cc5f72dcb602d44a16ef8e928.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-36---After-And-element-getByRole-button-name-Create-should-be-visible-42ms--e7eafd2cebeccbaeefafc28edf965e6266f6451c.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-37---Before-When-I-click-on-getByRole-button-name-Create--7bd7ca021991cd700acc633397bdba4239f7dff8.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-37---After-When-I-click-on-getByRole-button-name-Create-206ms--29cb6afa95e3fc2f0aca37d72282e7c5b6c473f6.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-38---Before-Then-I-wait-for-element-getByRole-heading-name-Confirm-creating-request-to-be-visible-with-timeout-10000-b1e831114dd4be67ef4bd22f426ffb2ae83e5790.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-38---After-Then-I-wait-for-element-getByRole-heading-name-Confirm-creating-request-to-be-visible-with-timeout-10000-32ms--15d60d077a173e402e4b0239499df1579c599d79.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-39---Before-And-element-getByRole-button-name-No-should-be-visible-d2f9662249f127b1e1f8e524693a2527cecc7a76.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-39---After-And-element-getByRole-button-name-No-should-be-visible-36ms--defa640cf72353ab68fe453e8407f157d70fa765.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-40---Before-And-element-getByRole-button-name-Yes-should-be-visible-ba2aee08fde3833a3aa0d46271b5927c9110880b.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-40---After-And-element-getByRole-button-name-Yes-should-be-visible-38ms--8013f6409bef3675f0807165fd3669a25fd4a7ab.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-41---Before-When-I-click-on-getByRole-button-name-Yes--d693cbfb0afa60e2b3a713f562d8a9f41ba8b866.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-41---After-When-I-click-on-getByRole-button-name-Yes-167ms--c1f0dd6d17ec8e1f384eb22e83edd0292c4e2023.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-42---Before-Then-I-wait-for-element-getByRole-heading-name-Created-request-to-be-visible-with-timeout-10000-2b12609c6d42a78672038a34622f7a49434ba900.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-42---After-Then-I-wait-for-element-getByRole-heading-name-Created-request-to-be-visible-with-timeout-10000-2579ms--3f11a2b503fdfedc515de2b911f5f1237631ecd7.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-43---Before-And-element-getByText-Successfully-created-should-be-visible-3268e5be292811ac0a7b860d648a17e334a4430b.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-43---After-And-element-getByText-Successfully-created-should-be-visible-44ms--455c0eb52bef5c93c2a36c2366bd605e3a8a2558.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-44---Before-And-element-getByRole-button-name-Close-should-be-visible-557a3cd94590df52634cc859295f237397c27adf.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-44---After-And-element-getByRole-button-name-Close-should-be-visible-30ms--e5f56bdf7a2f80ab6b787d7499222644e15dcbaf.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Step-45---Before-Then-the-locator-MuiChip-label-first-element-should-have-exact-text-FeaturedResult--25d3b624d1f7bdc9d5541fae7e1d922380c01a38.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\attachments\Failure-Screenshot-8bb9cb48c026463c729a9ab3c7ce68e5cf4ff1b8.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\test-failed-1.png]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\video.webm]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\error-context.md]]

[[ATTACHMENT|playwright-data\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\trace.zip]]
]]>
</system-out>
<system-err>
<![CDATA[❌ Scenario failed: TC0101- Login and Check the Default Data Source Order expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 102 elements:
    1) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('.MuiChip-label').first()
    2) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()
    3) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">terms</span> aka getByText('terms', { exact: true })
    4) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka locator('td:nth-child(10) > div > .MuiButtonBase-root').first()
    5) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()
    6) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">AdWord</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')
    7) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">gggggggggggggggg</span> aka getByText('gggggggggggggggg')
    8) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka locator('tr:nth-child(3) > td:nth-child(10) > div > .MuiButtonBase-root')
    9) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')
    10) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('tr:nth-child(4) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')
    ...

Call log:
[2m  - expect.toHaveText with timeout 30000ms[22m
[2m  - waiting for locator('.MuiChip-label')[22m

    at Object.handler [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\step-definition-registry.ts:154:31[90m)[39m
    at StepDefinitionRegistry.executeStep [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\step-definition-registry.ts:361:23[90m)[39m
    at PlaywrightCucumberRunner.executeStep [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-cucumber-runner.ts:196:31[90m)[39m
    at PlaywrightCucumberRunner.executeScenario [90m(C:\workspace\Playwright_Automation_Framework\[39msrc\core\playwright-cucumber-runner.ts:231:20[90m)[39m
    at [90mC:\workspace\Playwright_Automation_Framework\[39mtests\cucumber-wrapper.spec.ts:45:9 {
  [[32mSymbol(step)[39m]: {
    stepId: [32m'expect@231'[39m,
    category: [32m'expect'[39m,
    title: [32m'expect.toHaveText'[39m,
    params: { expected: [32m'FeaturedResult'[39m },
    infectParentStepsWithError: [90mundefined[39m,
    boxedStack: [90mundefined[39m,
    location: {
      file: [32m'C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts'[39m,
      line: [33m154[39m,
      column: [33m31[39m,
      function: [32m'Object.handler'[39m
    },
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'expect@231'[39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1749619908008[39m,
    error: {
      message: [32m"Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 102 elements:\n"[39m +
        [32m`    1) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('.MuiChip-label').first()\n`[39m +
        [32m`    2) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()\n`[39m +
        [32m`    3) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">terms</span> aka getByText('terms', { exact: true })\n`[39m +
        [32m`    4) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka locator('td:nth-child(10) > div > .MuiButtonBase-root').first()\n`[39m +
        [32m`    5) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()\n`[39m +
        [32m`    6) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">AdWord</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n`[39m +
        [32m`    7) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">gggggggggggggggg</span> aka getByText('gggggggggggggggg')\n`[39m +
        [32m`    8) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka locator('tr:nth-child(3) > td:nth-child(10) > div > .MuiButtonBase-root')\n`[39m +
        [32m`    9) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')\n`[39m +
        [32m`    10) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('tr:nth-child(4) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n`[39m +
        [32m'    ...\n'[39m +
        [32m'\n'[39m +
        [32m'Call log:\n'[39m +
        [32m'\x1B[2m  - expect.toHaveText with timeout 30000ms\x1B[22m\n'[39m +
        [32m"\x1B[2m  - waiting for locator('.MuiChip-label')\x1B[22m\n"[39m,
      stack: [32m"Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 102 elements:\n"[39m +
        [32m`    1) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('.MuiChip-label').first()\n`[39m +
        [32m`    2) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()\n`[39m +
        [32m`    3) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">terms</span> aka getByText('terms', { exact: true })\n`[39m +
        [32m`    4) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka locator('td:nth-child(10) > div > .MuiButtonBase-root').first()\n`[39m +
        [32m`    5) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()\n`[39m +
        [32m`    6) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">AdWord</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n`[39m +
        [32m`    7) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">gggggggggggggggg</span> aka getByText('gggggggggggggggg')\n`[39m +
        [32m`    8) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka locator('tr:nth-child(3) > td:nth-child(10) > div > .MuiButtonBase-root')\n`[39m +
        [32m`    9) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')\n`[39m +
        [32m`    10) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('tr:nth-child(4) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n`[39m +
        [32m'    ...\n'[39m +
        [32m'\n'[39m +
        [32m'Call log:\n'[39m +
        [32m'\x1B[2m  - expect.toHaveText with timeout 30000ms\x1B[22m\n'[39m +
        [32m"\x1B[2m  - waiting for locator('.MuiChip-label')\x1B[22m\n"[39m +
        [32m'\n'[39m +
        [32m'    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:154:31)\n'[39m +
        [32m'    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:361:23)\n'[39m +
        [32m'    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:196:31)\n'[39m +
        [32m'    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:231:20)\n'[39m +
        [32m'    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9'[39m,
      cause: [90mundefined[39m
    }
  }
}
]]>
</system-err>
</testcase>
</testsuite>
</testsuites>