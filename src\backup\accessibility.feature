@accessibility
Feature: Accessibility Testing
  As a QA engineer
  I want to test the accessibility of web applications
  So that I can ensure they are usable by people with disabilities

  Background:
    Given I navigate to "https://demo.playwright.dev/todomvc/#/"
    And I wait for page to load

  @a11y
  Scenario: Test overall page accessibility
    # Test the entire page for accessibility issues
    Then the page should be accessible

  @a11y
  Scenario: Test specific element accessibility
    # Add a todo item first
    When I wait for ".new-todo" to be visible
    And I fill ".new-todo" with "Buy groceries"
    And I press "Enter"
    Then ".todo-list li:last-child" should be visible

    # Test the specific todo item for accessibility
    Then the element ".todo-list li:last-child" should be accessible

  @a11y
  Scenario: Test accessibility for critical issues only
    # Test only for critical accessibility issues
    Then the page should have no accessibility violations with impact "critical"

  @a11y
  Scenario: Test specific accessibility rules
    # Test for specific WCAG rules
    Then the page should have no accessibility violations for rules:
      | color-contrast |
      | label          |
      | button-name    |
      | image-alt      |
      | link-name      |
      | heading-order  |
      | html-has-lang  |
      | meta-viewport  |

  @a11y
  Scenario: Test accessibility after interaction
    # Add a todo item
    When I wait for ".new-todo" to be visible
    And I fill ".new-todo" with "Buy groceries"
    And I press "Enter"
    Then ".todo-list li:last-child" should be visible

    # Complete the todo item
    When I click on ".todo-list li:last-child .toggle"
    Then ".todo-list li.completed" should be visible

    # Test accessibility after interaction
    Then the page should be accessible

  @a11y
  Scenario: Test accessibility of filtered views
    # Add multiple todo items
    When I wait for ".new-todo" to be visible
    And I fill ".new-todo" with "Task 1"
    And I press "Enter"
    And I fill ".new-todo" with "Task 2"
    And I press "Enter"
    And I wait for ".todo-list li:nth-child(2)" to be visible

    # Complete one item
    When I click on ".todo-list li:first-child .toggle"
    Then ".todo-list li:first-child.completed" should be visible

    # Test active filter
    When I click on "a[href='#/active']"
    Then the page should be accessible

    # Test completed filter
    When I click on "a[href='#/completed']"
    Then the page should be accessible
