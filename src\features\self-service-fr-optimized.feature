@combined @self-service @fr @optimized
Feature: Self Service Featured Results - Optimized
  As a KPMG user
  I want to create and verify featured results
  So that I can manage search results effectively

  Background:
    Given I navigate to URL "https://es-settings-staging.kpmg.com/"
    And I login with Microsoft SSO using "jagan<PERSON><PERSON>@kpmg.com"
    And I wait for the settings page to load

  @smoke @create-featured-result
  Scenario Outline: Create Featured Result with Different Data
    When I create a featured result request with the following details:
      | field        | value        |
      | title        | <title>      |
      | description  | <description>|
      | url          | <url>        |
      | searchTerms  | <terms>      |
      | comments     | <comments>   |
    And I confirm the creation
    Then I should see the featured result in the table with:
      | column      | expectedValue |
      | type        | FeaturedResult|
      | title       | <title>       |
      | description | <description> |
      | url         | <url>         |
      | status      | Submitted     |
      | requestor   | K, Jagannatha |

    Examples:
      | title       | description       | url                    | terms        | comments       |
      | Test Title  | Test Description  | https://www.google.com | test, terms  | Test Comments  |
      | Demo Title  | Demo Description  | https://www.bing.com   | demo, search | Demo Comments  |
      | Sample Data | Sample Info       | https://www.yahoo.com  | sample, test | Sample Notes   |

  @regression @form-validation
  Scenario: Validate Featured Result Form Elements
    When I start creating a featured result request
    Then I should see all required form elements:
      | element                    | type     | required |
      | Title                      | textbox  | true     |
      | Description                | textbox  | false    |
      | Url                        | textbox  | true     |
      | Search terms               | textbox  | false    |
      | Comments                   | textbox  | false    |
      | Publish button             | button   | false    |
      | Create button              | button   | true     |

  @data-driven @table-verification
  Scenario: Verify Multiple Table Entries
    Given I have created featured results with the following data:
      | title    | description    | url                  | terms     | status    |
      | Entry 1  | Description 1  | https://example1.com | term1     | Submitted |
      | Entry 2  | Description 2  | https://example2.com | term2     | Submitted |
      | Entry 3  | Description 3  | https://example3.com | term3     | Submitted |
    Then I should see all entries in the results table

  @workflow @end-to-end
  Scenario: Complete Featured Result Workflow
    When I perform the complete featured result workflow:
      """
      1. Navigate to create request page
      2. Select FeaturedResult type
      3. Choose user for request
      4. Fill form with test data
      5. Submit and confirm
      6. Verify creation success
      7. Validate table entry
      """
    Then the workflow should complete successfully
