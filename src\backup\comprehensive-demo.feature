@demo
Feature: Comprehensive Demo of Framework Capabilities
  As a QA engineer
  I want to use all the pre-defined steps in the framework
  So that I can test any web application efficiently

  Background:
    Given I navigate to "https://the-internet.herokuapp.com/"
    And I wait for page to load

  Scenario: Basic Navigation and Interaction
    # Navigation
    When I click on "a[href='/login']"
    Then the URL should contain "login"
    And the page title should contain "The Internet"

    # Form interactions
    When I wait for "#username" to be visible
    And I fill "#username" with "tomsmith"
    And I fill "#password" with "SuperSecretPassword!"
    And I click on "button[type='submit']"
    Then ".flash.success" should be visible
    And ".flash.success" should contain text "You logged into a secure area"

    # Go back and forward
    When I go back
    Then the URL should contain "login"

    When I go forward
    Then the URL should contain "secure"

    # Refresh page
    When I refresh the page
    # After refresh, we're still on the secure page but the flash message might be different
    Then "h2" should be visible
    And "h2" should contain text "Secure Area"

  Scenario: Checkbox Interactions
    When I click on "a[href='/checkboxes']"
    And I wait for "h3" to be visible
    Then "h3" should have exact text "Checkboxes"

    # Check and uncheck checkboxes
    When I check "input[type='checkbox']:nth-child(1)"
    Then "input[type='checkbox']:nth-child(1)" should be checked

    When I uncheck "input[type='checkbox']:nth-child(3)"
    Then "input[type='checkbox']:nth-child(3)" should not be checked

    # Check again
    When I check "input[type='checkbox']:nth-child(3)"
    Then "input[type='checkbox']:nth-child(3)" should be checked

  Scenario: Dropdown and Select Interactions
    # Navigate to dropdown page
    When I click on "a[href='/dropdown']"
    And I wait for "h3" to be visible
    Then "h3" should have exact text "Dropdown List"

    # Dropdown selection
    When I select "Option 1" from "#dropdown"
    Then "#dropdown" should have value "1"

    When I select "Option 2" from "#dropdown"
    Then "#dropdown" should have value "2"

  Scenario: Form Interactions
    # Navigate to form page
    When I click on "a[href='/login']"
    And I wait for "h2" to be visible

    # Basic form interactions
    When I fill "#username" with "tomsmith"
    And I fill "#password" with "SuperSecretPassword!"
    Then "#username" should have value "tomsmith"
    And "#password" should have value "SuperSecretPassword!"

    # Submit form
    When I click on "button[type='submit']"
    Then ".flash.success" should be visible

  Scenario: Keyboard Interactions
    # Navigate to key presses page
    When I click on "a[href='/key_presses']"
    And I wait for "h3" to be visible

    # Press keys
    When I press "A"
    Then "#result" should contain text "You entered: A"

    When I press "Tab"
    Then "#result" should contain text "You entered: TAB"

  Scenario: Drag and Drop Interactions
    # Navigate to drag and drop page
    When I click on "a[href='/drag_and_drop']"
    And I wait for "h3" to be visible

    # Drag and drop
    When I drag "#column-a" and drop it on "#column-b"
    Then "#column-a header" should contain text "B"
    And "#column-b header" should contain text "A"

  Scenario: Hover Interactions
    # Navigate to hovers page
    When I click on "a[href='/hovers']"
    And I wait for "h3" to be visible

    # Hover over elements - using more specific selectors
    When I hover over ".figure:first-of-type"
    # Use a more specific selector for the figcaption
    Then ".figure:first-of-type .figcaption" should be visible
    And ".figure:first-of-type .figcaption" should contain text "user1"

  Scenario: File Upload
    # Navigate to upload page
    When I click on "a[href='/upload']"
    And I wait for "h3" to be visible

    # Note: In a real test, you would use a real file path
    # When I upload file "path/to/file.txt" to "#file-upload"
    # And I click on "#file-submit"
    # Then "#uploaded-files" should contain text "file.txt"

  Scenario: Wait and Timing
    # Use a simpler example for timing demonstration
    When I click on "a[href='/javascript_alerts']"
    And I wait for "h3" to be visible

    # Click a button and wait for the result
    When I click on "button[onclick='jsAlert()']"
    And I wait for "#result" to be visible

    # Verify the result
    Then "#result" should contain text "You successfully clicked an alert"

    # Demonstrate wait functionality
    When I wait for 2000 milliseconds
    Then "#result" should still be visible

  Scenario: JavaScript Alerts
    # Navigate to JavaScript alerts page
    When I click on "a[href='/javascript_alerts']"
    And I wait for "h3" to be visible

    # Click button to trigger alert
    When I click on "button[onclick='jsAlert()']"
    # Note: Playwright automatically handles alerts by accepting them
    Then "#result" should contain text "You successfully clicked an alert"
