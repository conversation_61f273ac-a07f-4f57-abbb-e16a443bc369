{"version":7,"type":"context-options","origin":"library","browserName":"chromium","options":{"noDefaultViewport":false,"viewport":{"width":1860,"height":900},"ignoreHTTPSErrors":true,"javaScriptEnabled":true,"bypassCSP":false,"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.25 Safari/537.36","locale":"en-US","offline":false,"deviceScaleFactor":1,"isMobile":false,"hasTouch":false,"colorScheme":"light","acceptDownloads":"accept","baseURL":"https://www.google.com","recordVideo":{"dir":"C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\.playwright-artifacts-0","size":{"width":1280,"height":720}},"serviceWorkers":"allow"},"platform":"win32","wallTime":1749627471194,"monotonicTime":3350.617,"sdkLanguage":"javascript","testIdAttributeName":"data-testid","contextId":"browser-context@ba1fe8fdabd81befe7b47deccb4d96e5","channel":"msedge","title":"cucumber-wrapper.spec.ts:34 › Self Service Featured Results › TC0101- Create Featured Result with Test Data"}
{"type":"before","callId":"call@9","startTime":3378.6,"apiName":"browserContext.newPage","class":"BrowserContext","method":"newPage","params":{},"stepId":"pw:api@7","beforeSnapshot":"before@call@9"}
{"type":"event","time":3630.774,"class":"BrowserContext","method":"page","params":{"pageId":"page@e03013794ed73e4636c13d07cb2a6a2c"}}
{"type":"after","callId":"call@9","endTime":3630.947,"result":{"page":"<Page>"},"afterSnapshot":"after@call@9"}
{"type":"before","callId":"call@11","startTime":3666.188,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{"type":"png","fullPage":true},"stepId":"pw:api@8","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","beforeSnapshot":"before@call@11"}
{"type":"frame-snapshot","snapshot":{"callId":"call@11","snapshotName":"before@call@11","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","frameId":"frame@765d8cca12f25a9858457e9a87e6fb5b","frameUrl":"about:blank","html":["HTML",{},["HEAD",{},["BASE",{"href":"about:blank"}]],["BODY"]],"viewport":{"width":1860,"height":900},"timestamp":3682.09,"wallTime":1749627471527,"collectionTime":0.900000000372529,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@11","time":3684.987,"message":"taking page screenshot"}
{"type":"log","callId":"call@11","time":3694.523,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@11","time":3699.116,"message":"fonts loaded"}
{"type":"screencast-frame","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","sha1":"<EMAIL>","width":1860,"height":900,"timestamp":3883.995,"frameSwapWallTime":1749627471717.741}
{"type":"screencast-frame","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","sha1":"<EMAIL>","width":1860,"height":900,"timestamp":3974.187,"frameSwapWallTime":1749627471747.428}
{"type":"after","callId":"call@11","endTime":3976.607,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@11"}
{"type":"frame-snapshot","snapshot":{"callId":"call@11","snapshotName":"after@call@11","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","frameId":"frame@765d8cca12f25a9858457e9a87e6fb5b","frameUrl":"about:blank","html":[[1,3]],"viewport":{"width":1860,"height":900},"timestamp":3978.719,"wallTime":1749627471824,"collectionTime":0.19999999925494194,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@13","startTime":4011.49,"apiName":"locator.evaluate","class":"Frame","method":"waitForSelector","params":{"selector":"internal:role=button[name=\"Create a request on behalf of\"i] >> nth=0","strict":true,"timeout":15000,"state":"attached"},"stepId":"pw:api@10","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","beforeSnapshot":"before@call@13"}
{"type":"frame-snapshot","snapshot":{"callId":"call@13","snapshotName":"before@call@13","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","frameId":"frame@765d8cca12f25a9858457e9a87e6fb5b","frameUrl":"about:blank","html":[[2,3]],"viewport":{"width":1860,"height":900},"timestamp":4013.631,"wallTime":1749627471859,"collectionTime":0.5,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@13","time":4017.771,"message":"waiting for getByRole('button', { name: 'Create a request on behalf of' }).first()"}
{"type":"before","callId":"call@15","startTime":4525.691,"apiName":"locator.click","class":"Frame","method":"click","params":{"selector":"internal:role=button[name=\"Create a request on behalf of\"i]","strict":true},"stepId":"pw:api@11","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","beforeSnapshot":"before@call@15"}
{"type":"frame-snapshot","snapshot":{"callId":"call@15","snapshotName":"before@call@15","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","frameId":"frame@765d8cca12f25a9858457e9a87e6fb5b","frameUrl":"about:blank","html":[[3,3]],"viewport":{"width":1860,"height":900},"timestamp":4528.708,"wallTime":1749627472374,"collectionTime":0.09999999962747097,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@15","time":4529.95,"message":"waiting for getByRole('button', { name: 'Create a request on behalf of' })"}
{"type":"after","callId":"call@13","endTime":19015.305,"error":{"message":"Timeout 15000ms exceeded.","stack":"TimeoutError: Timeout 15000ms exceeded.\n    at ProgressController.run (C:\\workspace\\Playwright_Automation_Framework\\node_modules\\playwright-core\\lib\\server\\progress.js:76:26)\n    at Frame.waitForSelector (C:\\workspace\\Playwright_Automation_Framework\\node_modules\\playwright-core\\lib\\server\\frames.js:647:23)\n    at FrameDispatcher.waitForSelector (C:\\workspace\\Playwright_Automation_Framework\\node_modules\\playwright-core\\lib\\server\\dispatchers\\frameDispatcher.js:82:116)\n    at FrameDispatcher._handleCommand (C:\\workspace\\Playwright_Automation_Framework\\node_modules\\playwright-core\\lib\\server\\dispatchers\\dispatcher.js:94:40)\n    at DispatcherConnection.dispatch (C:\\workspace\\Playwright_Automation_Framework\\node_modules\\playwright-core\\lib\\server\\dispatchers\\dispatcher.js:307:39)","name":"TimeoutError"},"afterSnapshot":"after@call@13"}
{"type":"frame-snapshot","snapshot":{"callId":"call@13","snapshotName":"after@call@13","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","frameId":"frame@765d8cca12f25a9858457e9a87e6fb5b","frameUrl":"about:blank","html":[[4,3]],"viewport":{"width":1860,"height":900},"timestamp":19018.698,"wallTime":1749627486863,"collectionTime":0.09999999962747097,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@15","endTime":19536.159,"error":{"message":"Timeout 15000ms exceeded.","stack":"TimeoutError: Timeout 15000ms exceeded.\n    at ProgressController.run (C:\\workspace\\Playwright_Automation_Framework\\node_modules\\playwright-core\\lib\\server\\progress.js:76:26)\n    at Frame.click (C:\\workspace\\Playwright_Automation_Framework\\node_modules\\playwright-core\\lib\\server\\frames.js:991:23)\n    at FrameDispatcher.click (C:\\workspace\\Playwright_Automation_Framework\\node_modules\\playwright-core\\lib\\server\\dispatchers\\frameDispatcher.js:117:30)\n    at FrameDispatcher._handleCommand (C:\\workspace\\Playwright_Automation_Framework\\node_modules\\playwright-core\\lib\\server\\dispatchers\\dispatcher.js:94:40)\n    at DispatcherConnection.dispatch (C:\\workspace\\Playwright_Automation_Framework\\node_modules\\playwright-core\\lib\\server\\dispatchers\\dispatcher.js:307:39)","name":"TimeoutError"},"afterSnapshot":"after@call@15"}
{"type":"frame-snapshot","snapshot":{"callId":"call@15","snapshotName":"after@call@15","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","frameId":"frame@765d8cca12f25a9858457e9a87e6fb5b","frameUrl":"about:blank","html":[[5,3]],"viewport":{"width":1860,"height":900},"timestamp":19538.686,"wallTime":1749627487384,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@17","startTime":19553.734,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{"type":"png","fullPage":true},"stepId":"pw:api@12","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","beforeSnapshot":"before@call@17"}
{"type":"frame-snapshot","snapshot":{"callId":"call@17","snapshotName":"before@call@17","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","frameId":"frame@765d8cca12f25a9858457e9a87e6fb5b","frameUrl":"about:blank","html":[[6,3]],"viewport":{"width":1860,"height":900},"timestamp":19556.899,"wallTime":1749627487401,"collectionTime":0.2000000011175871,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@17","time":19557.507,"message":"taking page screenshot"}
{"type":"log","callId":"call@17","time":19560.806,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@17","time":19564.166,"message":"fonts loaded"}
{"type":"screencast-frame","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","sha1":"<EMAIL>","width":1860,"height":900,"timestamp":19625.453,"frameSwapWallTime":1749627487452.533}
{"type":"after","callId":"call@17","endTime":19777.101,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@17"}
{"type":"frame-snapshot","snapshot":{"callId":"call@17","snapshotName":"after@call@17","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","frameId":"frame@765d8cca12f25a9858457e9a87e6fb5b","frameUrl":"about:blank","html":[[7,3]],"viewport":{"width":1860,"height":900},"timestamp":19779.891,"wallTime":1749627487625,"collectionTime":0.09999999962747097,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@19","startTime":19806.288,"apiName":"page.screenshot","class":"Page","method":"screenshot","params":{"timeout":5000,"type":"png","fullPage":true,"caret":"initial"},"stepId":"pw:api@15","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","beforeSnapshot":"before@call@19"}
{"type":"frame-snapshot","snapshot":{"callId":"call@19","snapshotName":"before@call@19","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","frameId":"frame@765d8cca12f25a9858457e9a87e6fb5b","frameUrl":"about:blank","html":[[8,3]],"viewport":{"width":1860,"height":900},"timestamp":19808.596,"wallTime":1749627487654,"collectionTime":0.09999999962747097,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@19","time":19809.133,"message":"taking page screenshot"}
{"type":"log","callId":"call@19","time":19811.52,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@19","time":19812.967,"message":"fonts loaded"}
{"type":"screencast-frame","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","sha1":"<EMAIL>","width":1860,"height":900,"timestamp":19875.234,"frameSwapWallTime":1749627487702.698}
{"type":"after","callId":"call@19","endTime":20016.942,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@19"}
{"type":"frame-snapshot","snapshot":{"callId":"call@19","snapshotName":"after@call@19","pageId":"page@e03013794ed73e4636c13d07cb2a6a2c","frameId":"frame@765d8cca12f25a9858457e9a87e6fb5b","frameUrl":"about:blank","html":[[9,3]],"viewport":{"width":1860,"height":900},"timestamp":20020.196,"wallTime":1749627487865,"collectionTime":0.40000000037252903,"resourceOverrides":[],"isMainFrame":true}}
