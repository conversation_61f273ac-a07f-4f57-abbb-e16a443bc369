Feature: TodoMVC Comprehensive Framework Capabilities Demo
  As a QA engineer using the world's most advanced test automation framework
  I want to demonstrate all framework capabilities on a real application
  So that I can showcase the power, intelligence, and simplicity of our solution

  Background: Setup TodoMVC Application
    Given I navigate to "https://todomvc.com/examples/react/"
    And I wait for the page to load
    And I take a screenshot named "todomvc-initial-load"

  @smoke @demo @navigation @accessibility
  Scenario: Framework Initialization and Basic Navigation Demo
    # Demonstrate navigation and accessibility testing
    Then I should see ".todoapp"
    And the page title should contain "TodoMVC"
    And the page should be accessible
    When I take a screenshot named "todomvc-homepage-accessible"
    
    # Demonstrate smart locator resolution
    Then I should see "input.new-todo"
    And I should see ".main"
    And I should see ".footer"

  @demo @data-driven @variables @smart-interaction
  Scenario: Advanced Data Management and Smart Interactions
    # Demonstrate dynamic data generation and variable management
    When I generate random "text" and store as "todoItem1"
    And I generate random "text" and store as "todoItem2"
    And I generate random "uuid" and store as "sessionId"
    And I set variable "testPrefix" to "Framework Demo"
    
    # Demonstrate smart interaction with auto-healing locators
    When I type "${testPrefix}: ${todoItem1}" into ".new-todo"
    And I press "Enter"
    Then I should see ".todo-list li"
    And ".todo-list li:first-child label" should contain text "${testPrefix}"
    
    # Add second todo with different approach
    When I click on ".new-todo"
    And I type "${testPrefix}: ${todoItem2}" into ".new-todo"
    And I press "Enter"
    Then ".todo-count" should contain text "2 items left"
    
    # Store dynamic data for later use
    When I store the text from ".todo-count" as "currentCount"
    And I take a screenshot named "todos-added-${sessionId}"

  @demo @form-interaction @validation @conditional
  Scenario: Comprehensive Form Interactions and Validations
    # Demonstrate various form interactions
    When I type "Learn Playwright Framework" into ".new-todo"
    And I press "Enter"
    And I type "Master Test Automation" into ".new-todo"
    And I press "Enter"
    And I type "Become QA Expert" into ".new-todo"
    And I press "Enter"
    
    # Demonstrate element state validations
    Then ".todo-list li" should be visible
    And ".todo-count" should contain text "3 items left"
    
    # Demonstrate checkbox interactions
    When I click on ".todo-list li:first-child .toggle"
    Then ".todo-list li:first-child" should have class "completed"
    And ".todo-count" should contain text "2 items left"
    
    # Demonstrate conditional logic
    When if ".clear-completed" is visible then I click on it
    Then ".todo-count" should contain text "2 items left"
    
    # Demonstrate hover interactions
    When I hover over ".todo-list li:first-child"
    Then I should see ".todo-list li:first-child .destroy"

  @demo @visual-testing @responsive @cross-browser
  Scenario: Visual Testing and Responsive Design Validation
    # Setup test data
    When I type "Visual Test Item 1" into ".new-todo"
    And I press "Enter"
    And I type "Visual Test Item 2" into ".new-todo"
    And I press "Enter"
    
    # Desktop view visual testing
    When I set browser window size to 1920x1080
    And I wait for 2 seconds
    Then I take a screenshot named "todomvc-desktop-view"
    
    # Tablet view visual testing
    When I set browser window size to 768x1024
    And I wait for 2 seconds
    Then I take a screenshot named "todomvc-tablet-view"
    
    # Mobile view visual testing
    When I set browser window size to 375x667
    And I wait for 2 seconds
    Then I take a screenshot named "todomvc-mobile-view"
    
    # Restore desktop view
    When I set browser window size to 1920x1080
    And I wait for 2 seconds

  @demo @accessibility @wcag @a11y
  Scenario: Comprehensive Accessibility Testing
    # Add test content
    When I type "Accessibility Test Todo" into ".new-todo"
    And I press "Enter"
    
    # Test overall page accessibility
    Then the page should be accessible
    And the page should be accessible according to "wcag21aa" rules
    
    # Test specific component accessibility
    Then ".todoapp" should be accessible
    And ".new-todo" should be accessible
    And ".todo-list" should be accessible
    
    # Test accessibility after interactions
    When I click on ".todo-list li:first-child .toggle"
    Then the page should be accessible
    And I take a screenshot named "accessibility-test-completed"

  @demo @keyboard @mouse @advanced-interaction
  Scenario: Advanced Keyboard and Mouse Interactions
    # Demonstrate keyboard navigation
    When I focus on ".new-todo"
    And I type "Keyboard Navigation Test" into ".new-todo"
    And I press "Enter"
    
    # Demonstrate tab navigation
    When I press "Tab"
    And I press "Space"
    Then ".todo-list li:first-child" should have class "completed"
    
    # Demonstrate mouse interactions
    When I type "Mouse Interaction Test" into ".new-todo"
    And I press "Enter"
    And I hover over ".todo-list li:last-child"
    And I click on ".todo-list li:last-child .destroy"
    Then ".todo-list li" should have count "1"
    
    # Demonstrate double-click editing
    When I double-click on ".todo-list li:first-child label"
    And I clear ".todo-list li:first-child .edit"
    And I type "Edited Todo Item" into ".todo-list li:first-child .edit"
    And I press "Enter"
    Then ".todo-list li:first-child label" should contain text "Edited Todo Item"

  @demo @error-handling @resilience @auto-healing
  Scenario: Error Handling and Auto-Healing Locators Demo
    # Test with potentially changing selectors
    When I type "Error Handling Test" into "input[placeholder='What needs to be done?']"
    And I press "Enter"
    
    # Test alternative locator strategies
    Then I should see "getByText('Error Handling Test')"
    
    # Test resilience with non-existent elements
    When if ".non-existent-element" is visible then I click on it
    Then I should see ".todo-list li"
    
    # Test auto-healing with different locator approaches
    When I click on "role=checkbox"
    Then ".todo-list li:first-child" should have class "completed"
    
    # Test graceful handling of timing issues
    When I wait for ".todo-list" to be visible
    And I wait for 1 seconds
    Then I take a screenshot named "error-handling-demo-completed"

  @demo @filters @state-management
  Scenario: Filter Functionality and State Management
    # Setup test data
    When I type "Active Todo 1" into ".new-todo"
    And I press "Enter"
    And I type "Active Todo 2" into ".new-todo"
    And I press "Enter"
    And I type "Completed Todo" into ".new-todo"
    And I press "Enter"
    
    # Mark one as completed
    When I click on ".todo-list li:last-child .toggle"
    Then ".todo-count" should contain text "2 items left"
    
    # Test All filter (default)
    Then ".todo-list li" should have count "3"
    And I take a screenshot named "filter-all-view"
    
    # Test Active filter
    When I click on "getByText('Active')"
    Then ".todo-list li" should have count "2"
    And I take a screenshot named "filter-active-view"
    
    # Test Completed filter
    When I click on "getByText('Completed')"
    Then ".todo-list li" should have count "1"
    And ".todo-list li:first-child" should have class "completed"
    And I take a screenshot named "filter-completed-view"
    
    # Return to All filter
    When I click on "getByText('All')"
    Then ".todo-list li" should have count "3"

  @demo @performance @monitoring
  Scenario: Performance Monitoring and Resource Management
    # Generate multiple todos to test performance
    When I generate random "number" and store as "todoCount"
    And I set variable "performanceTest" to "Performance Test Item"
    
    # Add multiple todos quickly
    When I type "${performanceTest} 1" into ".new-todo"
    And I press "Enter"
    And I type "${performanceTest} 2" into ".new-todo"
    And I press "Enter"
    And I type "${performanceTest} 3" into ".new-todo"
    And I press "Enter"
    And I type "${performanceTest} 4" into ".new-todo"
    And I press "Enter"
    And I type "${performanceTest} 5" into ".new-todo"
    And I press "Enter"
    
    # Verify performance
    Then ".todo-list li" should have count "5"
    And ".todo-count" should contain text "5 items left"
    And I take a screenshot named "performance-test-completed"

  @demo @bulk-operations @efficiency
  Scenario: Bulk Operations and Framework Efficiency
    # Setup multiple todos
    When I type "Bulk Test 1" into ".new-todo"
    And I press "Enter"
    And I type "Bulk Test 2" into ".new-todo"
    And I press "Enter"
    And I type "Bulk Test 3" into ".new-todo"
    And I press "Enter"
    
    # Test toggle all functionality
    When I click on ".toggle-all"
    Then ".todo-list li:nth-child(1)" should have class "completed"
    And ".todo-list li:nth-child(2)" should have class "completed"
    And ".todo-list li:nth-child(3)" should have class "completed"
    And ".todo-count" should contain text "0 items left"
    
    # Test clear completed
    When I click on ".clear-completed"
    Then ".todo-list li" should have count "0"
    And I should not see ".todo-count"

  @demo @edge-cases @boundary-testing
  Scenario: Edge Cases and Boundary Testing
    # Test empty state
    Then I should not see ".main"
    And I should not see ".footer"
    
    # Test very long todo text
    When I generate random "text" and store as "longText"
    And I type "This is a very long todo item that tests the framework's ability to handle edge cases and boundary conditions: ${longText}" into ".new-todo"
    And I press "Enter"
    Then I should see ".todo-list li"
    
    # Test special characters
    When I type "Special chars: !@#$%^&*()_+-=[]{}|;:,.<>?" into ".new-todo"
    And I press "Enter"
    Then ".todo-list li" should have count "2"
    
    # Test rapid interactions
    When I click on ".todo-list li:first-child .toggle"
    And I click on ".todo-list li:first-child .toggle"
    And I click on ".todo-list li:first-child .toggle"
    Then ".todo-list li:first-child" should have class "completed"

  @demo @comprehensive @end-to-end @showcase
  Scenario Outline: Comprehensive End-to-End Framework Showcase
    # Demonstrate data-driven testing with multiple datasets
    When I type "<todo_text>" into ".new-todo"
    And I press "Enter"
    Then I should see ".todo-list li"
    And ".todo-list li:last-child label" should contain text "<todo_text>"
    
    # Demonstrate conditional operations based on priority
    When if "<priority>" is "high" then I click on ".todo-list li:last-child .toggle"
    
    # Take category-specific screenshots
    And I take a screenshot named "todo-<category>-<priority>"
    
    # Verify accessibility for each scenario
    Then the page should be accessible

    Examples:
      | todo_text                    | priority | category    |
      | Learn Framework Basics       | high     | learning    |
      | Practice Advanced Features   | medium   | practice    |
      | Master Test Automation       | high     | mastery     |
      | Explore Plugin System        | low      | exploration |
      | Create Custom Extensions     | medium   | development |

  @demo @final @integration @complete
  Scenario: Final Integration Test - All Framework Capabilities
    # Initialize with comprehensive setup
    When I generate random "uuid" and store as "testRunId"
    And I set variable "frameworkDemo" to "Complete Framework Demo"
    And I take a screenshot named "final-test-start-${testRunId}"
    
    # Demonstrate all interaction types
    When I type "${frameworkDemo}: Navigation Test" into ".new-todo"
    And I press "Enter"
    And I type "${frameworkDemo}: Interaction Test" into ".new-todo"
    And I press "Enter"
    And I type "${frameworkDemo}: Validation Test" into ".new-todo"
    And I press "Enter"
    
    # Demonstrate state management
    When I click on ".todo-list li:nth-child(2) .toggle"
    Then ".todo-count" should contain text "2 items left"
    
    # Demonstrate advanced interactions
    When I hover over ".todo-list li:first-child"
    And I double-click on ".todo-list li:first-child label"
    And I clear ".todo-list li:first-child .edit"
    And I type "${frameworkDemo}: Edited Item" into ".todo-list li:first-child .edit"
    And I press "Enter"
    
    # Demonstrate filtering
    When I click on "getByText('Active')"
    Then ".todo-list li" should have count "2"
    When I click on "getByText('Completed')"
    Then ".todo-list li" should have count "1"
    When I click on "getByText('All')"
    Then ".todo-list li" should have count "3"
    
    # Final validations
    Then the page should be accessible
    And the page should be accessible according to "wcag21aa" rules
    
    # Store final results
    When I store the text from ".todo-count" as "finalCount"
    And I take a screenshot named "final-test-complete-${testRunId}"
    
    # Comprehensive verification
    Then ".todoapp" should be visible
    And ".todo-list li" should have count "3"
    And I should see ".footer"
