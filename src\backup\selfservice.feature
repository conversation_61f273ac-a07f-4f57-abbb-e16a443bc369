
@combined @self
Feature: Self Service Health Check
  Data Source - Health Check - General
  en
  Background:
    # Store credentials for later use
    Given I store "jagannath<PERSON>@kpmg.com" as "USERNAME"

  Scenario: TC0101- Login
    Given I navigate to URL "https://es-settings-staging.kpmg.com/"
    # Smart login - will detect if Microsoft login is needed and handle it automatically
    When I login to Microsoft with stored credentials
    # Wait for the application to load after successful authentication
    Then I wait for element "//*[@href='#/allrequests/']/div/following-sibling::div" to be visible with timeout 60000
    And the page title should contain "KPMG Find - Settings"

#   Scenario: TC0102- Check the Default Data Source Order
#     # Continue from the previous scenario - already authenticated and on the SharePoint site
#     # Wait for the search box to appear (should already be available from previous scenario)
#     Then I wait for element "getByRole('combobox', { name: 'Search box. Suggestions' })" to be visible with timeout 60000
#     When I fill "getByRole('combobox', { name: 'Search box. Suggestions' })" with "kpmg"
#     And I press "Enter"
#     Then the page title should contain "Results"
#     Then I wait for element "getByRole('link', { name: 'Organizational Logo' })" to be visible with timeout 60000
#     # Wait for search results to fully load
#     # And I wait for 5000 milliseconds
#     # Use a simpler selector for the button - look for the filter/datasource button
#     Then I wait for "getByRole('button', { name: 'All', exact: true })" to be visible with timeout 30000
#     When I click on "getByRole('button', { name: 'All', exact: true })"
#     # Then I wait for 5000 milliseconds
#     Then I wait for "getByRole('heading', { name: 'Datasources' })" to be visible with timeout 30000
#     # Then I wait for element "div.ms-Panel-header" to be visible with timeout 10000
#     Then "getByRole('heading', { name: 'Datasources' })" should contain text "Datasources"
