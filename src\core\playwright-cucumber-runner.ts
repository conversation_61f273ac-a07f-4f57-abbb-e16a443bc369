import { test, expect, Page, BrowserContext } from '@playwright/test';
import { Logger } from '../utils/logger';
import { StepDefinitionRegistry } from './step-definition-registry';
import { PlaywrightWorld } from './playwright-world';
import { EnvConfig } from '../config/env.config';
import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';

/**
 * Playwright-Cucumber Integration Runner
 * 
 * This runner converts Cucumber scenarios directly into Playwright tests,
 * providing proper step-by-step logging and native Playwright reporting.
 */

interface CucumberScenario {
  name: string;
  tags: string[];
  steps: CucumberStep[];
  feature: string;
}

interface CucumberStep {
  keyword: string;
  text: string;
  line: number;
}

interface CucumberFeature {
  name: string;
  description: string;
  tags: string[];
  scenarios: CucumberScenario[];
  backgroundSteps: CucumberStep[];
  file: string;
}

export class PlaywrightCucumberRunner {
  private logger: Logger;
  private stepRegistry: StepDefinitionRegistry;
  private features: CucumberFeature[] = [];

  constructor() {
    this.logger = new Logger('PlaywrightCucumberRunner');
    this.stepRegistry = new StepDefinitionRegistry();
  }

  /**
   * Load and parse Cucumber feature files synchronously
   */
  loadFeaturesSync(pattern: string = 'src/features/**/*.feature'): CucumberFeature[] {
    const featureFiles = require('glob').sync(pattern);
    const features: CucumberFeature[] = [];

    for (const file of featureFiles) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        const feature = this.parseFeature(content, file);
        if (feature) {
          features.push(feature);
        }
      } catch (error) {
        this.logger.error(`Failed to parse feature file ${file}:`, error);
      }
    }

    this.logger.info(`Loaded ${features.length} feature files`);
    this.features = features;
    return features;
  }

  /**
   * Load and parse Cucumber feature files
   */
  async loadFeatures(pattern: string = 'src/features/**/*.feature'): Promise<void> {
    const featureFiles = await glob(pattern);

    for (const file of featureFiles) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        const feature = this.parseFeature(content, file);
        if (feature) {
          this.features.push(feature);
        }
      } catch (error) {
        this.logger.error(`Failed to parse feature file ${file}:`, error);
      }
    }

    this.logger.info(`Loaded ${this.features.length} feature files`);
  }

  /**
   * Parse a Cucumber feature file
   */
  private parseFeature(content: string, file: string): CucumberFeature | null {
    const lines = content.split('\n');
    let currentLine = 0;
    
    // Skip to feature line
    while (currentLine < lines.length && !lines[currentLine].trim().startsWith('Feature:')) {
      currentLine++;
    }
    
    if (currentLine >= lines.length) {
      return null;
    }

    const featureLine = lines[currentLine].trim();
    const featureName = featureLine.replace('Feature:', '').trim();
    
    // Get feature tags (lines before feature)
    const featureTags: string[] = [];
    let tagLine = currentLine - 1;
    while (tagLine >= 0 && lines[tagLine].trim().startsWith('@')) {
      featureTags.push(...lines[tagLine].trim().split(/\s+/));
      tagLine--;
    }

    // Get feature description and parse background
    currentLine++;
    let description = '';
    let backgroundSteps: CucumberStep[] = [];

    while (currentLine < lines.length && !lines[currentLine].trim().startsWith('Scenario') && !lines[currentLine].trim().startsWith('@')) {
      const line = lines[currentLine].trim();

      if (line.startsWith('Background:')) {
        // Parse background steps
        currentLine++;
        while (currentLine < lines.length &&
               !lines[currentLine].trim().startsWith('Scenario') &&
               !lines[currentLine].trim().startsWith('@')) {

          const stepLine = lines[currentLine].trim();
          if (stepLine && (stepLine.startsWith('Given') || stepLine.startsWith('When') || stepLine.startsWith('Then') || stepLine.startsWith('And') || stepLine.startsWith('But'))) {
            const parts = stepLine.split(/\s+/);
            const keyword = parts[0];
            const text = parts.slice(1).join(' ');

            backgroundSteps.push({
              keyword,
              text,
              line: currentLine + 1
            });
          }
          currentLine++;
        }
        break; // Exit the description parsing loop
      } else if (line) {
        description += line + ' ';
      }
      currentLine++;
    }

    // Parse scenarios
    const scenarios: CucumberScenario[] = [];
    
    while (currentLine < lines.length) {
      // Look for scenario tags
      const scenarioTags: string[] = [...featureTags];
      while (currentLine < lines.length && lines[currentLine].trim().startsWith('@')) {
        scenarioTags.push(...lines[currentLine].trim().split(/\s+/));
        currentLine++;
      }

      // Look for scenario line (both Scenario: and Scenario Outline:)
      if (currentLine < lines.length && (lines[currentLine].trim().startsWith('Scenario:') || lines[currentLine].trim().startsWith('Scenario Outline:'))) {
        const scenarioLine = lines[currentLine].trim();
        const scenarioName = scenarioLine.replace(/^Scenario( Outline)?:\s*/, '').trim();
        const isOutline = scenarioLine.includes('Outline');
        currentLine++;

        // Parse steps
        const steps: CucumberStep[] = [];
        let examplesTable: string[][] = [];

        while (currentLine < lines.length &&
               !lines[currentLine].trim().startsWith('Scenario') &&
               !lines[currentLine].trim().startsWith('@') &&
               !lines[currentLine].trim().startsWith('Feature:')) {

          const line = lines[currentLine].trim();

          // Parse step definitions
          if (line && (line.startsWith('Given') || line.startsWith('When') || line.startsWith('Then') || line.startsWith('And') || line.startsWith('But'))) {
            const parts = line.split(/\s+/);
            const keyword = parts[0];
            const text = parts.slice(1).join(' ');

            steps.push({
              keyword,
              text,
              line: currentLine + 1
            });
          }
          // Parse Examples table for Scenario Outline
          else if (line.startsWith('Examples:')) {
            currentLine++; // Skip the Examples: line
            // Parse table headers and rows
            while (currentLine < lines.length &&
                   lines[currentLine].trim().startsWith('|') &&
                   !lines[currentLine].trim().startsWith('Scenario') &&
                   !lines[currentLine].trim().startsWith('@')) {

              const tableLine = lines[currentLine].trim();
              if (tableLine.startsWith('|') && tableLine.endsWith('|')) {
                const row = tableLine.slice(1, -1).split('|').map(cell => cell.trim());
                examplesTable.push(row);
              }
              currentLine++;
            }
            break; // Exit step parsing after Examples
          }
          currentLine++;
        }

        if (steps.length > 0) {
          if (isOutline && examplesTable.length > 1) {
            // Generate multiple scenarios from outline
            const headers = examplesTable[0];
            for (let i = 1; i < examplesTable.length; i++) {
              const row = examplesTable[i];
              const expandedSteps = steps.map(step => {
                let expandedText = step.text;
                // Replace placeholders with actual values
                headers.forEach((header, index) => {
                  const placeholder = `<${header}>`;
                  const value = row[index] || '';
                  expandedText = expandedText.replace(new RegExp(placeholder, 'g'), value);
                });

                return {
                  keyword: step.keyword,
                  text: expandedText,
                  line: step.line
                };
              });

              scenarios.push({
                name: `${scenarioName} [${i}]`,
                tags: scenarioTags,
                steps: expandedSteps,
                feature: featureName
              });
            }
          } else {
            // Regular scenario
            scenarios.push({
              name: scenarioName,
              tags: scenarioTags,
              steps,
              feature: featureName
            });
          }
        }
      } else {
        currentLine++;
      }
    }

    return {
      name: featureName,
      description: description.trim(),
      tags: featureTags,
      scenarios,
      backgroundSteps,
      file
    };
  }



  /**
   * Execute a Cucumber step using the step definitions
   */
  private async executeStep(world: PlaywrightWorld, stepText: string, testInfo: any): Promise<void> {
    try {
      await this.stepRegistry.executeStep(world, stepText);
    } catch (error) {
      this.logger.error(`❌ Step failed: ${stepText}`, error);
      throw error;
    }
  }



  /**
   * Execute background steps only
   */
  async executeBackgroundSteps(backgroundSteps: CucumberStep[], page: Page, context: BrowserContext, testInfo: any): Promise<void> {
    // Initialize Playwright world
    const world = new PlaywrightWorld();
    await world.init(page, context);

    try {
      if (backgroundSteps.length > 0) {
        console.log(`🏗️ Executing ${backgroundSteps.length} background steps`);

        for (let i = 0; i < backgroundSteps.length; i++) {
          const step = backgroundSteps[i];
          const stepText = `${step.keyword} ${step.text}`;

          console.log(`📋 Background Step ${i + 1}/${backgroundSteps.length}: ${stepText}`);

          // Take screenshot before step
          const beforeScreenshot = path.join(testInfo.outputDir, `background-step-${i + 1}-before.png`);
          await page.screenshot({ path: beforeScreenshot, fullPage: true });
          await testInfo.attach(`Background Step ${i + 1} - Before: ${stepText}`, {
            path: beforeScreenshot,
            contentType: 'image/png'
          });

          // Execute the step
          const startTime = Date.now();
          await this.executeStep(world, stepText, testInfo);
          const duration = Date.now() - startTime;

          // Take screenshot after step
          const afterScreenshot = path.join(testInfo.outputDir, `background-step-${i + 1}-after.png`);
          await page.screenshot({ path: afterScreenshot, fullPage: true });

          await testInfo.attach(`Background Step ${i + 1} - After: ${stepText} (${duration}ms)`, {
            path: afterScreenshot,
            contentType: 'image/png'
          });

          console.log(`✅ Background step completed in ${duration}ms`);
        }

        console.log(`🏗️ Background steps completed successfully`);
      }
    } catch (error) {
      console.error(`❌ Background steps failed`, error);
      throw error;
    } finally {
      await world.cleanup();
    }
  }

  /**
   * Execute scenario steps only (without background)
   */
  async executeScenarioSteps(scenario: CucumberScenario, page: Page, context: BrowserContext, testInfo: any, dataSetNumber: number = 1): Promise<void> {
    // Initialize Playwright world
    const world = new PlaywrightWorld();
    await world.init(page, context);

    try {
      // Execute scenario steps
      for (let i = 0; i < scenario.steps.length; i++) {
        const scenarioStep = scenario.steps[i];
        const stepText = `${scenarioStep.keyword} ${scenarioStep.text}`;

        console.log(`📋 Data Set ${dataSetNumber} - Step ${i + 1}/${scenario.steps.length}: ${stepText}`);

        // Take screenshot before step
        const beforeScreenshot = path.join(testInfo.outputDir, `dataset-${dataSetNumber}-step-${i + 1}-before.png`);
        await page.screenshot({ path: beforeScreenshot, fullPage: true });
        await testInfo.attach(`Data Set ${dataSetNumber} - Step ${i + 1} - Before: ${stepText}`, {
          path: beforeScreenshot,
          contentType: 'image/png'
        });

        // Execute the step
        const startTime = Date.now();
        await this.executeStep(world, stepText, testInfo);
        const duration = Date.now() - startTime;

        // Take screenshot after step
        const afterScreenshot = path.join(testInfo.outputDir, `dataset-${dataSetNumber}-step-${i + 1}-after.png`);
        await page.screenshot({ path: afterScreenshot, fullPage: true });

        await testInfo.attach(`Data Set ${dataSetNumber} - Step ${i + 1} - After: ${stepText} (${duration}ms)`, {
          path: afterScreenshot,
          contentType: 'image/png'
        });

        console.log(`✅ Data Set ${dataSetNumber} - Step completed in ${duration}ms`);
      }

      console.log(`🎉 Data Set ${dataSetNumber} completed successfully: ${scenario.name}`);

    } catch (error) {
      console.error(`❌ Data Set ${dataSetNumber} failed: ${scenario.name}`, error);

      // Take failure screenshot
      const failureScreenshot = path.join(testInfo.outputDir, `dataset-${dataSetNumber}-failure-screenshot.png`);
      await page.screenshot({ path: failureScreenshot, fullPage: true });
      await testInfo.attach(`Data Set ${dataSetNumber} - Failure Screenshot`, {
        path: failureScreenshot,
        contentType: 'image/png'
      });

      throw error;
    } finally {
      await world.cleanup();
    }
  }

  /**
   * Reset to clean state between data sets
   */
  async resetToCleanState(page: Page, context: BrowserContext, testInfo: any): Promise<void> {
    try {
      console.log(`🔄 Resetting browser to clean state`);

      // Navigate back to the settings page (after background steps)
      await page.goto('https://es-settings-staging.kpmg.com/');

      // Wait for the page to be ready using a simple CSS selector
      await page.waitForSelector("button:has-text('Create a request on behalf of')", {
        state: 'visible',
        timeout: 10000
      });

      console.log(`✅ Reset to clean state completed`);

    } catch (error) {
      console.error(`❌ Reset to clean state failed:`, error);
      // Don't throw error, just log it - we can continue with the test
    }
  }

  /**
   * Execute a single scenario with background steps
   */
  async executeScenario(scenario: CucumberScenario, page: Page, context: BrowserContext, testInfo: any, backgroundSteps: CucumberStep[] = []): Promise<void> {
    // Initialize Playwright world
    const world = new PlaywrightWorld();
    await world.init(page, context);

    try {
      // Execute background steps first
      let stepCounter = 0;

      if (backgroundSteps.length > 0) {
        console.log(`🏗️ Executing ${backgroundSteps.length} background steps`);

        for (let i = 0; i < backgroundSteps.length; i++) {
          stepCounter++;
          const step = backgroundSteps[i];
          const stepText = `${step.keyword} ${step.text}`;

          console.log(`📋 Background Step ${i + 1}/${backgroundSteps.length}: ${stepText}`);

          // Take screenshot before step
          const beforeScreenshot = path.join(testInfo.outputDir, `background-step-${i + 1}-before.png`);
          await page.screenshot({ path: beforeScreenshot, fullPage: true });
          await testInfo.attach(`Background Step ${i + 1} - Before: ${stepText}`, {
            path: beforeScreenshot,
            contentType: 'image/png'
          });

          // Execute the step
          const startTime = Date.now();
          await this.executeStep(world, stepText, testInfo);
          const duration = Date.now() - startTime;

          // Take screenshot after step
          const afterScreenshot = path.join(testInfo.outputDir, `background-step-${i + 1}-after.png`);
          await page.screenshot({ path: afterScreenshot, fullPage: true });

          await testInfo.attach(`Background Step ${i + 1} - After: ${stepText} (${duration}ms)`, {
            path: afterScreenshot,
            contentType: 'image/png'
          });

          console.log(`✅ Background step completed in ${duration}ms`);
        }

        console.log(`🏗️ Background steps completed successfully`);
      }

      // Execute scenario steps
      for (let i = 0; i < scenario.steps.length; i++) {
        stepCounter++;
        const step = scenario.steps[i];
        const scenarioStep = scenario.steps[i];
        const stepText = `${scenarioStep.keyword} ${scenarioStep.text}`;

        console.log(`📋 Step ${i + 1}/${scenario.steps.length}: ${stepText}`);

        // Take screenshot before step
        const beforeScreenshot = path.join(testInfo.outputDir, `step-${i + 1}-before.png`);
        await page.screenshot({ path: beforeScreenshot, fullPage: true });
        await testInfo.attach(`Step ${i + 1} - Before: ${stepText}`, {
          path: beforeScreenshot,
          contentType: 'image/png'
        });

        // Execute the step
        const startTime = Date.now();
        await this.executeStep(world, stepText, testInfo);
        const duration = Date.now() - startTime;

        // Take screenshot after step
        const afterScreenshot = path.join(testInfo.outputDir, `step-${i + 1}-after.png`);
        await page.screenshot({ path: afterScreenshot, fullPage: true });

        await testInfo.attach(`Step ${i + 1} - After: ${stepText} (${duration}ms)`, {
          path: afterScreenshot,
          contentType: 'image/png'
        });

        console.log(`✅ Step completed in ${duration}ms`);
      }

      console.log(`🎉 Scenario completed successfully: ${scenario.name}`);

      // Attach video if available
      if (testInfo.attachments) {
        const videoAttachment = testInfo.attachments.find((a: any) => a.name === 'video');
        if (videoAttachment) {
          console.log(`🎬 Video recorded: ${videoAttachment.path}`);
        }
      }

      // Attach trace if available
      const tracePath = path.join(testInfo.outputDir, 'trace.zip');
      if (await this.fileExists(tracePath)) {
        await testInfo.attach('Trace', {
          path: tracePath,
          contentType: 'application/zip'
        });
        console.log(`🔍 Trace recorded: ${tracePath}`);
      }

    } catch (error) {
      console.error(`❌ Scenario failed: ${scenario.name}`, error);

      // Take failure screenshot
      const failureScreenshot = path.join(testInfo.outputDir, 'failure-screenshot.png');
      await page.screenshot({ path: failureScreenshot, fullPage: true });
      await testInfo.attach('Failure Screenshot', {
        path: failureScreenshot,
        contentType: 'image/png'
      });

      throw error;
    } finally {
      await world.cleanup();
    }
  }

  /**
   * Check if scenario tags match the filter
   */
  matchesTags(tags: string[], filter: string): boolean {
    if (!filter) return true;

    // Simple tag matching - supports @tag format
    const filterTags = filter.split(/\s+/).map(tag => tag.trim());

    return filterTags.some(filterTag =>
      tags.some(tag => tag === filterTag)
    );
  }

  /**
   * Check if file exists
   */
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.promises.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Extract element selector from step text
   */
  private extractElementSelector(stepText: string): string | null {
    // Match quoted selectors in step text
    const selectorPatterns = [
      /"([^"]+)"/g,  // Match anything in double quotes
      /'([^']+)'/g   // Match anything in single quotes
    ];

    for (const pattern of selectorPatterns) {
      const matches = Array.from(stepText.matchAll(pattern));
      for (const match of matches) {
        const selector = match[1];
        // Check if it looks like a selector (contains common selector patterns)
        if (this.isLikelySelector(selector)) {
          return selector;
        }
      }
    }

    return null;
  }

  /**
   * Check if a string looks like an element selector
   */
  private isLikelySelector(text: string): boolean {
    // Exclude URLs and common non-selector patterns
    if (text.startsWith('http://') || text.startsWith('https://') ||
        text.includes('@') || text.includes('.com') || text.includes('.org') ||
        text.length > 200) {
      return false;
    }

    const selectorIndicators = [
      'getByRole(',
      'getByText(',
      'getByLabel(',
      'getByPlaceholder(',
      'getByTestId(',
      'locator(',
      '#',  // CSS ID (but not URLs)
      '.',  // CSS class (but not domains)
      '[',  // CSS attribute
    ];

    // For CSS selectors, be more specific
    if (text.startsWith('#') || text.startsWith('.') || text.startsWith('[')) {
      return !text.includes('://') && !text.includes('@');
    }

    return selectorIndicators.some(indicator => text.includes(indicator));
  }

  /**
   * Take a highlighted screenshot
   */
  private async takeHighlightedScreenshot(world: PlaywrightWorld, selector: string, screenshotPath: string): Promise<void> {
    try {
      const locator = world.getLocator(selector);

      // Highlight the element
      await world['uiHelper'].highlightLocator(locator, { duration: 0 }); // No auto-remove

      // Take screenshot
      await world.page.screenshot({ path: screenshotPath, fullPage: true });

      // Remove highlight
      await locator.evaluate((element: HTMLElement) => {
        if (element) {
          element.style.outline = element.getAttribute('data-original-outline') || '';
          element.style.outlineOffset = element.getAttribute('data-original-outline-offset') || '';
          element.style.boxShadow = element.getAttribute('data-original-box-shadow') || '';
          element.style.transition = '';
          element.removeAttribute('data-original-outline');
          element.removeAttribute('data-original-outline-offset');
          element.removeAttribute('data-original-box-shadow');
        }
      });
    } catch (error) {
      // Fall back to regular screenshot
      await world.page.screenshot({ path: screenshotPath, fullPage: true });
    }
  }
}

// Export for use in Playwright tests
export { CucumberScenario, CucumberStep, CucumberFeature };
