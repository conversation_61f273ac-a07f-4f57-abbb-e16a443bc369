{"config": {"configFile": "C:\\workspace\\Playwright_Automation_Framework\\playwright.config.ts", "rootDir": "C:/workspace/Playwright_Automation_Framework/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report", "open": "always", "host": "localhost", "port": 9323}], ["line", null], ["json", {"outputFile": "test-results/playwright-results.json"}], ["junit", {"outputFile": "test-results/junit-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/workspace/Playwright_Automation_Framework/test-results/playwright-data", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "C:/workspace/Playwright_Automation_Framework/tests", "testIgnore": [], "testMatch": ["**/*.spec.ts", "**/*.spec.js"], "timeout": 300000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": null}, "suites": [{"title": "cucumber-wrapper.spec.ts", "file": "cucumber-wrapper.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Self Service Featured Results", "file": "cucumber-wrapper.spec.ts", "line": 26, "column": 8, "specs": [{"title": "TC0101- <PERSON><PERSON> and Check the Default Data Source Order", "ok": false, "tags": [], "tests": [{"timeout": 300000, "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 90887, "error": {"message": "Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 102 elements:\n    1) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('.MuiChip-label').first()\n    2) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()\n    3) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms', { exact: true })\n    4) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka locator('td:nth-child(10) > div > .MuiButtonBase-root').first()\n    5) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()\n    6) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">AdWord</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n    7) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">gggggggggggggggg</span> aka getByText('gggggggggggggggg')\n    8) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka locator('tr:nth-child(3) > td:nth-child(10) > div > .MuiButtonBase-root')\n    9) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')\n    10) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('tr:nth-child(4) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n    ...\n\nCall log:\n\u001b[2m  - expect.toHaveText with timeout 30000ms\u001b[22m\n\u001b[2m  - waiting for locator('.MuiChip-label')\u001b[22m\n", "stack": "Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 102 elements:\n    1) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('.MuiChip-label').first()\n    2) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()\n    3) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms', { exact: true })\n    4) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka locator('td:nth-child(10) > div > .MuiButtonBase-root').first()\n    5) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()\n    6) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">AdWord</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n    7) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">gggggggggggggggg</span> aka getByText('gggggggggggggggg')\n    8) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka locator('tr:nth-child(3) > td:nth-child(10) > div > .MuiButtonBase-root')\n    9) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')\n    10) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('tr:nth-child(4) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n    ...\n\nCall log:\n\u001b[2m  - expect.toHaveText with timeout 30000ms\u001b[22m\n\u001b[2m  - waiting for locator('.MuiChip-label')\u001b[22m\n\n    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:154:31)\n    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:361:23)\n    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:196:31)\n    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:231:20)\n    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9", "location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts", "column": 31, "line": 154}, "snippet": "\u001b[90m   at \u001b[39m..\\src\\core\\step-definition-registry.ts:154\n\n\u001b[0m \u001b[90m 152 |\u001b[39m       \u001b[36masync\u001b[39m (world\u001b[33m:\u001b[39m \u001b[33mPlaywrightWorld\u001b[39m\u001b[33m,\u001b[39m selector\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m text\u001b[33m:\u001b[39m string) \u001b[33m=>\u001b[39m {\n \u001b[90m 153 |\u001b[39m         \u001b[36mconst\u001b[39m element \u001b[33m=\u001b[39m world\u001b[33m.\u001b[39mgetLocator(selector)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 154 |\u001b[39m         \u001b[36mawait\u001b[39m expect(element)\u001b[33m.\u001b[39mtoHaveText(text)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 155 |\u001b[39m       }\u001b[33m,\u001b[39m\n \u001b[90m 156 |\u001b[39m       \u001b[32m'Verify element has exact text (with \"the\" and \"element\")'\u001b[39m\n \u001b[90m 157 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts", "column": 31, "line": 154}, "message": "Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 102 elements:\n    1) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('.MuiChip-label').first()\n    2) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()\n    3) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms', { exact: true })\n    4) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka locator('td:nth-child(10) > div > .MuiButtonBase-root').first()\n    5) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()\n    6) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">AdWord</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n    7) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">gggggggggggggggg</span> aka getByText('gggggggggggggggg')\n    8) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka locator('tr:nth-child(3) > td:nth-child(10) > div > .MuiButtonBase-root')\n    9) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')\n    10) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('tr:nth-child(4) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n    ...\n\nCall log:\n\u001b[2m  - expect.toHaveText with timeout 30000ms\u001b[22m\n\u001b[2m  - waiting for locator('.MuiChip-label')\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\src\\core\\step-definition-registry.ts:154\n\n\u001b[0m \u001b[90m 152 |\u001b[39m       \u001b[36masync\u001b[39m (world\u001b[33m:\u001b[39m \u001b[33mPlaywrightWorld\u001b[39m\u001b[33m,\u001b[39m selector\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m text\u001b[33m:\u001b[39m string) \u001b[33m=>\u001b[39m {\n \u001b[90m 153 |\u001b[39m         \u001b[36mconst\u001b[39m element \u001b[33m=\u001b[39m world\u001b[33m.\u001b[39mgetLocator(selector)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 154 |\u001b[39m         \u001b[36mawait\u001b[39m expect(element)\u001b[33m.\u001b[39mtoHaveText(text)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 155 |\u001b[39m       }\u001b[33m,\u001b[39m\n \u001b[90m 156 |\u001b[39m       \u001b[32m'Verify element has exact text (with \"the\" and \"element\")'\u001b[39m\n \u001b[90m 157 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:154:31)\u001b[22m\n\u001b[2m    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:361:23)\u001b[22m\n\u001b[2m    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:196:31)\u001b[22m\n\u001b[2m    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:231:20)\u001b[22m\n\u001b[2m    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9\u001b[22m"}], "stdout": [{"text": "🎬 Starting scenario: TC0101- <PERSON><PERSON> and Check the Default Data Source Order\n"}, {"text": "📁 Feature: Self Service Featured Results\n"}, {"text": "🏷️ Tags: @combined, @self-service, @fr\n"}, {"text": "📋 Step 1/47: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\"\n"}, {"text": "✅ Step completed in 3301ms\n"}, {"text": "📋 Step 2/47: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 6899ms\n"}, {"text": "📋 Step 3/47: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"\n"}, {"text": "✅ Step completed in 138ms\n"}, {"text": "📋 Step 4/47: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"\n"}, {"text": "✅ Step completed in 136ms\n"}, {"text": "📋 Step 5/47: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000\n"}, {"text": "✅ Step completed in 13285ms\n"}, {"text": "📋 Step 6/47: And I wait for element \"getByRole('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000\n"}, {"text": "✅ Step completed in 3760ms\n"}, {"text": "📋 Step 7/47: And I wait for page title to contain \"KPMG Find - Settings\"\n"}, {"text": "✅ Step completed in 10ms\n"}, {"text": "📋 Step 8/47: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\"\n"}, {"text": "✅ Step completed in 231ms\n"}, {"text": "📋 Step 9/47: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 54ms\n"}, {"text": "📋 Step 10/47: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\"\n"}, {"text": "✅ Step completed in 71ms\n"}, {"text": "📋 Step 11/47: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON>ult' })\" should be visible\n"}, {"text": "✅ Step completed in 51ms\n"}, {"text": "📋 Step 12/47: And element \"getByRole('button', { name: 'Can<PERSON>' })\" should be visible\n"}, {"text": "✅ Step completed in 41ms\n"}, {"text": "📋 Step 13/47: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON>R<PERSON><PERSON>' })\"\n"}, {"text": "✅ Step completed in 168ms\n"}, {"text": "📋 Step 14/47: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000\n"}, {"text": "✅ Step completed in 42ms\n"}, {"text": "📋 Step 15/47: When I click on \"locator('#menu-').getByText('FeaturedResult')\"\n"}, {"text": "✅ Step completed in 113ms\n"}, {"text": "📋 Step 16/47: When I click on \"getByRole('textbox', { name: 'Selected User' })\"\n"}, {"text": "✅ Step completed in 177ms\n"}, {"text": "📋 Step 17/47: Then I wait for element \"getByRole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 49ms\n"}, {"text": "📋 Step 18/47: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible\n"}, {"text": "✅ Step completed in 50ms\n"}, {"text": "📋 Step 19/47: And element \"getByRole('searchbox', { name: 'description' })\" should be visible\n"}, {"text": "✅ Step completed in 42ms\n"}, {"text": "📋 Step 20/47: When I fill \"getBy<PERSON><PERSON>('searchbox', { name: 'description' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"\n"}, {"text": "✅ Step completed in 103ms\n"}, {"text": "📋 Step 21/47: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 2039ms\n"}, {"text": "📋 Step 22/47: And I wait for element \"getBy<PERSON><PERSON><PERSON>('jagan<PERSON><PERSON>@kpmg.com')\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 35ms\n"}, {"text": "📋 Step 23/47: When I click on \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\"\n"}, {"text": "✅ Step completed in 154ms\n"}, {"text": "📋 Step 24/47: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Select' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 39ms\n"}, {"text": "📋 Step 25/47: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Select' })\"\n"}, {"text": "✅ Step completed in 142ms\n"}, {"text": "📋 Step 26/47: Then I wait for element \"getByR<PERSON>('button', { name: 'Create' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 44ms\n"}, {"text": "📋 Step 27/47: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\"\n"}, {"text": "✅ Step completed in 362ms\n"}, {"text": "📋 Step 28/47: Then I wait for element \"getByText('New Featured Result for K,')\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 47ms\n"}, {"text": "📋 Step 29/47: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Title *' })\" with \"Test Title\"\n"}, {"text": "✅ Step completed in 229ms\n"}, {"text": "📋 Step 30/47: And I click on \"getByRole('textbox', { name: 'Description' })\"\n"}, {"text": "✅ Step completed in 168ms\n"}, {"text": "📋 Step 31/47: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Description' })\" with \"Test Description\"\n"}, {"text": "✅ Step completed in 203ms\n"}, {"text": "📋 Step 32/47: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Url', exact: true })\" with \"https://www.google.com\"\n"}, {"text": "✅ Step completed in 254ms\n"}, {"text": "📋 Step 33/47: And I fill \"getByRole('textbox', { name: 'Search terms (provide a comma' })\" with \"test, terms\"\n"}, {"text": "✅ Step completed in 276ms\n"}, {"text": "📋 Step 34/47: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Comments' })\" with \"Test Comments\"\n"}, {"text": "✅ Step completed in 147ms\n"}, {"text": "📋 Step 35/47: Then element \"getByRole('button', { name: 'Publish' })\" should be visible\n"}, {"text": "✅ Step completed in 49ms\n"}, {"text": "📋 Step 36/47: And element \"getByRole('button', { name: 'Create' })\" should be visible\n"}, {"text": "✅ Step completed in 42ms\n"}, {"text": "📋 Step 37/47: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\"\n"}, {"text": "✅ Step completed in 206ms\n"}, {"text": "📋 Step 38/47: Then I wait for element \"getByRole('heading', { name: 'Confirm creating request!' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 32ms\n"}, {"text": "📋 Step 39/47: And element \"getByRole('button', { name: 'No' })\" should be visible\n"}, {"text": "✅ Step completed in 36ms\n"}, {"text": "📋 Step 40/47: And element \"getByRole('button', { name: 'Yes' })\" should be visible\n"}, {"text": "✅ Step completed in 38ms\n"}, {"text": "📋 Step 41/47: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Yes' })\"\n"}, {"text": "✅ Step completed in 167ms\n"}, {"text": "📋 Step 42/47: Then I wait for element \"getByRole('heading', { name: 'Created request' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 2579ms\n"}, {"text": "📋 Step 43/47: And element \"getByText('Successfully created')\" should be visible\n"}, {"text": "✅ Step completed in 44ms\n"}, {"text": "📋 Step 44/47: And element \"getByRole('button', { name: 'Close' })\" should be visible\n"}, {"text": "✅ Step completed in 30ms\n"}, {"text": "📋 Step 45/47: Then the \"locator('.MuiChip-label').first()\" element should have exact text \"FeaturedResult\"\n"}, {"text": "[\u001b[31merror\u001b[39m]: ❌ Step failed: Then the \"locator('.MuiChip-label').first()\" element should have exact text \"FeaturedResult\"\r\n"}], "stderr": [{"text": "❌ Scenario failed: TC0101- Lo<PERSON> and Check the Default Data Source Order expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 102 elements:\n    1) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('.MuiChip-label').first()\n    2) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()\n    3) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms', { exact: true })\n    4) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka locator('td:nth-child(10) > div > .MuiButtonBase-root').first()\n    5) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()\n    6) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">AdWord</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n    7) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">gggggggggggggggg</span> aka getByText('gggggggggggggggg')\n    8) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka locator('tr:nth-child(3) > td:nth-child(10) > div > .MuiButtonBase-root')\n    9) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')\n    10) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('tr:nth-child(4) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n    ...\n\nCall log:\n\u001b[2m  - expect.toHaveText with timeout 30000ms\u001b[22m\n\u001b[2m  - waiting for locator('.MuiChip-label')\u001b[22m\n\n    at Object.handler \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\step-definition-registry.ts:154:31\u001b[90m)\u001b[39m\n    at StepDefinitionRegistry.executeStep \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\step-definition-registry.ts:361:23\u001b[90m)\u001b[39m\n    at PlaywrightCucumberRunner.executeStep \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-cucumber-runner.ts:196:31\u001b[90m)\u001b[39m\n    at PlaywrightCucumberRunner.executeScenario \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-cucumber-runner.ts:231:20\u001b[90m)\u001b[39m\n    at \u001b[90mC:\\workspace\\Playwright_Automation_Framework\\\u001b[39mtests\\cucumber-wrapper.spec.ts:45:9 {\n  [\u001b[32mSymbol(step)\u001b[39m]: {\n    stepId: \u001b[32m'expect@231'\u001b[39m,\n    category: \u001b[32m'expect'\u001b[39m,\n    title: \u001b[32m'expect.toHaveText'\u001b[39m,\n    params: { expected: \u001b[32m'FeaturedResult'\u001b[39m },\n    infectParentStepsWithError: \u001b[90mundefined\u001b[39m,\n    boxedStack: \u001b[90mundefined\u001b[39m,\n    location: {\n      file: \u001b[32m'C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\step-definition-registry.ts'\u001b[39m,\n      line: \u001b[33m154\u001b[39m,\n      column: \u001b[33m31\u001b[39m,\n      function: \u001b[32m'Object.handler'\u001b[39m\n    },\n    steps: [],\n    attachmentIndices: [],\n    info: TestStepInfoImpl {\n      annotations: [],\n      _testInfo: \u001b[36m[TestInfoImpl]\u001b[39m,\n      _stepId: \u001b[32m'expect@231'\u001b[39m\n    },\n    complete: \u001b[36m[Function: complete]\u001b[39m,\n    endWallTime: \u001b[33m1749619908008\u001b[39m,\n    error: {\n      message: \u001b[32m\"Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 102 elements:\\n\"\u001b[39m +\n        \u001b[32m`    1) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('.MuiChip-label').first()\\n`\u001b[39m +\n        \u001b[32m`    2) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()\\n`\u001b[39m +\n        \u001b[32m`    3) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms', { exact: true })\\n`\u001b[39m +\n        \u001b[32m`    4) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka locator('td:nth-child(10) > div > .MuiButtonBase-root').first()\\n`\u001b[39m +\n        \u001b[32m`    5) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()\\n`\u001b[39m +\n        \u001b[32m`    6) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">AdWord</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\\n`\u001b[39m +\n        \u001b[32m`    7) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">gggggggggggggggg</span> aka getByText('gggggggggggggggg')\\n`\u001b[39m +\n        \u001b[32m`    8) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka locator('tr:nth-child(3) > td:nth-child(10) > div > .MuiButtonBase-root')\\n`\u001b[39m +\n        \u001b[32m`    9) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')\\n`\u001b[39m +\n        \u001b[32m`    10) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('tr:nth-child(4) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\\n`\u001b[39m +\n        \u001b[32m'    ...\\n'\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - expect.toHaveText with timeout 30000ms\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for locator('.MuiChip-label')\\x1B[22m\\n\"\u001b[39m,\n      stack: \u001b[32m\"Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 102 elements:\\n\"\u001b[39m +\n        \u001b[32m`    1) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('.MuiChip-label').first()\\n`\u001b[39m +\n        \u001b[32m`    2) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()\\n`\u001b[39m +\n        \u001b[32m`    3) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms', { exact: true })\\n`\u001b[39m +\n        \u001b[32m`    4) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka locator('td:nth-child(10) > div > .MuiButtonBase-root').first()\\n`\u001b[39m +\n        \u001b[32m`    5) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()\\n`\u001b[39m +\n        \u001b[32m`    6) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">AdWord</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\\n`\u001b[39m +\n        \u001b[32m`    7) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">gggggggggggggggg</span> aka getByText('gggggggggggggggg')\\n`\u001b[39m +\n        \u001b[32m`    8) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka locator('tr:nth-child(3) > td:nth-child(10) > div > .MuiButtonBase-root')\\n`\u001b[39m +\n        \u001b[32m`    9) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')\\n`\u001b[39m +\n        \u001b[32m`    10) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('tr:nth-child(4) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\\n`\u001b[39m +\n        \u001b[32m'    ...\\n'\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - expect.toHaveText with timeout 30000ms\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for locator('.MuiChip-label')\\x1B[22m\\n\"\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'    at Object.handler (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\step-definition-registry.ts:154:31)\\n'\u001b[39m +\n        \u001b[32m'    at StepDefinitionRegistry.executeStep (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\step-definition-registry.ts:361:23)\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightCucumberRunner.executeStep (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts:196:31)\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightCucumberRunner.executeScenario (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts:231:20)\\n'\u001b[39m +\n        \u001b[32m'    at C:\\\\workspace\\\\Playwright_Automation_Framework\\\\tests\\\\cucumber-wrapper.spec.ts:45:9'\u001b[39m,\n      cause: \u001b[90mundefined\u001b[39m\n    }\n  }\n}\n"}], "retry": 0, "startTime": "2025-06-11T05:30:24.223Z", "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "attachments": [{"name": "Step 1 - Before: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--9f5ae14be54d00e3a62c2b16aaf7cc1484e376f8.png"}, {"name": "Step 1 - After: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\" (3301ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-3301ms--a1a087f0e3c500721ff9ae627164f2a156e6def3.png"}, {"name": "Step 2 - Before: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-1d25b4a7f9215540528bcae43fdce44b2e129411.png"}, {"name": "Step 2 - After: Then I wait for \"getByR<PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000 (6899ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-6899ms--73617745d97bb32ec3dc8ae51580cc93e4a45228.png"}, {"name": "Step 3 - Before: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagan<PERSON><PERSON>-kpmg-com--316da82eef654cae4ad12d47f8fe1ffa375efaa6.png"}, {"name": "Step 3 - After: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\" (138ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagan<PERSON><PERSON>-kpmg-com-138ms--4a9f4d18cec09338fddd537da3174525295c5e1c.png"}, {"name": "Step 4 - Before: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-4---Before-And-I-click-on-getByRole-button-name-Next--996138976132e5a6f4d8dbc56204b69cf5c74dd9.png"}, {"name": "Step 4 - After: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\" (136ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-4---After-And-I-click-on-getByRole-button-name-Next-136ms--3f82437afad13e30321af3d78ac8f5fa484586c5.png"}, {"name": "Step 5 - Before: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-b7eb0d29174c8085f8fb05af67b2219b6e33c240.png"}, {"name": "Step 5 - After: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000 (13285ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-5---After-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-13285ms--3dd4df820ca306c48c56e1222ae6fb296e3fa9cb.png"}, {"name": "Step 6 - Before: And I wait for element \"getBy<PERSON>ole('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-6---Before-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-9a62bc5c83bd200eb2a7dff944a05f818f65627a.png"}, {"name": "Step 6 - After: And I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000 (3760ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-6---After-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-3760ms--17fc87cf7487f9ec6179fdd411bb5055ed61bea4.png"}, {"name": "Step 7 - Before: And I wait for page title to contain \"KPMG Find - Settings\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-7---Before-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings--e0bfffde38f7f27a22628c6df8759960773211f9.png"}, {"name": "Step 7 - After: And I wait for page title to contain \"KPMG Find - Settings\" (10ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-7---After-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings-10ms--4b83bbf3501667bb17e22b4c76e9fb6dc6462bf1.png"}, {"name": "Step 8 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-8---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--589f5cbca4ff4d2eb36011127d110c26f4ff274a.png"}, {"name": "Step 8 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\" (231ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-8---After-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of-231ms--03f7be8836af0bea3f8d59039c40bcb3f07cf292.png"}, {"name": "Step 9 - Before: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-9---Before-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-04edb0a85796d1d977f53b8c4037f0ff4bbd750a.png"}, {"name": "Step 9 - After: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000 (54ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-9---After-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-54ms--c971768b63e560fabc0ff2286d7a76cb56abac01.png"}, {"name": "Step 10 - Before: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-10---Before-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User--165c17b6050f87ae4a620d027e9fd607edf2912b.png"}, {"name": "Step 10 - After: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\" (71ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-10---After-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User-71ms--feb725ac02ac5f75d4bed7dedb852bf3829125f9.png"}, {"name": "Step 11 - Before: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON><PERSON>' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-11---Before-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-f466302481ff8688838ef58344d6d1badc735b93.png"}, {"name": "Step 11 - After: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON><PERSON>' })\" should be visible (51ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-11---After-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-51ms--f84da7efb281bbb123b9457fafd025189069862f.png"}, {"name": "Step 12 - Before: And element \"getByRole('button', { name: 'Can<PERSON>' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-12---Before-And-element-getByRole-button-name-Cancel-should-be-visible-3dd38e4bd0b48ac6cd8e82e706f3ceaa7cb695e4.png"}, {"name": "Step 12 - After: And element \"getByRole('button', { name: '<PERSON><PERSON>' })\" should be visible (41ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-12---After-And-element-getByRole-button-name-Cancel-should-be-visible-41ms--58ad1c6ff6ad0e844b8e1bb1067a683d738f6616.png"}, {"name": "Step 13 - Before: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-13---Before-When-I-click-on-getByRole-combobox-name-FeaturedR<PERSON>ult--b44ff2847df8960e31125198a9f555b9005c3f2c.png"}, {"name": "Step 13 - After: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\" (168ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-13---After-When-I-click-on-getByRole-combobox-name-FeaturedResult-168ms--f94b88f5efa8be97575230b453269d0c8c81733e.png"}, {"name": "Step 14 - Before: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-14---Before-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-b63538221cab462f800c2d0ec0aa353bd3fd08f7.png"}, {"name": "Step 14 - After: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000 (42ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-14---After-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-42ms--00989910ab77e431a1340290b365d1acde0c36d1.png"}, {"name": "Step 15 - Before: When I click on \"locator('#menu-').getByText('FeaturedResult')\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-15---Before-When-I-click-on-locator-menu--getByText-FeaturedResult--d3599577eff0509e5507af39e850dad2e5d8d263.png"}, {"name": "Step 15 - After: When I click on \"locator('#menu-').getByText('FeaturedResult')\" (113ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-15---After-When-I-click-on-locator-menu--getByText-FeaturedResult-113ms--9875c948c18489dbe34e5ed4b757d4f451df60d2.png"}, {"name": "Step 16 - Before: When I click on \"getByRole('textbox', { name: 'Selected User' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-16---Before-When-I-click-on-getByRole-textbox-name-Selected-User--e7453252d51fec73a60460d99d29ff0cc4471d0e.png"}, {"name": "Step 16 - After: When I click on \"getByRole('textbox', { name: 'Selected User' })\" (177ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-16---After-When-I-click-on-getByRole-textbox-name-Selected-User-177ms--b9914e79665458f3c1b4e8d3130e0144e074bd60.png"}, {"name": "Step 17 - Before: Then I wait for element \"getByRole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-17---Before-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-081a4f6989bf3959043643f2ee92f933d294559d.png"}, {"name": "Step 17 - After: Then I wait for element \"getBy<PERSON>ole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000 (49ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-17---After-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-49ms--2d5b1362c025503f2b83d8744564bd6523341c42.png"}, {"name": "Step 18 - Before: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-18---Before-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-c8cb60666241e60459e04119164baf19ede36dec.png"}, {"name": "Step 18 - After: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible (50ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-18---After-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-50ms--0b241fc56d9a2dbbfadd6b051a7b40c998e51f97.png"}, {"name": "Step 19 - Before: And element \"getByRole('searchbox', { name: 'description' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-19---Before-And-element-getByRole-searchbox-name-description-should-be-visible-a3728c020b254f2103b34b0dc6af2b8cd2f29e7e.png"}, {"name": "Step 19 - After: And element \"getByRole('searchbox', { name: 'description' })\" should be visible (42ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-19---After-And-element-getByRole-searchbox-name-description-should-be-visible-42ms--956e1319ff9389a01034dc21c463ebce2e24301c.png"}, {"name": "Step 20 - Before: When I fill \"getBy<PERSON><PERSON>('searchbox', { name: 'description' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-20---Before-When-I-fill-getByRole-searchbox-name-description-with-j<PERSON><PERSON><PERSON>-kpmg-com--6c8452e9192231788e4c5f1b13f164438f545565.png"}, {"name": "Step 20 - After: When I fill \"getBy<PERSON><PERSON>('searchbox', { name: 'description' })\" with \"jagan<PERSON><PERSON>@kpmg.com\" (103ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-20---After-When-I-fill-getByRole-searchbox-name-description-with-j<PERSON><PERSON><PERSON>-kpmg-com-103ms--4229ab33ae24538a749e13b5cc8fbe2b09cb9835.png"}, {"name": "Step 21 - Before: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-21---Before-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-b84c83400fb05aee3210d524537b31cf2168e238.png"}, {"name": "Step 21 - After: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000 (2039ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-21---After-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-2039ms--d4c4b0791a0d6e6f73a25da980f328fbdc70b5aa.png"}, {"name": "Step 22 - Before: And I wait for element \"getBy<PERSON><PERSON><PERSON>('j<PERSON><PERSON><PERSON>@kpmg.com')\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-22---Before-And-I-wait-for-element-getByLabel-jagan<PERSON><PERSON>-kpmg-com-to-be-visible-with-timeout-10000-f62adb5618e58a83aa622f261fe25140be482880.png"}, {"name": "Step 22 - After: And I wait for element \"getBy<PERSON><PERSON><PERSON>('j<PERSON><PERSON><PERSON>@kpmg.com')\" to be visible with timeout 10000 (35ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-22---After-And-I-wait-for-element-getByLabel-jagan<PERSON><PERSON>-kpmg-com-to-be-visible-with-timeout-10000-35ms--b058dc2951ccf78260cf3f1a942f7e24e4da7322.png"}, {"name": "Step 23 - Before: When I click on \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-23---Before-When-I-click-on-getByLabel-K-Jagannatha--45924ca3f70ae46b34dd928e2b059beb8b8cb12d.png"}, {"name": "Step 23 - After: When I click on \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>')\" (154ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-23---After-When-I-click-on-getByL<PERSON>l-K-Jagannatha-154ms--634c8214023995af38bd6fcbb95f2c7f73bfeae2.png"}, {"name": "Step 24 - Before: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Select' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-24---Before-Then-I-wait-for-element-getByRole-button-name-Select-to-be-visible-with-timeout-10000-7fca1bf9d98139bddf1fe4675a4490de6770efec.png"}, {"name": "Step 24 - After: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON>' })\" to be visible with timeout 10000 (39ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-24---After-Then-I-wait-for-element-getByRole-button-name-Select-to-be-visible-with-timeout-10000-39ms--7d54beb71eec6671431e2b2d2c6eeb87fbfd5b8d.png"}, {"name": "Step 25 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: '<PERSON>' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-25---Before-When-I-click-on-getByRole-button-name-Select--400568b0d60065a269429e72fe5e133e86c46636.png"}, {"name": "Step 25 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: '<PERSON>' })\" (142ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-25---After-When-I-click-on-getByRole-button-name-Select-142ms--d696b7e8bce66eb0c769f7f4d83c5147db7d296e.png"}, {"name": "Step 26 - Before: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Create' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-26---Before-Then-I-wait-for-element-getByRole-button-name-Create-to-be-visible-with-timeout-10000-b66a5d42c0b9a82dafe8f1b46714d7078892f182.png"}, {"name": "Step 26 - After: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Create' })\" to be visible with timeout 10000 (44ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-26---After-Then-I-wait-for-element-getByRole-button-name-Create-to-be-visible-with-timeout-10000-44ms--2001504c604814f105db554e4d183e4a4a125e6b.png"}, {"name": "Step 27 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-27---Before-When-I-click-on-getByRole-button-name-Create--596b90d80a4e62fbdf015e73ac819352ee436e05.png"}, {"name": "Step 27 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\" (362ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-27---After-When-I-click-on-getByRole-button-name-Create-362ms--3956549cd504e824b1bacfc087ce2934770cdb43.png"}, {"name": "Step 28 - Before: Then I wait for element \"getByText('New Featured Result for K,')\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-28---Before-Then-I-wait-for-element-getByText-New-Featured-Result-for-K-to-be-visible-with-timeout-10000-eca7c457e9201706de469e352df7466f7a98f973.png"}, {"name": "Step 28 - After: Then I wait for element \"getByText('New Featured Result for K,')\" to be visible with timeout 10000 (47ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-28---After-Then-I-wait-for-element-getByText-New-Featured-Result-for-K-to-be-visible-with-timeout-10000-47ms--a54486937dd245f6934085cf5097abfd6470baf3.png"}, {"name": "Step 29 - Before: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Title *' })\" with \"Test Title\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-29---Before-When-I-fill-getByRole-textbox-name-Title-with-Test-Title--a8a99d6b56efa29d08bca24ceff604609976ff38.png"}, {"name": "Step 29 - After: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Title *' })\" with \"Test Title\" (229ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-29---After-When-I-fill-getByRole-textbox-name-Title-with-Test-Title-229ms--5e2ab7f14f487a6af872ce0c560d69f1c9bff221.png"}, {"name": "Step 30 - Before: And I click on \"getBy<PERSON><PERSON>('textbox', { name: 'Description' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-30---Before-And-I-click-on-getByRole-textbox-name-Description--e61ead3a03a4bbcc8a47f4872d18708eab216c3f.png"}, {"name": "Step 30 - After: And I click on \"getByRole('textbox', { name: 'Description' })\" (168ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-30---After-And-I-click-on-getByRole-textbox-name-Description-168ms--245e13188e6a0de3b343ea72b51b77a968f8fb2c.png"}, {"name": "Step 31 - Before: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Description' })\" with \"Test Description\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-31---Before-And-I-fill-getByRole-textbox-name-Description-with-Test-Description--1fbed80c2c7d54f270046552a90ddc4a2ec9d616.png"}, {"name": "Step 31 - After: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Description' })\" with \"Test Description\" (203ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-31---After-And-I-fill-getByRole-textbox-name-Description-with-Test-Description-203ms--07d3214ce7a76d1b386c38da2e721f406bec396a.png"}, {"name": "Step 32 - Before: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Url', exact: true })\" with \"https://www.google.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-32---Before-And-I-fill-getByRole-textbox-name-Url-exact-true-with-https-www-google-com--0fac4f7246be7e4e5aa70c3ebc8e44578a03b7e6.png"}, {"name": "Step 32 - After: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Url', exact: true })\" with \"https://www.google.com\" (254ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-32---After-And-I-fill-getByRole-textbox-name-Url-exact-true-with-https-www-google-com-254ms--46807cbf42d2b29114b3eaa616ba1fdf37145268.png"}, {"name": "Step 33 - Before: And I fill \"getByRole('textbox', { name: 'Search terms (provide a comma' })\" with \"test, terms\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-33---Before-And-I-fill-getByRole-textbox-name-Search-terms-provide-a-comma-with-test-terms--4cafcc584a163ab6d2bd0862f1342ece2dc39716.png"}, {"name": "Step 33 - After: And I fill \"getByRole('textbox', { name: 'Search terms (provide a comma' })\" with \"test, terms\" (276ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-33---After-And-I-fill-getByRole-textbox-name-Search-terms-provide-a-comma-with-test-terms-276ms--f60507a3cb842edb85c6b7e000c6e82799dae553.png"}, {"name": "Step 34 - Before: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Comments' })\" with \"Test Comments\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-34---Before-And-I-fill-getByRole-textbox-name-Comments-with-Test-Comments--45a71673334d9de37457af5abfdaf5b05a9feaf7.png"}, {"name": "Step 34 - After: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Comments' })\" with \"Test Comments\" (147ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-34---After-And-I-fill-getByRole-textbox-name-Comments-with-Test-Comments-147ms--7f4f47fe2f8e3a362086ed7d2868f406f317f933.png"}, {"name": "Step 35 - Before: Then element \"getByRole('button', { name: 'Publish' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-35---Before-Then-element-getByRole-button-name-Publish-should-be-visible-6835b491a677a33dc1dbe5e4a670d81df2d9549d.png"}, {"name": "Step 35 - After: Then element \"getByRole('button', { name: 'Publish' })\" should be visible (49ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-35---After-Then-element-getByRole-button-name-Publish-should-be-visible-49ms--b7b326176d3bf4c8b96d756100f8491b787d6a3e.png"}, {"name": "Step 36 - Before: And element \"getByRole('button', { name: 'Create' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-36---Before-And-element-getByRole-button-name-Create-should-be-visible-d9d88089f52ad99cc5f72dcb602d44a16ef8e928.png"}, {"name": "Step 36 - After: And element \"getByRole('button', { name: 'Create' })\" should be visible (42ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-36---After-And-element-getByRole-button-name-Create-should-be-visible-42ms--e7eafd2cebeccbaeefafc28edf965e6266f6451c.png"}, {"name": "Step 37 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-37---Before-When-I-click-on-getByRole-button-name-Create--7bd7ca021991cd700acc633397bdba4239f7dff8.png"}, {"name": "Step 37 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\" (206ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-37---After-When-I-click-on-getByRole-button-name-Create-206ms--29cb6afa95e3fc2f0aca37d72282e7c5b6c473f6.png"}, {"name": "Step 38 - Before: Then I wait for element \"getByRole('heading', { name: 'Confirm creating request!' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-38---Before-Then-I-wait-for-element-getByRole-heading-name-Confirm-creating-request-to-be-visible-with-timeout-10000-b1e831114dd4be67ef4bd22f426ffb2ae83e5790.png"}, {"name": "Step 38 - After: Then I wait for element \"getByRole('heading', { name: 'Confirm creating request!' })\" to be visible with timeout 10000 (32ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-38---After-Then-I-wait-for-element-getByRole-heading-name-Confirm-creating-request-to-be-visible-with-timeout-10000-32ms--15d60d077a173e402e4b0239499df1579c599d79.png"}, {"name": "Step 39 - Before: And element \"getByRole('button', { name: 'No' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-39---Before-And-element-getByRole-button-name-No-should-be-visible-d2f9662249f127b1e1f8e524693a2527cecc7a76.png"}, {"name": "Step 39 - After: And element \"getByRole('button', { name: 'No' })\" should be visible (36ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-39---After-And-element-getByRole-button-name-No-should-be-visible-36ms--defa640cf72353ab68fe453e8407f157d70fa765.png"}, {"name": "Step 40 - Before: And element \"getByRole('button', { name: 'Yes' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-40---Before-And-element-getByRole-button-name-Yes-should-be-visible-ba2aee08fde3833a3aa0d46271b5927c9110880b.png"}, {"name": "Step 40 - After: And element \"getByRole('button', { name: 'Yes' })\" should be visible (38ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-40---After-And-element-getByRole-button-name-Yes-should-be-visible-38ms--8013f6409bef3675f0807165fd3669a25fd4a7ab.png"}, {"name": "Step 41 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Yes' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-41---Before-When-I-click-on-getByRole-button-name-Yes--d693cbfb0afa60e2b3a713f562d8a9f41ba8b866.png"}, {"name": "Step 41 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Yes' })\" (167ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-41---After-When-I-click-on-getByRole-button-name-Yes-167ms--c1f0dd6d17ec8e1f384eb22e83edd0292c4e2023.png"}, {"name": "Step 42 - Before: Then I wait for element \"getByRole('heading', { name: 'Created request' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-42---Before-Then-I-wait-for-element-getByRole-heading-name-Created-request-to-be-visible-with-timeout-10000-2b12609c6d42a78672038a34622f7a49434ba900.png"}, {"name": "Step 42 - After: Then I wait for element \"getByRole('heading', { name: 'Created request' })\" to be visible with timeout 10000 (2579ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-42---After-Then-I-wait-for-element-getByRole-heading-name-Created-request-to-be-visible-with-timeout-10000-2579ms--3f11a2b503fdfedc515de2b911f5f1237631ecd7.png"}, {"name": "Step 43 - Before: And element \"getByText('Successfully created')\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-43---Before-And-element-getByText-Successfully-created-should-be-visible-3268e5be292811ac0a7b860d648a17e334a4430b.png"}, {"name": "Step 43 - After: And element \"getByText('Successfully created')\" should be visible (44ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-43---After-And-element-getByText-Successfully-created-should-be-visible-44ms--455c0eb52bef5c93c2a36c2366bd605e3a8a2558.png"}, {"name": "Step 44 - Before: And element \"getBy<PERSON>ole('button', { name: 'Close' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-44---Before-And-element-getByRole-button-name-Close-should-be-visible-557a3cd94590df52634cc859295f237397c27adf.png"}, {"name": "Step 44 - After: And element \"getByRole('button', { name: 'Close' })\" should be visible (30ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-44---After-And-element-getByRole-button-name-Close-should-be-visible-30ms--e5f56bdf7a2f80ab6b787d7499222644e15dcbaf.png"}, {"name": "Step 45 - Before: Then the \"locator('.MuiChip-label').first()\" element should have exact text \"FeaturedResult\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-45---Before-Then-the-locator-MuiChip-label-first-element-should-have-exact-text-FeaturedResult--25d3b624d1f7bdc9d5541fae7e1d922380c01a38.png"}, {"name": "Failure Screenshot", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Failure-Screenshot-8bb9cb48c026463c729a9ab3c7ce68e5cf4ff1b8.png"}, {"name": "screenshot", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\trace.zip"}], "errorLocation": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts", "column": 31, "line": 154}}], "status": "unexpected"}], "id": "f0a7916b485de33344dc-83e8223fa8d0c7f1f561", "file": "cucumber-wrapper.spec.ts", "line": 34, "column": 11}]}]}], "errors": [], "stats": {"startTime": "2025-06-11T05:30:21.298Z", "duration": 101690.23800000001, "expected": 0, "skipped": 0, "unexpected": 1, "flaky": 0}}