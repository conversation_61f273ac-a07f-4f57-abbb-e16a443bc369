{"config": {"configFile": "C:\\workspace\\Playwright_Automation_Framework\\playwright.config.ts", "rootDir": "C:/workspace/Playwright_Automation_Framework/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report", "open": "always", "host": "localhost", "port": 9323}], ["line", null], ["json", {"outputFile": "test-results/playwright-results.json"}], ["junit", {"outputFile": "test-results/junit-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/workspace/Playwright_Automation_Framework/test-results/playwright-data", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "edge", "name": "edge", "testDir": "C:/workspace/Playwright_Automation_Framework/tests", "testIgnore": [], "testMatch": ["**/*.spec.ts", "**/*.spec.js"], "timeout": 300000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": null}, "suites": [{"title": "cucumber-wrapper.spec.ts", "file": "cucumber-wrapper.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Self Service Featured Results", "file": "cucumber-wrapper.spec.ts", "line": 26, "column": 8, "specs": [{"title": "TC0101- <PERSON><PERSON> and Check the Default Data Source Order", "ok": true, "tags": [], "tests": [{"timeout": 300000, "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "expectedStatus": "passed", "projectId": "edge", "projectName": "edge", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "interrupted", "duration": 211302, "error": {"message": "Error: page.screenshot: Target page, context or browser has been closed", "stack": "Error: page.screenshot: Target page, context or browser has been closed\n    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:285:18)\n    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9", "location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts", "column": 18, "line": 285}, "snippet": "\u001b[90m   at \u001b[39m..\\src\\core\\playwright-cucumber-runner.ts:285\n\n\u001b[0m \u001b[90m 283 |\u001b[39m       \u001b[90m// Take failure screenshot\u001b[39m\n \u001b[90m 284 |\u001b[39m       \u001b[36mconst\u001b[39m failureScreenshot \u001b[33m=\u001b[39m path\u001b[33m.\u001b[39mjoin(testInfo\u001b[33m.\u001b[39moutputDir\u001b[33m,\u001b[39m \u001b[32m'failure-screenshot.png'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 285 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mscreenshot({ path\u001b[33m:\u001b[39m failureScreenshot\u001b[33m,\u001b[39m fullPage\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 286 |\u001b[39m       \u001b[36mawait\u001b[39m testInfo\u001b[33m.\u001b[39mattach(\u001b[32m'Failure Screenshot'\u001b[39m\u001b[33m,\u001b[39m {\n \u001b[90m 287 |\u001b[39m         path\u001b[33m:\u001b[39m failureScreenshot\u001b[33m,\u001b[39m\n \u001b[90m 288 |\u001b[39m         contentType\u001b[33m:\u001b[39m \u001b[32m'image/png'\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts", "column": 18, "line": 285}, "message": "Error: page.screenshot: Target page, context or browser has been closed\n\n\u001b[90m   at \u001b[39m..\\src\\core\\playwright-cucumber-runner.ts:285\n\n\u001b[0m \u001b[90m 283 |\u001b[39m       \u001b[90m// Take failure screenshot\u001b[39m\n \u001b[90m 284 |\u001b[39m       \u001b[36mconst\u001b[39m failureScreenshot \u001b[33m=\u001b[39m path\u001b[33m.\u001b[39mjoin(testInfo\u001b[33m.\u001b[39moutputDir\u001b[33m,\u001b[39m \u001b[32m'failure-screenshot.png'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 285 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mscreenshot({ path\u001b[33m:\u001b[39m failureScreenshot\u001b[33m,\u001b[39m fullPage\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 286 |\u001b[39m       \u001b[36mawait\u001b[39m testInfo\u001b[33m.\u001b[39mattach(\u001b[32m'Failure Screenshot'\u001b[39m\u001b[33m,\u001b[39m {\n \u001b[90m 287 |\u001b[39m         path\u001b[33m:\u001b[39m failureScreenshot\u001b[33m,\u001b[39m\n \u001b[90m 288 |\u001b[39m         contentType\u001b[33m:\u001b[39m \u001b[32m'image/png'\u001b[39m\u001b[0m\n\u001b[2m    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:285:18)\u001b[22m\n\u001b[2m    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9\u001b[22m"}], "stdout": [{"text": "🎬 Starting scenario: TC0101- <PERSON><PERSON> and Check the Default Data Source Order\n"}, {"text": "📁 Feature: Self Service Featured Results\n"}, {"text": "🏷️ Tags: @combined, @self-service, @fr\n"}, {"text": "📋 Step 1/53: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\"\n"}, {"text": "[\u001b[33mwarn\u001b[39m]: Failed to highlight locator: Error: locator.evaluate: Unexpected token \"/\" while parsing css selector \"https://es-settings-staging.kpmg.com/\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - waiting for https://es-settings-staging.kpmg.com/\u001b[22m\n\r\n"}, {"text": "✅ Step completed in 2623ms\n"}, {"text": "📋 Step 2/53: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 8685ms\n"}, {"text": "📋 Step 3/53: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"\n"}, {"text": "✅ Step completed in 1190ms\n"}, {"text": "📋 Step 4/53: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"\n"}, {"text": "✅ Step completed in 1190ms\n"}, {"text": "📋 Step 5/53: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000\n"}, {"text": "✅ Step completed in 1115ms\n"}, {"text": "📋 Step 6/53: And I wait for element \"getByRole('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000\n"}, {"text": "✅ Step completed in 1113ms\n"}, {"text": "📋 Step 7/53: And I wait for page title to contain \"KPMG Find - Settings\"\n"}, {"text": "✅ Step completed in 8ms\n"}, {"text": "📋 Step 8/53: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\"\n"}, {"text": "[\u001b[33mwarn\u001b[39m]: Failed to highlight locator: TimeoutError: locator.evaluate: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getByRole('button', { name: 'Create a request on behalf of' })\u001b[22m\n\r\n"}, {"text": "✅ Step completed in 1377ms\n"}, {"text": "📋 Step 9/53: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 1110ms\n"}, {"text": "📋 Step 10/53: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\"\n"}, {"text": "✅ Step completed in 34ms\n"}, {"text": "📋 Step 11/53: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON>ult' })\" should be visible\n"}, {"text": "✅ Step completed in 1122ms\n"}, {"text": "📋 Step 12/53: And element \"getByRole('button', { name: 'Can<PERSON>' })\" should be visible\n"}, {"text": "✅ Step completed in 1174ms\n"}, {"text": "📋 Step 13/53: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\"\n"}, {"text": "[\u001b[33mwarn\u001b[39m]: Failed to highlight locator: TimeoutError: locator.evaluate: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getByRole('combobox', { name: 'FeaturedResult' })\u001b[22m\n\r\n"}, {"text": "✅ Step completed in 1319ms\n"}, {"text": "📋 Step 14/53: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000\n"}, {"text": "✅ Step completed in 1099ms\n"}, {"text": "📋 Step 15/53: When I click on \"locator('#menu-').getByText('FeaturedResult')\"\n"}, {"text": "[\u001b[33mwarn\u001b[39m]: Failed to highlight locator: TimeoutError: locator.evaluate: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#menu-').getByText('FeaturedResult')\u001b[22m\n\r\n"}, {"text": "✅ Step completed in 1178ms\n"}, {"text": "📋 Step 16/53: When I click on \"getByRole('textbox', { name: 'Selected User' })\"\n"}, {"text": "[\u001b[33mwarn\u001b[39m]: Failed to highlight locator: TimeoutError: locator.evaluate: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getByRole('textbox', { name: 'Selected User' })\u001b[22m\n\r\n"}, {"text": "✅ Step completed in 1239ms\n"}, {"text": "📋 Step 17/53: Then I wait for element \"getByRole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 1139ms\n"}, {"text": "📋 Step 18/53: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible\n"}, {"text": "✅ Step completed in 1119ms\n"}, {"text": "📋 Step 19/53: And element \"getByRole('searchbox', { name: 'description' })\" should be visible\n"}, {"text": "✅ Step completed in 1135ms\n"}, {"text": "📋 Step 20/53: When I fill \"getBy<PERSON><PERSON>('searchbox', { name: 'description' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"\n"}, {"text": "✅ Step completed in 1182ms\n"}, {"text": "📋 Step 21/53: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 2539ms\n"}, {"text": "📋 Step 22/53: And I wait for element \"getBy<PERSON><PERSON><PERSON>('jagan<PERSON><PERSON>@kpmg.com')\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 1081ms\n"}, {"text": "📋 Step 23/53: When I click on \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\"\n"}, {"text": "✅ Step completed in 1164ms\n"}, {"text": "📋 Step 24/53: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Select' })\" to be visible with timeout 10000\n"}], "stderr": [{"text": "❌ Scenario failed: TC0101- Login and Check the Default Data Source Order page.screenshot: Test ended.\nCall log:\n\u001b[2m  - taking page screenshot\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\n    at PlaywrightCucumberRunner.executeScenario \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-cucumber-runner.ts:224:20\u001b[90m)\u001b[39m\n    at \u001b[90mC:\\workspace\\Playwright_Automation_Framework\\\u001b[39mtests\\cucumber-wrapper.spec.ts:45:9 {\n  [\u001b[32mSymbol(step)\u001b[39m]: {\n    stepId: \u001b[32m'pw:api@273'\u001b[39m,\n    location: {\n      file: \u001b[32m'C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts'\u001b[39m,\n      line: \u001b[33m224\u001b[39m,\n      column: \u001b[33m20\u001b[39m,\n      function: \u001b[32m'PlaywrightCucumberRunner.executeScenario'\u001b[39m\n    },\n    category: \u001b[32m'pw:api'\u001b[39m,\n    title: \u001b[32m'page.screenshot'\u001b[39m,\n    apiName: \u001b[32m'page.screenshot'\u001b[39m,\n    params: {\n      path: \u001b[32m'C:\\\\workspace\\\\Playwright_Automation_Framework\\\\test-results\\\\playwright-data\\\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\\\step-24-before.png'\u001b[39m,\n      fullPage: \u001b[33mtrue\u001b[39m,\n      mask: \u001b[90mundefined\u001b[39m,\n      type: \u001b[32m'png'\u001b[39m\n    },\n    boxedStack: \u001b[90mundefined\u001b[39m,\n    steps: [],\n    attachmentIndices: [],\n    info: TestStepInfoImpl {\n      annotations: [],\n      _testInfo: \u001b[36m[TestInfoImpl]\u001b[39m,\n      _stepId: \u001b[32m'pw:api@273'\u001b[39m\n    },\n    complete: \u001b[36m[Function: complete]\u001b[39m,\n    endWallTime: \u001b[33m1749623495242\u001b[39m,\n    error: {\n      message: \u001b[32m'Error: page.screenshot: Test ended.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - taking page screenshot\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - waiting for fonts to load...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - fonts loaded\\x1B[22m\\n'\u001b[39m,\n      stack: \u001b[32m'Error: page.screenshot: Test ended.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - taking page screenshot\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - waiting for fonts to load...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - fonts loaded\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightCucumberRunner.executeScenario (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts:224:20)\\n'\u001b[39m +\n        \u001b[32m'    at C:\\\\workspace\\\\Playwright_Automation_Framework\\\\tests\\\\cucumber-wrapper.spec.ts:45:9'\u001b[39m,\n      cause: \u001b[90mundefined\u001b[39m\n    }\n  }\n}\n"}], "retry": 0, "startTime": "2025-06-11T06:28:03.294Z", "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "attachments": [{"name": "Step 1 - Before: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--fb638ebdbe80a7756a8e63079085f5659f232f8d.png"}, {"name": "Step 1 - After: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\" (2623ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-2623ms--c301db7883c8ac54fad2dda0014def9f64fa9ef6.png"}, {"name": "Step 2 - Before: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-c181d5006af6c480a37f3073811d405ef4d7d4bb.png"}, {"name": "Step 2 - After: Then I wait for \"getByR<PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000 (8685ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-8685ms--dbcfbedf8ddf18ee8081c74d9174986056ee01a9.png"}, {"name": "Step 3 - Before: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagan<PERSON><PERSON>-kpmg-com--e62e82a00bce764349f7a3421da54aa70d7c5945.png"}, {"name": "Step 3 - After: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\" (1190ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagan<PERSON><PERSON>-kpmg-com-1190ms--ccc4322a28c3a78507b4e014c4195af45a0cc374.png"}, {"name": "Step 4 - Before: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-4---Before-And-I-click-on-getByRole-button-name-Next--5af3fa92aa35cd659ae9cfc51f250822ceddc396.png"}, {"name": "Step 4 - After: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\" (1190ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-4---After-And-I-click-on-getByRole-button-name-Next-1190ms--c007bb4fd799ce37174f0d986ee15b0e40044124.png"}, {"name": "Step 5 - Before: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-52592266963c7f3754edee9d81ffa66787e15c0e.png"}, {"name": "Step 5 - After: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000 (1115ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-5---After-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-1115ms--2ca1f66bf3e72b190e2f83211e4510c325579be3.png"}, {"name": "Step 6 - Before: And I wait for element \"getBy<PERSON>ole('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-6---Before-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-0957de0148fa2a9e786ceb186434aefa3c91c425.png"}, {"name": "Step 6 - After: And I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000 (1113ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-6---After-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-1113ms--36c8b5f343da45056eb899a268a5b9ce58f51a6a.png"}, {"name": "Step 7 - Before: And I wait for page title to contain \"KPMG Find - Settings\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-7---Before-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings--2830df252472b9cc100607a4ff267e3799bb792b.png"}, {"name": "Step 7 - After: And I wait for page title to contain \"KPMG Find - Settings\" (8ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-7---After-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings-8ms--440b71e03148f2c9736050313443160bfbc30b80.png"}, {"name": "Step 8 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-8---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--4eedcce63c9f33ebacac9db4e82df0999591233d.png"}, {"name": "Step 8 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\" (1377ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-8---After-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of-1377ms--de7613aeff7fcc77cea345305fc3df5a7a784d23.png"}, {"name": "Step 9 - Before: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-9---Before-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-bd4ca85717a9d378d4d720c3cd304ba8a6a57294.png"}, {"name": "Step 9 - After: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000 (1110ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-9---After-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-1110ms--d1d4b443945eb98438c260cee200c7c6e0edf538.png"}, {"name": "Step 10 - Before: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-10---Before-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User--b08e829dda5494d267706751167100a153d21ec3.png"}, {"name": "Step 10 - After: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\" (34ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-10---After-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User-34ms--6c0beee0559e01d3164a0afceb2a5017fa85635e.png"}, {"name": "Step 11 - Before: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON><PERSON>' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-11---Before-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-7bedcb2fba06419cdb4ad65ae27440ff22b67482.png"}, {"name": "Step 11 - After: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON><PERSON>' })\" should be visible (1122ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-11---After-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-1122ms--7d0dc856e97a788d11d8d4be8ed1b5bc1f3ecbf8.png"}, {"name": "Step 12 - Before: And element \"getByRole('button', { name: 'Can<PERSON>' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-12---Before-And-element-getByRole-button-name-Cancel-should-be-visible-957be121fb9008568d5fd748f3a224aebe5b8c2f.png"}, {"name": "Step 12 - After: And element \"getByRole('button', { name: '<PERSON><PERSON>' })\" should be visible (1174ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-12---After-And-element-getByRole-button-name-Cancel-should-be-visible-1174ms--cff0ca0bc17760c97bc8f15dce927b9dfbdaa262.png"}, {"name": "Step 13 - Before: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-13---Before-When-I-click-on-getByRole-combobox-name-FeaturedResult--3906cf22d48323e8a2cf8ad26a942a20f3664778.png"}, {"name": "Step 13 - After: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\" (1319ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-13---After-When-I-click-on-getByRole-combobox-name-FeaturedResult-1319ms--7a7fe71c67cc54b656ea3c6ac43856c436e18c59.png"}, {"name": "Step 14 - Before: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-14---Before-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-a70df5b58cc72fdaf34cf91f3563b23443712d47.png"}, {"name": "Step 14 - After: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000 (1099ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-14---After-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-1099ms--6f35a48d8f243ce03ea159355546dc6f50a9cb0d.png"}, {"name": "Step 15 - Before: When I click on \"locator('#menu-').getByText('FeaturedResult')\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-15---Before-When-I-click-on-locator-menu--getByText-FeaturedResult--94e86062326fdba1eedfafecd4ffe23d90507b6c.png"}, {"name": "Step 15 - After: When I click on \"locator('#menu-').getByText('FeaturedResult')\" (1178ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-15---After-When-I-click-on-locator-menu--getByText-FeaturedResult-1178ms--15f40eb865014f05a4e97ea3a43874192eaba18c.png"}, {"name": "Step 16 - Before: When I click on \"getByRole('textbox', { name: 'Selected User' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-16---Before-When-I-click-on-getByRole-textbox-name-Selected-User--0a41f955234b107eefbd2e2e62a51b4c9fcc84e6.png"}, {"name": "Step 16 - After: When I click on \"getByRole('textbox', { name: 'Selected User' })\" (1239ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-16---After-When-I-click-on-getByRole-textbox-name-Selected-User-1239ms--3cc67b571247b005772296153e96f77946c35435.png"}, {"name": "Step 17 - Before: Then I wait for element \"getByRole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-17---Before-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-a5ab2b811b518f8cc1256e6805e474ce5d1f7db0.png"}, {"name": "Step 17 - After: Then I wait for element \"getByRole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000 (1139ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-17---After-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-1139ms--802bb6066aec4ef6d7823437ad37955e882d6b3c.png"}, {"name": "Step 18 - Before: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-18---Before-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-87102f9c9c1e0adef109f27e2255f6ca41060bd2.png"}, {"name": "Step 18 - After: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible (1119ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-18---After-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-1119ms--4b45c44d0cf31d2242e9283a1228d096e70a4856.png"}, {"name": "Step 19 - Before: And element \"getByRole('searchbox', { name: 'description' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-19---Before-And-element-getByRole-searchbox-name-description-should-be-visible-5fc58f0060555ae6533dc2a6adca3161f9e50828.png"}, {"name": "Step 19 - After: And element \"getByRole('searchbox', { name: 'description' })\" should be visible (1135ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-19---After-And-element-getByRole-searchbox-name-description-should-be-visible-1135ms--6270a7bcce1bd38b0084311bf14186854054b6c5.png"}, {"name": "Step 20 - Before: When I fill \"getBy<PERSON><PERSON>('searchbox', { name: 'description' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-20---Before-When-I-fill-getByRole-searchbox-name-description-with-j<PERSON><PERSON><PERSON>-kpmg-com--296d645610b9e778c7872b128c4e1c6a1a9ca400.png"}, {"name": "Step 20 - After: When I fill \"getBy<PERSON><PERSON>('searchbox', { name: 'description' })\" with \"jagan<PERSON><PERSON>@kpmg.com\" (1182ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-20---After-When-I-fill-getByRole-searchbox-name-description-with-j<PERSON><PERSON><PERSON>-kpmg-com-1182ms--56ae2a46bcd48db7d37e94182e6e324866ec1b34.png"}, {"name": "Step 21 - Before: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-21---Before-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-a667d3d5601147b2e0f9c177cf464aea28f37db2.png"}, {"name": "Step 21 - After: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000 (2539ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-21---After-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-2539ms--774fd23deb7b32dd1d94fdc1fedd6e89367d13ac.png"}, {"name": "Step 22 - Before: And I wait for element \"getBy<PERSON><PERSON><PERSON>('j<PERSON><PERSON><PERSON>@kpmg.com')\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-22---Before-And-I-wait-for-element-getByLabel-jagan<PERSON><PERSON>-kpmg-com-to-be-visible-with-timeout-10000-1f2b2e4a680ee4a4f529f09fd70658cd7723b221.png"}, {"name": "screenshot", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\test-failed-1.png"}, {"name": "Step 22 - After: And I wait for element \"getBy<PERSON><PERSON><PERSON>('j<PERSON><PERSON><PERSON>@kpmg.com')\" to be visible with timeout 10000 (1081ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-22---After-And-I-wait-for-element-getByLabel-jagan<PERSON><PERSON>-kpmg-com-to-be-visible-with-timeout-10000-1081ms--6f1c4ba2191561abd9c7ccebe899bfb152696fca.png"}, {"name": "Step 23 - Before: When I click on \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-23---Before-When-I-click-on-getByL<PERSON><PERSON>-K-Jagannatha--f661b8e8d60d06df5027af944e95ed28700c38e4.png"}, {"name": "Step 23 - After: When I click on \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>')\" (1164ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-23---After-When-I-click-on-getByLabel-K-Jagannatha-1164ms--0c6e1891dc25b4d26d413ef7b70e2dad2495819e.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\video.webm"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\trace.zip"}], "errorLocation": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts", "column": 18, "line": 285}}], "status": "skipped"}], "id": "f0a7916b485de33344dc-fc47a07ec800114403a0", "file": "cucumber-wrapper.spec.ts", "line": 34, "column": 11}]}]}], "errors": [], "stats": {"startTime": "2025-06-11T06:28:00.884Z", "duration": 219880.87300000002, "expected": 0, "skipped": 1, "unexpected": 0, "flaky": 0}}