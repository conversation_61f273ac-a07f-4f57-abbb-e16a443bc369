{"config": {"configFile": "C:\\workspace\\Playwright_Automation_Framework\\playwright.config.ts", "rootDir": "C:/workspace/Playwright_Automation_Framework/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report", "open": "always", "host": "localhost", "port": 9323}], ["line", null], ["json", {"outputFile": "test-results/playwright-results.json"}], ["junit", {"outputFile": "test-results/junit-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/workspace/Playwright_Automation_Framework/test-results/playwright-data", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "edge", "name": "edge", "testDir": "C:/workspace/Playwright_Automation_Framework/tests", "testIgnore": [], "testMatch": ["**/*.spec.ts", "**/*.spec.js"], "timeout": 300000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": null}, "suites": [{"title": "cucumber-wrapper.spec.ts", "file": "cucumber-wrapper.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Self Service Featured Results", "file": "cucumber-wrapper.spec.ts", "line": 26, "column": 8, "specs": [{"title": "TC0101- Create Featured Result with Test Data", "ok": false, "tags": [], "tests": [{"timeout": 300000, "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr, @smoke, @create-featured-result"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "expectedStatus": "passed", "projectId": "edge", "projectName": "edge", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 37023, "error": {"message": "TimeoutError: locator.click: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getByRole('button', { name: 'Create a request on behalf of' })\u001b[22m\n", "stack": "TimeoutError: locator.click: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getB<PERSON><PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\u001b[22m\n\n    at PlaywrightWorld.clickElement (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts:660:19)\n    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:62:9)\n    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:361:9)\n    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:197:7)\n    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:232:9)\n    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9", "location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts", "column": 19, "line": 660}, "snippet": "\u001b[90m   at \u001b[39m..\\src\\core\\playwright-world.ts:660\n\n\u001b[0m \u001b[90m 658 |\u001b[39m     }\n \u001b[90m 659 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 660 |\u001b[39m     \u001b[36mawait\u001b[39m element\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 661 |\u001b[39m   }\n \u001b[90m 662 |\u001b[39m\n \u001b[90m 663 |\u001b[39m   \u001b[90m/**\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts", "column": 19, "line": 660}, "message": "TimeoutError: locator.click: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getB<PERSON><PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\src\\core\\playwright-world.ts:660\n\n\u001b[0m \u001b[90m 658 |\u001b[39m     }\n \u001b[90m 659 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 660 |\u001b[39m     \u001b[36mawait\u001b[39m element\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 661 |\u001b[39m   }\n \u001b[90m 662 |\u001b[39m\n \u001b[90m 663 |\u001b[39m   \u001b[90m/**\u001b[39m\u001b[0m\n\u001b[2m    at PlaywrightWorld.clickElement (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts:660:19)\u001b[22m\n\u001b[2m    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:62:9)\u001b[22m\n\u001b[2m    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:361:9)\u001b[22m\n\u001b[2m    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:197:7)\u001b[22m\n\u001b[2m    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:232:9)\u001b[22m\n\u001b[2m    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9\u001b[22m"}, {"message": "Error: browserContext._wrapApiCall: Test ended."}], "stdout": [{"text": "🎬 Starting scenario: TC0101- Create Featured Result with Test Data\n"}, {"text": "📁 Feature: Self Service Featured Results\n"}, {"text": "🏷️ Tags: @combined, @self-service, @fr, @smoke, @create-featured-result\n"}, {"text": "📋 Step 1/46: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\"\n"}, {"text": "[\u001b[31merror\u001b[39m]: ❌ Step failed: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\"\r\n"}], "stderr": [{"text": "❌ Scenario failed: TC0101- Create Featured Result with Test Data locator.click: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getB<PERSON><PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\u001b[22m\n\n    at PlaywrightWorld.clickElement \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-world.ts:660:19\u001b[90m)\u001b[39m\n    at Object.handler \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\step-definition-registry.ts:62:9\u001b[90m)\u001b[39m\n    at StepDefinitionRegistry.executeStep \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\step-definition-registry.ts:361:9\u001b[90m)\u001b[39m\n    at PlaywrightCucumberRunner.executeStep \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-cucumber-runner.ts:197:7\u001b[90m)\u001b[39m\n    at PlaywrightCucumberRunner.executeScenario \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-cucumber-runner.ts:232:9\u001b[90m)\u001b[39m\n    at \u001b[90mC:\\workspace\\Playwright_Automation_Framework\\\u001b[39mtests\\cucumber-wrapper.spec.ts:45:9 {\n  name: \u001b[32m'TimeoutError'\u001b[39m,\n  [\u001b[32mSymbol(step)\u001b[39m]: {\n    stepId: \u001b[32m'pw:api@11'\u001b[39m,\n    location: {\n      file: \u001b[32m'C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-world.ts'\u001b[39m,\n      line: \u001b[33m660\u001b[39m,\n      column: \u001b[33m19\u001b[39m,\n      function: \u001b[32m'PlaywrightWorld.clickElement'\u001b[39m\n    },\n    category: \u001b[32m'pw:api'\u001b[39m,\n    title: \u001b[32m\"locator.getByRole('button', { name: 'Create a request on behalf of' }).click\"\u001b[39m,\n    apiName: \u001b[32m'locator.click'\u001b[39m,\n    params: {\n      selector: \u001b[32m'internal:role=button[name=\"Create a request on behalf of\"i]'\u001b[39m,\n      strict: \u001b[33mtrue\u001b[39m\n    },\n    boxedStack: \u001b[90mundefined\u001b[39m,\n    steps: [],\n    attachmentIndices: [],\n    info: TestStepInfoImpl {\n      annotations: [],\n      _testInfo: \u001b[36m[TestInfoImpl]\u001b[39m,\n      _stepId: \u001b[32m'pw:api@11'\u001b[39m\n    },\n    complete: \u001b[36m[Function: complete]\u001b[39m,\n    endWallTime: \u001b[33m1749627441661\u001b[39m,\n    error: {\n      message: \u001b[32m'TimeoutError: locator.click: Timeout 15000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for getByRole('button', { name: 'Create a request on behalf of' })\\x1B[22m\\n\"\u001b[39m,\n      stack: \u001b[32m'TimeoutError: locator.click: Timeout 15000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for getByRole('button', { name: 'Create a request on behalf of' })\\x1B[22m\\n\"\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightWorld.clickElement (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-world.ts:660:19)\\n'\u001b[39m +\n        \u001b[32m'    at Object.handler (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\step-definition-registry.ts:62:9)\\n'\u001b[39m +\n        \u001b[32m'    at StepDefinitionRegistry.executeStep (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\step-definition-registry.ts:361:9)\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightCucumberRunner.executeStep (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts:197:7)\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightCucumberRunner.executeScenario (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts:232:9)\\n'\u001b[39m +\n        \u001b[32m'    at C:\\\\workspace\\\\Playwright_Automation_Framework\\\\tests\\\\cucumber-wrapper.spec.ts:45:9'\u001b[39m,\n      cause: \u001b[90mundefined\u001b[39m\n    }\n  }\n}\n"}], "retry": 0, "startTime": "2025-06-11T07:37:04.931Z", "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr, @smoke, @create-featured-result"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "attachments": [{"name": "Step 1 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-1---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--f2828ab2648124860832dd644cd1767783c0dec8.png"}, {"name": "Failure Screenshot", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Failure-Screenshot-542d11fe5ab6020a20eb743e1391b31b77491061.png"}, {"name": "screenshot", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\test-failed-1.png"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\trace.zip"}], "errorLocation": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts", "column": 19, "line": 660}}], "status": "unexpected"}], "id": "f0a7916b485de33344dc-b36f91be3d546e93349f", "file": "cucumber-wrapper.spec.ts", "line": 34, "column": 11}]}]}], "errors": [], "stats": {"startTime": "2025-06-11T07:37:03.407Z", "duration": 43804.824, "expected": 0, "skipped": 0, "unexpected": 1, "flaky": 0}}