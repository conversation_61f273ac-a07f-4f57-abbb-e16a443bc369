{"config": {"configFile": "C:\\workspace\\Playwright_Automation_Framework\\playwright.config.ts", "rootDir": "C:/workspace/Playwright_Automation_Framework/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report", "open": "always", "host": "localhost", "port": 9323}], ["line", null], ["json", {"outputFile": "test-results/playwright-results.json"}], ["junit", {"outputFile": "test-results/junit-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/workspace/Playwright_Automation_Framework/test-results/playwright-data", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "C:/workspace/Playwright_Automation_Framework/tests", "testIgnore": [], "testMatch": ["**/*.spec.ts", "**/*.spec.js"], "timeout": 300000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": null}, "suites": [{"title": "cucumber-wrapper.spec.ts", "file": "cucumber-wrapper.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Self Service Featured Results", "file": "cucumber-wrapper.spec.ts", "line": 26, "column": 8, "specs": [{"title": "TC0101- <PERSON><PERSON> and Check the Default Data Source Order", "ok": true, "tags": [], "tests": [{"timeout": 300000, "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 65878, "errors": [], "stdout": [{"text": "🎬 Starting scenario: TC0101- <PERSON><PERSON> and Check the Default Data Source Order\n"}, {"text": "📁 Feature: Self Service Featured Results\n"}, {"text": "🏷️ Tags: @combined, @self-service, @fr\n"}, {"text": "📋 Step 1/27: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\"\n"}, {"text": "✅ Step completed in 3886ms\n"}, {"text": "📋 Step 2/27: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 7241ms\n"}, {"text": "📋 Step 3/27: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"\n"}, {"text": "✅ Step completed in 108ms\n"}, {"text": "📋 Step 4/27: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"\n"}, {"text": "✅ Step completed in 131ms\n"}, {"text": "📋 Step 5/27: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000\n"}, {"text": "✅ Step completed in 13481ms\n"}, {"text": "📋 Step 6/27: And I wait for element \"getByRole('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000\n"}, {"text": "✅ Step completed in 3797ms\n"}, {"text": "📋 Step 7/27: And I wait for page title to contain \"KPMG Find - Settings\"\n"}, {"text": "✅ Step completed in 35ms\n"}, {"text": "📋 Step 8/27: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\"\n"}, {"text": "✅ Step completed in 296ms\n"}, {"text": "📋 Step 9/27: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 49ms\n"}, {"text": "📋 Step 10/27: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\"\n"}, {"text": "[\u001b[33mwarn\u001b[39m]: ⚠️ No step definition found for: \"Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\"\"\r\n"}, {"text": "✅ Step completed in 546ms\n"}, {"text": "📋 Step 11/27: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON>ult' })\" should be visible\n"}, {"text": "[\u001b[33mwarn\u001b[39m]: ⚠️ No step definition found for: \"And element \"getByRole('combobox', { name: 'FeaturedR<PERSON>ult' })\" should be visible\"\r\n"}, {"text": "✅ Step completed in 526ms\n"}, {"text": "📋 Step 12/27: And element \"getByRole('button', { name: 'Can<PERSON>' })\" should be visible\n"}, {"text": "[\u001b[33mwarn\u001b[39m]: ⚠️ No step definition found for: \"And element \"getByRole('button', { name: 'Cancel' })\" should be visible\"\r\n"}, {"text": "✅ Step completed in 539ms\n"}, {"text": "📋 Step 13/27: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\"\n"}, {"text": "✅ Step completed in 129ms\n"}, {"text": "📋 Step 14/27: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000\n"}, {"text": "✅ Step completed in 36ms\n"}, {"text": "📋 Step 15/27: When I click on \"locator('#menu-').getByText('FeaturedResult')\"\n"}, {"text": "✅ Step completed in 104ms\n"}, {"text": "📋 Step 16/27: When I click on \"getByRole('textbox', { name: 'Selected User' })\"\n"}, {"text": "✅ Step completed in 208ms\n"}, {"text": "📋 Step 17/27: Then I wait for element \"getByRole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 46ms\n"}, {"text": "📋 Step 18/27: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible\n"}, {"text": "[\u001b[33mwarn\u001b[39m]: ⚠️ No step definition found for: \"And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible\"\r\n"}, {"text": "✅ Step completed in 540ms\n"}, {"text": "📋 Step 19/27: And element \"getByRole('searchbox', { name: 'description' })\" should be visible\n"}, {"text": "[\u001b[33mwarn\u001b[39m]: ⚠️ No step definition found for: \"And element \"getByRole('searchbox', { name: 'description' })\" should be visible\"\r\n"}, {"text": "✅ Step completed in 537ms\n"}, {"text": "📋 Step 20/27: When I fill \"getBy<PERSON><PERSON>('searchbox', { name: 'description' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"\n"}, {"text": "✅ Step completed in 94ms\n"}, {"text": "📋 Step 21/27: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 2011ms\n"}, {"text": "📋 Step 22/27: And I wait for element \"getBy<PERSON><PERSON><PERSON>('jagan<PERSON><PERSON>@kpmg.com')\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 33ms\n"}, {"text": "📋 Step 23/27: When I click on \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\"\n"}, {"text": "✅ Step completed in 166ms\n"}, {"text": "📋 Step 24/27: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Select' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 43ms\n"}, {"text": "📋 Step 25/27: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Select' })\"\n"}, {"text": "✅ Step completed in 122ms\n"}, {"text": "📋 Step 26/27: Then I wait for element \"getByR<PERSON>('button', { name: 'Create' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 62ms\n"}, {"text": "📋 Step 27/27: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\"\n"}, {"text": "✅ Step completed in 268ms\n"}, {"text": "🎉 Scenario completed successfully: TC0101- Login and Check the Default Data Source Order\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-11T04:45:31.017Z", "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "attachments": [{"name": "Step 1 - Before: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--9f5ae14be54d00e3a62c2b16aaf7cc1484e376f8.png"}, {"name": "Step 1 - After: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\" (3886ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-3886ms--a1a087f0e3c500721ff9ae627164f2a156e6def3.png"}, {"name": "Step 2 - Before: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-1d25b4a7f9215540528bcae43fdce44b2e129411.png"}, {"name": "Step 2 - After: Then I wait for \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000 (7241ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-7241ms--73617745d97bb32ec3dc8ae51580cc93e4a45228.png"}, {"name": "Step 3 - Before: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagan<PERSON><PERSON>-kpmg-com--316da82eef654cae4ad12d47f8fe1ffa375efaa6.png"}, {"name": "Step 3 - After: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\" (108ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagan<PERSON><PERSON>-kpmg-com-108ms--4a9f4d18cec09338fddd537da3174525295c5e1c.png"}, {"name": "Step 4 - Before: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-4---Before-And-I-click-on-getByRole-button-name-Next--996138976132e5a6f4d8dbc56204b69cf5c74dd9.png"}, {"name": "Step 4 - After: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\" (131ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-4---After-And-I-click-on-getByRole-button-name-Next-131ms--3f82437afad13e30321af3d78ac8f5fa484586c5.png"}, {"name": "Step 5 - Before: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-b7eb0d29174c8085f8fb05af67b2219b6e33c240.png"}, {"name": "Step 5 - After: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000 (13481ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-5---After-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-13481ms--3dd4df820ca306c48c56e1222ae6fb296e3fa9cb.png"}, {"name": "Step 6 - Before: And I wait for element \"getBy<PERSON>ole('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-6---Before-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-9a62bc5c83bd200eb2a7dff944a05f818f65627a.png"}, {"name": "Step 6 - After: And I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000 (3797ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-6---After-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-3797ms--17fc87cf7487f9ec6179fdd411bb5055ed61bea4.png"}, {"name": "Step 7 - Before: And I wait for page title to contain \"KPMG Find - Settings\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-7---Before-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings--e0bfffde38f7f27a22628c6df8759960773211f9.png"}, {"name": "Step 7 - After: And I wait for page title to contain \"KPMG Find - Settings\" (35ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-7---After-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings-35ms--4b83bbf3501667bb17e22b4c76e9fb6dc6462bf1.png"}, {"name": "Step 8 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-8---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--589f5cbca4ff4d2eb36011127d110c26f4ff274a.png"}, {"name": "Step 8 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\" (296ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-8---After-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of-296ms--03f7be8836af0bea3f8d59039c40bcb3f07cf292.png"}, {"name": "Step 9 - Before: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-9---Before-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-04edb0a85796d1d977f53b8c4037f0ff4bbd750a.png"}, {"name": "Step 9 - After: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000 (49ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-9---After-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-49ms--c971768b63e560fabc0ff2286d7a76cb56abac01.png"}, {"name": "Step 10 - Before: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-10---Before-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User--165c17b6050f87ae4a620d027e9fd607edf2912b.png"}, {"name": "Step 10 - After: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\" (546ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-10---After-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User-546ms--feb725ac02ac5f75d4bed7dedb852bf3829125f9.png"}, {"name": "Step 11 - Before: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON><PERSON>' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-11---Before-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-f466302481ff8688838ef58344d6d1badc735b93.png"}, {"name": "Step 11 - After: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON><PERSON>' })\" should be visible (526ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-11---After-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-526ms--f84da7efb281bbb123b9457fafd025189069862f.png"}, {"name": "Step 12 - Before: And element \"getByRole('button', { name: 'Can<PERSON>' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-12---Before-And-element-getByRole-button-name-Cancel-should-be-visible-3dd38e4bd0b48ac6cd8e82e706f3ceaa7cb695e4.png"}, {"name": "Step 12 - After: And element \"getByRole('button', { name: '<PERSON><PERSON>' })\" should be visible (539ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-12---After-And-element-getByRole-button-name-Cancel-should-be-visible-539ms--58ad1c6ff6ad0e844b8e1bb1067a683d738f6616.png"}, {"name": "Step 13 - Before: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-13---Before-When-I-click-on-getByRole-combobox-name-FeaturedR<PERSON>ult--b44ff2847df8960e31125198a9f555b9005c3f2c.png"}, {"name": "Step 13 - After: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\" (129ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-13---After-When-I-click-on-getByRole-combobox-name-FeaturedResult-129ms--f94b88f5efa8be97575230b453269d0c8c81733e.png"}, {"name": "Step 14 - Before: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-14---Before-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-b63538221cab462f800c2d0ec0aa353bd3fd08f7.png"}, {"name": "Step 14 - After: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000 (36ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-14---After-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-36ms--00989910ab77e431a1340290b365d1acde0c36d1.png"}, {"name": "Step 15 - Before: When I click on \"locator('#menu-').getByText('FeaturedResult')\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-15---Before-When-I-click-on-locator-menu--getByText-FeaturedResult--d3599577eff0509e5507af39e850dad2e5d8d263.png"}, {"name": "Step 15 - After: When I click on \"locator('#menu-').getByText('FeaturedResult')\" (104ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-15---After-When-I-click-on-locator-menu--getByText-FeaturedResult-104ms--9875c948c18489dbe34e5ed4b757d4f451df60d2.png"}, {"name": "Step 16 - Before: When I click on \"getByRole('textbox', { name: 'Selected User' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-16---Before-When-I-click-on-getByRole-textbox-name-Selected-User--e7453252d51fec73a60460d99d29ff0cc4471d0e.png"}, {"name": "Step 16 - After: When I click on \"getByRole('textbox', { name: 'Selected User' })\" (208ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-16---After-When-I-click-on-getByRole-textbox-name-Selected-User-208ms--b9914e79665458f3c1b4e8d3130e0144e074bd60.png"}, {"name": "Step 17 - Before: Then I wait for element \"getByRole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-17---Before-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-081a4f6989bf3959043643f2ee92f933d294559d.png"}, {"name": "Step 17 - After: Then I wait for element \"getBy<PERSON>ole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000 (46ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-17---After-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-46ms--2d5b1362c025503f2b83d8744564bd6523341c42.png"}, {"name": "Step 18 - Before: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-18---Before-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-c8cb60666241e60459e04119164baf19ede36dec.png"}, {"name": "Step 18 - After: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible (540ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-18---After-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-540ms--0b241fc56d9a2dbbfadd6b051a7b40c998e51f97.png"}, {"name": "Step 19 - Before: And element \"getByRole('searchbox', { name: 'description' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-19---Before-And-element-getByRole-searchbox-name-description-should-be-visible-a3728c020b254f2103b34b0dc6af2b8cd2f29e7e.png"}, {"name": "Step 19 - After: And element \"getByRole('searchbox', { name: 'description' })\" should be visible (537ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-19---After-And-element-getByRole-searchbox-name-description-should-be-visible-537ms--956e1319ff9389a01034dc21c463ebce2e24301c.png"}, {"name": "Step 20 - Before: When I fill \"getBy<PERSON><PERSON>('searchbox', { name: 'description' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-20---Before-When-I-fill-getByRole-searchbox-name-description-with-j<PERSON><PERSON><PERSON>-kpmg-com--6c8452e9192231788e4c5f1b13f164438f545565.png"}, {"name": "Step 20 - After: When I fill \"getBy<PERSON><PERSON>('searchbox', { name: 'description' })\" with \"jagan<PERSON><PERSON>@kpmg.com\" (94ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-20---After-When-I-fill-getByRole-searchbox-name-description-with-j<PERSON><PERSON><PERSON>-kpmg-com-94ms--4229ab33ae24538a749e13b5cc8fbe2b09cb9835.png"}, {"name": "Step 21 - Before: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-21---Before-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-b84c83400fb05aee3210d524537b31cf2168e238.png"}, {"name": "Step 21 - After: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000 (2011ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-21---After-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-2011ms--d4c4b0791a0d6e6f73a25da980f328fbdc70b5aa.png"}, {"name": "Step 22 - Before: And I wait for element \"getBy<PERSON><PERSON><PERSON>('j<PERSON><PERSON><PERSON>@kpmg.com')\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-22---Before-And-I-wait-for-element-getByLabel-jagan<PERSON><PERSON>-kpmg-com-to-be-visible-with-timeout-10000-f62adb5618e58a83aa622f261fe25140be482880.png"}, {"name": "Step 22 - After: And I wait for element \"getBy<PERSON><PERSON><PERSON>('j<PERSON><PERSON><PERSON>@kpmg.com')\" to be visible with timeout 10000 (33ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-22---After-And-I-wait-for-element-getByLabel-jagan<PERSON><PERSON>-kpmg-com-to-be-visible-with-timeout-10000-33ms--b058dc2951ccf78260cf3f1a942f7e24e4da7322.png"}, {"name": "Step 23 - Before: When I click on \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-23---Before-When-I-click-on-getByLabel-K-Jagannatha--45924ca3f70ae46b34dd928e2b059beb8b8cb12d.png"}, {"name": "Step 23 - After: When I click on \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>')\" (166ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-23---After-When-I-click-on-getByL<PERSON><PERSON>-K-Jagannatha-166ms--634c8214023995af38bd6fcbb95f2c7f73bfeae2.png"}, {"name": "Step 24 - Before: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Select' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-24---Before-Then-I-wait-for-element-getByRole-button-name-Select-to-be-visible-with-timeout-10000-7fca1bf9d98139bddf1fe4675a4490de6770efec.png"}, {"name": "Step 24 - After: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON>' })\" to be visible with timeout 10000 (43ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-24---After-Then-I-wait-for-element-getByRole-button-name-Select-to-be-visible-with-timeout-10000-43ms--7d54beb71eec6671431e2b2d2c6eeb87fbfd5b8d.png"}, {"name": "Step 25 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: '<PERSON>' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-25---Before-When-I-click-on-getByRole-button-name-Select--400568b0d60065a269429e72fe5e133e86c46636.png"}, {"name": "Step 25 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: '<PERSON>' })\" (122ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-25---After-When-I-click-on-getByRole-button-name-Select-122ms--d696b7e8bce66eb0c769f7f4d83c5147db7d296e.png"}, {"name": "Step 26 - Before: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Create' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-26---Before-Then-I-wait-for-element-getByRole-button-name-Create-to-be-visible-with-timeout-10000-b66a5d42c0b9a82dafe8f1b46714d7078892f182.png"}, {"name": "Step 26 - After: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Create' })\" to be visible with timeout 10000 (62ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-26---After-Then-I-wait-for-element-getByRole-button-name-Create-to-be-visible-with-timeout-10000-62ms--2001504c604814f105db554e4d183e4a4a125e6b.png"}, {"name": "Step 27 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-27---Before-When-I-click-on-getByRole-button-name-Create--596b90d80a4e62fbdf015e73ac819352ee436e05.png"}, {"name": "Step 27 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\" (268ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-27---After-When-I-click-on-getByRole-button-name-Create-268ms--3956549cd504e824b1bacfc087ce2934770cdb43.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\video.webm"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\trace.zip"}]}], "status": "expected"}], "id": "f0a7916b485de33344dc-83e8223fa8d0c7f1f561", "file": "cucumber-wrapper.spec.ts", "line": 34, "column": 11}]}]}], "errors": [], "stats": {"startTime": "2025-06-11T04:45:28.965Z", "duration": 73899.709, "expected": 1, "skipped": 0, "unexpected": 0, "flaky": 0}}