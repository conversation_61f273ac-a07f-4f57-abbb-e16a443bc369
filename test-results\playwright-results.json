{"config": {"configFile": "C:\\workspace\\Playwright_Automation_Framework\\playwright.config.ts", "rootDir": "C:/workspace/Playwright_Automation_Framework/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report", "open": "always", "host": "localhost", "port": 9323}], ["line", null], ["json", {"outputFile": "test-results/playwright-results.json"}], ["junit", {"outputFile": "test-results/junit-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/workspace/Playwright_Automation_Framework/test-results/playwright-data", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "edge", "name": "edge", "testDir": "C:/workspace/Playwright_Automation_Framework/tests", "testIgnore": [], "testMatch": ["**/*.spec.ts", "**/*.spec.js"], "timeout": 300000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": null}, "suites": [{"title": "cucumber-wrapper.spec.ts", "file": "cucumber-wrapper.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Self Service Featured Results", "file": "cucumber-wrapper.spec.ts", "line": 26, "column": 8, "specs": [{"title": "TC0101- <PERSON><PERSON> and Check the Default Data Source Order", "ok": false, "tags": [], "tests": [{"timeout": 300000, "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "expectedStatus": "passed", "projectId": "edge", "projectName": "edge", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 107189, "error": {"message": "Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 101 elements:\n    1) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('.MuiChip-label').first()\n    2) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()\n    3) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms').nth(1)\n    4) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka getByRole('row', { name: 'Edit Share 75 FeaturedResult' }).getByRole('button').nth(2)\n    5) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()\n    6) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n    7) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).nth(1)\n    8) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms').nth(2)\n    9) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka getByRole('row', { name: 'Edit Share 74 FeaturedResult' }).getByRole('button').nth(2)\n    10) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')\n    ...\n\nCall log:\n\u001b[2m  - expect.toHaveText with timeout 30000ms\u001b[22m\n\u001b[2m  - waiting for locator('.MuiChip-label')\u001b[22m\n", "stack": "Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 101 elements:\n    1) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('.MuiChip-label').first()\n    2) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()\n    3) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms').nth(1)\n    4) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka getByRole('row', { name: 'Edit Share 75 FeaturedResult' }).getByRole('button').nth(2)\n    5) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()\n    6) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n    7) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).nth(1)\n    8) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms').nth(2)\n    9) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka getByRole('row', { name: 'Edit Share 74 FeaturedResult' }).getByRole('button').nth(2)\n    10) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')\n    ...\n\nCall log:\n\u001b[2m  - expect.toHaveText with timeout 30000ms\u001b[22m\n\u001b[2m  - waiting for locator('.MuiChip-label')\u001b[22m\n\n    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:154:31)\n    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:361:23)\n    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:196:31)\n    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:231:20)\n    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9", "location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts", "column": 31, "line": 154}, "snippet": "\u001b[90m   at \u001b[39m..\\src\\core\\step-definition-registry.ts:154\n\n\u001b[0m \u001b[90m 152 |\u001b[39m       \u001b[36masync\u001b[39m (world\u001b[33m:\u001b[39m \u001b[33mPlaywrightWorld\u001b[39m\u001b[33m,\u001b[39m selector\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m text\u001b[33m:\u001b[39m string) \u001b[33m=>\u001b[39m {\n \u001b[90m 153 |\u001b[39m         \u001b[36mconst\u001b[39m element \u001b[33m=\u001b[39m world\u001b[33m.\u001b[39mgetLocator(selector)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 154 |\u001b[39m         \u001b[36mawait\u001b[39m expect(element)\u001b[33m.\u001b[39mtoHaveText(text)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 155 |\u001b[39m       }\u001b[33m,\u001b[39m\n \u001b[90m 156 |\u001b[39m       \u001b[32m'Verify element has exact text (with \"the\" and \"element\")'\u001b[39m\n \u001b[90m 157 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts", "column": 31, "line": 154}, "message": "Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 101 elements:\n    1) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('.MuiChip-label').first()\n    2) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()\n    3) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms').nth(1)\n    4) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka getByRole('row', { name: 'Edit Share 75 FeaturedResult' }).getByRole('button').nth(2)\n    5) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()\n    6) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n    7) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).nth(1)\n    8) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms').nth(2)\n    9) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka getByRole('row', { name: 'Edit Share 74 FeaturedResult' }).getByRole('button').nth(2)\n    10) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')\n    ...\n\nCall log:\n\u001b[2m  - expect.toHaveText with timeout 30000ms\u001b[22m\n\u001b[2m  - waiting for locator('.MuiChip-label')\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\src\\core\\step-definition-registry.ts:154\n\n\u001b[0m \u001b[90m 152 |\u001b[39m       \u001b[36masync\u001b[39m (world\u001b[33m:\u001b[39m \u001b[33mPlaywrightWorld\u001b[39m\u001b[33m,\u001b[39m selector\u001b[33m:\u001b[39m string\u001b[33m,\u001b[39m text\u001b[33m:\u001b[39m string) \u001b[33m=>\u001b[39m {\n \u001b[90m 153 |\u001b[39m         \u001b[36mconst\u001b[39m element \u001b[33m=\u001b[39m world\u001b[33m.\u001b[39mgetLocator(selector)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 154 |\u001b[39m         \u001b[36mawait\u001b[39m expect(element)\u001b[33m.\u001b[39mtoHaveText(text)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 155 |\u001b[39m       }\u001b[33m,\u001b[39m\n \u001b[90m 156 |\u001b[39m       \u001b[32m'Verify element has exact text (with \"the\" and \"element\")'\u001b[39m\n \u001b[90m 157 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:154:31)\u001b[22m\n\u001b[2m    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:361:23)\u001b[22m\n\u001b[2m    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:196:31)\u001b[22m\n\u001b[2m    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:231:20)\u001b[22m\n\u001b[2m    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9\u001b[22m"}], "stdout": [{"text": "🎬 Starting scenario: TC0101- <PERSON><PERSON> and Check the Default Data Source Order\n"}, {"text": "📁 Feature: Self Service Featured Results\n"}, {"text": "🏷️ Tags: @combined, @self-service, @fr\n"}, {"text": "📋 Step 1/48: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\"\n"}, {"text": "✅ Step completed in 3403ms\n"}, {"text": "📋 Step 2/48: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 7391ms\n"}, {"text": "📋 Step 3/48: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"\n"}, {"text": "✅ Step completed in 76ms\n"}, {"text": "📋 Step 4/48: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"\n"}, {"text": "✅ Step completed in 143ms\n"}, {"text": "📋 Step 5/48: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000\n"}, {"text": "✅ Step completed in 12797ms\n"}, {"text": "📋 Step 6/48: And I wait for element \"getByRole('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000\n"}, {"text": "✅ Step completed in 3167ms\n"}, {"text": "📋 Step 7/48: And I wait for page title to contain \"KPMG Find - Settings\"\n"}, {"text": "✅ Step completed in 13ms\n"}, {"text": "📋 Step 8/48: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\"\n"}, {"text": "✅ Step completed in 307ms\n"}, {"text": "📋 Step 9/48: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 39ms\n"}, {"text": "📋 Step 10/48: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\"\n"}, {"text": "✅ Step completed in 65ms\n"}, {"text": "📋 Step 11/48: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON>ult' })\" should be visible\n"}, {"text": "✅ Step completed in 47ms\n"}, {"text": "📋 Step 12/48: And element \"getByRole('button', { name: 'Can<PERSON>' })\" should be visible\n"}, {"text": "✅ Step completed in 45ms\n"}, {"text": "📋 Step 13/48: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON>R<PERSON><PERSON>' })\"\n"}, {"text": "✅ Step completed in 172ms\n"}, {"text": "📋 Step 14/48: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000\n"}, {"text": "✅ Step completed in 35ms\n"}, {"text": "📋 Step 15/48: When I click on \"locator('#menu-').getByText('FeaturedResult')\"\n"}, {"text": "✅ Step completed in 114ms\n"}, {"text": "📋 Step 16/48: When I click on \"getByRole('textbox', { name: 'Selected User' })\"\n"}, {"text": "✅ Step completed in 210ms\n"}, {"text": "📋 Step 17/48: Then I wait for element \"getByRole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 48ms\n"}, {"text": "📋 Step 18/48: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible\n"}, {"text": "✅ Step completed in 44ms\n"}, {"text": "📋 Step 19/48: And element \"getByRole('searchbox', { name: 'description' })\" should be visible\n"}, {"text": "✅ Step completed in 41ms\n"}, {"text": "📋 Step 20/48: When I fill \"getBy<PERSON>ole('searchbox', { name: 'description' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"\n"}, {"text": "✅ Step completed in 114ms\n"}, {"text": "📋 Step 21/48: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 2098ms\n"}, {"text": "📋 Step 22/48: And I wait for element \"getBy<PERSON><PERSON><PERSON>('jagan<PERSON><PERSON>@kpmg.com')\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 40ms\n"}, {"text": "📋 Step 23/48: When I click on \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\"\n"}, {"text": "✅ Step completed in 189ms\n"}, {"text": "📋 Step 24/48: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Select' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 48ms\n"}, {"text": "📋 Step 25/48: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Select' })\"\n"}, {"text": "✅ Step completed in 154ms\n"}, {"text": "📋 Step 26/48: Then I wait for element \"getByR<PERSON>('button', { name: 'Create' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 47ms\n"}, {"text": "📋 Step 27/48: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\"\n"}, {"text": "✅ Step completed in 274ms\n"}, {"text": "📋 Step 28/48: Then I wait for element \"getByText('New Featured Result for K,')\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 44ms\n"}, {"text": "📋 Step 29/48: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Title *' })\" with \"Test Title\"\n"}, {"text": "✅ Step completed in 262ms\n"}, {"text": "📋 Step 30/48: And I click on \"getByRole('textbox', { name: 'Description' })\"\n"}, {"text": "✅ Step completed in 161ms\n"}, {"text": "📋 Step 31/48: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Description' })\" with \"Test Description\"\n"}, {"text": "✅ Step completed in 233ms\n"}, {"text": "📋 Step 32/48: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Url', exact: true })\" with \"https://www.google.com\"\n"}, {"text": "✅ Step completed in 287ms\n"}, {"text": "📋 Step 33/48: And I fill \"getByRole('textbox', { name: 'Search terms (provide a comma' })\" with \"test, terms\"\n"}, {"text": "✅ Step completed in 245ms\n"}, {"text": "📋 Step 34/48: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Comments' })\" with \"Test Comments\"\n"}, {"text": "✅ Step completed in 164ms\n"}, {"text": "📋 Step 35/48: Then element \"getByRole('button', { name: 'Publish' })\" should be visible\n"}, {"text": "✅ Step completed in 50ms\n"}, {"text": "📋 Step 36/48: And element \"getByRole('button', { name: 'Create' })\" should be visible\n"}, {"text": "✅ Step completed in 49ms\n"}, {"text": "📋 Step 37/48: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\"\n"}, {"text": "✅ Step completed in 188ms\n"}, {"text": "📋 Step 38/48: Then I wait for element \"getByRole('heading', { name: 'Confirm creating request!' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 55ms\n"}, {"text": "📋 Step 39/48: And element \"getByRole('button', { name: 'No' })\" should be visible\n"}, {"text": "✅ Step completed in 48ms\n"}, {"text": "📋 Step 40/48: And element \"getByRole('button', { name: 'Yes' })\" should be visible\n"}, {"text": "✅ Step completed in 46ms\n"}, {"text": "📋 Step 41/48: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Yes' })\"\n"}, {"text": "✅ Step completed in 249ms\n"}, {"text": "📋 Step 42/48: Then I wait for element \"getByRole('heading', { name: 'Created request' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 3271ms\n"}, {"text": "📋 Step 43/48: And element \"getByText('Successfully created')\" should be visible\n"}, {"text": "✅ Step completed in 61ms\n"}, {"text": "📋 Step 44/48: And element \"getByRole('button', { name: 'Close' })\" should be visible\n"}, {"text": "✅ Step completed in 74ms\n"}, {"text": "📋 Step 45/48: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Close' })\"\n"}, {"text": "✅ Step completed in 142ms\n"}, {"text": "📋 Step 46/48: Then the \"locator('.MuiChip-label').first()\" element should have exact text \"FeaturedResult\"\n"}, {"text": "[\u001b[31merror\u001b[39m]: ❌ Step failed: Then the \"locator('.MuiChip-label').first()\" element should have exact text \"FeaturedResult\"\r\n"}], "stderr": [{"text": "❌ Scenario failed: TC0101- Login and Check the Default Data Source Order expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 101 elements:\n    1) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('.MuiChip-label').first()\n    2) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()\n    3) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms').nth(1)\n    4) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka getByRole('row', { name: 'Edit Share 75 FeaturedResult' }).getByRole('button').nth(2)\n    5) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()\n    6) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\n    7) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).nth(1)\n    8) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms').nth(2)\n    9) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka getByRole('row', { name: 'Edit Share 74 FeaturedResult' }).getByRole('button').nth(2)\n    10) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')\n    ...\n\nCall log:\n\u001b[2m  - expect.toHaveText with timeout 30000ms\u001b[22m\n\u001b[2m  - waiting for locator('.MuiChip-label')\u001b[22m\n\n    at Object.handler \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\step-definition-registry.ts:154:31\u001b[90m)\u001b[39m\n    at StepDefinitionRegistry.executeStep \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\step-definition-registry.ts:361:23\u001b[90m)\u001b[39m\n    at PlaywrightCucumberRunner.executeStep \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-cucumber-runner.ts:196:31\u001b[90m)\u001b[39m\n    at PlaywrightCucumberRunner.executeScenario \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-cucumber-runner.ts:231:20\u001b[90m)\u001b[39m\n    at \u001b[90mC:\\workspace\\Playwright_Automation_Framework\\\u001b[39mtests\\cucumber-wrapper.spec.ts:45:9 {\n  [\u001b[32mSymbol(step)\u001b[39m]: {\n    stepId: \u001b[32m'expect@236'\u001b[39m,\n    category: \u001b[32m'expect'\u001b[39m,\n    title: \u001b[32m'expect.toHaveText'\u001b[39m,\n    params: { expected: \u001b[32m'FeaturedResult'\u001b[39m },\n    infectParentStepsWithError: \u001b[90mundefined\u001b[39m,\n    boxedStack: \u001b[90mundefined\u001b[39m,\n    location: {\n      file: \u001b[32m'C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\step-definition-registry.ts'\u001b[39m,\n      line: \u001b[33m154\u001b[39m,\n      column: \u001b[33m31\u001b[39m,\n      function: \u001b[32m'Object.handler'\u001b[39m\n    },\n    steps: [],\n    attachmentIndices: [],\n    info: TestStepInfoImpl {\n      annotations: [],\n      _testInfo: \u001b[36m[TestInfoImpl]\u001b[39m,\n      _stepId: \u001b[32m'expect@236'\u001b[39m\n    },\n    complete: \u001b[36m[Function: complete]\u001b[39m,\n    endWallTime: \u001b[33m1749621186326\u001b[39m,\n    error: {\n      message: \u001b[32m\"Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 101 elements:\\n\"\u001b[39m +\n        \u001b[32m`    1) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('.MuiChip-label').first()\\n`\u001b[39m +\n        \u001b[32m`    2) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()\\n`\u001b[39m +\n        \u001b[32m`    3) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms').nth(1)\\n`\u001b[39m +\n        \u001b[32m`    4) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka getByRole('row', { name: 'Edit Share 75 FeaturedResult' }).getByRole('button').nth(2)\\n`\u001b[39m +\n        \u001b[32m`    5) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()\\n`\u001b[39m +\n        \u001b[32m`    6) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\\n`\u001b[39m +\n        \u001b[32m`    7) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).nth(1)\\n`\u001b[39m +\n        \u001b[32m`    8) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms').nth(2)\\n`\u001b[39m +\n        \u001b[32m`    9) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka getByRole('row', { name: 'Edit Share 74 FeaturedResult' }).getByRole('button').nth(2)\\n`\u001b[39m +\n        \u001b[32m`    10) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')\\n`\u001b[39m +\n        \u001b[32m'    ...\\n'\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - expect.toHaveText with timeout 30000ms\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for locator('.MuiChip-label')\\x1B[22m\\n\"\u001b[39m,\n      stack: \u001b[32m\"Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 101 elements:\\n\"\u001b[39m +\n        \u001b[32m`    1) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('.MuiChip-label').first()\\n`\u001b[39m +\n        \u001b[32m`    2) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()\\n`\u001b[39m +\n        \u001b[32m`    3) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms').nth(1)\\n`\u001b[39m +\n        \u001b[32m`    4) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka getByRole('row', { name: 'Edit Share 75 FeaturedResult' }).getByRole('button').nth(2)\\n`\u001b[39m +\n        \u001b[32m`    5) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()\\n`\u001b[39m +\n        \u001b[32m`    6) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">FeaturedResult</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')\\n`\u001b[39m +\n        \u001b[32m`    7) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">test</span> aka locator('span').filter({ hasText: /^test$/ }).nth(1)\\n`\u001b[39m +\n        \u001b[32m`    8) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">terms</span> aka getByText('terms').nth(2)\\n`\u001b[39m +\n        \u001b[32m`    9) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">Submitted</span> aka getByRole('row', { name: 'Edit Share 74 FeaturedResult' }).getByRole('button').nth(2)\\n`\u001b[39m +\n        \u001b[32m`    10) <span class=\"MuiChip-label MuiChip-labelMedium css-qbwvub\">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')\\n`\u001b[39m +\n        \u001b[32m'    ...\\n'\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - expect.toHaveText with timeout 30000ms\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for locator('.MuiChip-label')\\x1B[22m\\n\"\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'    at Object.handler (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\step-definition-registry.ts:154:31)\\n'\u001b[39m +\n        \u001b[32m'    at StepDefinitionRegistry.executeStep (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\step-definition-registry.ts:361:23)\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightCucumberRunner.executeStep (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts:196:31)\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightCucumberRunner.executeScenario (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts:231:20)\\n'\u001b[39m +\n        \u001b[32m'    at C:\\\\workspace\\\\Playwright_Automation_Framework\\\\tests\\\\cucumber-wrapper.spec.ts:45:9'\u001b[39m,\n      cause: \u001b[90mundefined\u001b[39m\n    }\n  }\n}\n"}], "retry": 0, "startTime": "2025-06-11T05:51:29.722Z", "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "attachments": [{"name": "Step 1 - Before: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--fb638ebdbe80a7756a8e63079085f5659f232f8d.png"}, {"name": "Step 1 - After: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\" (3403ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-3403ms--c301db7883c8ac54fad2dda0014def9f64fa9ef6.png"}, {"name": "Step 2 - Before: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-c181d5006af6c480a37f3073811d405ef4d7d4bb.png"}, {"name": "Step 2 - After: Then I wait for \"getByR<PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000 (7391ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-7391ms--dbcfbedf8ddf18ee8081c74d9174986056ee01a9.png"}, {"name": "Step 3 - Before: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagan<PERSON><PERSON>-kpmg-com--e62e82a00bce764349f7a3421da54aa70d7c5945.png"}, {"name": "Step 3 - After: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\" (76ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagan<PERSON><PERSON>-kpmg-com-76ms--ccc4322a28c3a78507b4e014c4195af45a0cc374.png"}, {"name": "Step 4 - Before: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-4---Before-And-I-click-on-getByRole-button-name-Next--5af3fa92aa35cd659ae9cfc51f250822ceddc396.png"}, {"name": "Step 4 - After: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\" (143ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-4---After-And-I-click-on-getByRole-button-name-Next-143ms--c007bb4fd799ce37174f0d986ee15b0e40044124.png"}, {"name": "Step 5 - Before: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-52592266963c7f3754edee9d81ffa66787e15c0e.png"}, {"name": "Step 5 - After: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000 (12797ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-5---After-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-12797ms--2ca1f66bf3e72b190e2f83211e4510c325579be3.png"}, {"name": "Step 6 - Before: And I wait for element \"getBy<PERSON>ole('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-6---Before-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-0957de0148fa2a9e786ceb186434aefa3c91c425.png"}, {"name": "Step 6 - After: And I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000 (3167ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-6---After-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-3167ms--36c8b5f343da45056eb899a268a5b9ce58f51a6a.png"}, {"name": "Step 7 - Before: And I wait for page title to contain \"KPMG Find - Settings\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-7---Before-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings--2830df252472b9cc100607a4ff267e3799bb792b.png"}, {"name": "Step 7 - After: And I wait for page title to contain \"KPMG Find - Settings\" (13ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-7---After-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings-13ms--440b71e03148f2c9736050313443160bfbc30b80.png"}, {"name": "Step 8 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-8---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--4eedcce63c9f33ebacac9db4e82df0999591233d.png"}, {"name": "Step 8 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\" (307ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-8---After-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of-307ms--de7613aeff7fcc77cea345305fc3df5a7a784d23.png"}, {"name": "Step 9 - Before: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-9---Before-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-bd4ca85717a9d378d4d720c3cd304ba8a6a57294.png"}, {"name": "Step 9 - After: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000 (39ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-9---After-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-39ms--d1d4b443945eb98438c260cee200c7c6e0edf538.png"}, {"name": "Step 10 - Before: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-10---Before-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User--b08e829dda5494d267706751167100a153d21ec3.png"}, {"name": "Step 10 - After: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\" (65ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-10---After-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User-65ms--6c0beee0559e01d3164a0afceb2a5017fa85635e.png"}, {"name": "Step 11 - Before: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON><PERSON>' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-11---Before-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-7bedcb2fba06419cdb4ad65ae27440ff22b67482.png"}, {"name": "Step 11 - After: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON><PERSON>' })\" should be visible (47ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-11---After-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-47ms--7d0dc856e97a788d11d8d4be8ed1b5bc1f3ecbf8.png"}, {"name": "Step 12 - Before: And element \"getByRole('button', { name: 'Can<PERSON>' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-12---Before-And-element-getByRole-button-name-Cancel-should-be-visible-957be121fb9008568d5fd748f3a224aebe5b8c2f.png"}, {"name": "Step 12 - After: And element \"getByRole('button', { name: '<PERSON><PERSON>' })\" should be visible (45ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-12---After-And-element-getByRole-button-name-Cancel-should-be-visible-45ms--cff0ca0bc17760c97bc8f15dce927b9dfbdaa262.png"}, {"name": "Step 13 - Before: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-13---Before-When-I-click-on-getByRole-combobox-name-FeaturedResult--3906cf22d48323e8a2cf8ad26a942a20f3664778.png"}, {"name": "Step 13 - After: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\" (172ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-13---After-When-I-click-on-getByRole-combobox-name-FeaturedResult-172ms--7a7fe71c67cc54b656ea3c6ac43856c436e18c59.png"}, {"name": "Step 14 - Before: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-14---Before-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-a70df5b58cc72fdaf34cf91f3563b23443712d47.png"}, {"name": "Step 14 - After: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000 (35ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-14---After-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-35ms--6f35a48d8f243ce03ea159355546dc6f50a9cb0d.png"}, {"name": "Step 15 - Before: When I click on \"locator('#menu-').getByText('FeaturedResult')\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-15---Before-When-I-click-on-locator-menu--getByText-FeaturedResult--94e86062326fdba1eedfafecd4ffe23d90507b6c.png"}, {"name": "Step 15 - After: When I click on \"locator('#menu-').getByText('FeaturedResult')\" (114ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-15---After-When-I-click-on-locator-menu--getByText-FeaturedResult-114ms--15f40eb865014f05a4e97ea3a43874192eaba18c.png"}, {"name": "Step 16 - Before: When I click on \"getByRole('textbox', { name: 'Selected User' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-16---Before-When-I-click-on-getByRole-textbox-name-Selected-User--0a41f955234b107eefbd2e2e62a51b4c9fcc84e6.png"}, {"name": "Step 16 - After: When I click on \"getByRole('textbox', { name: 'Selected User' })\" (210ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-16---After-When-I-click-on-getByRole-textbox-name-Selected-User-210ms--3cc67b571247b005772296153e96f77946c35435.png"}, {"name": "Step 17 - Before: Then I wait for element \"getByRole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-17---Before-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-a5ab2b811b518f8cc1256e6805e474ce5d1f7db0.png"}, {"name": "Step 17 - After: Then I wait for element \"getBy<PERSON>ole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000 (48ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-17---After-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-48ms--802bb6066aec4ef6d7823437ad37955e882d6b3c.png"}, {"name": "Step 18 - Before: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-18---Before-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-87102f9c9c1e0adef109f27e2255f6ca41060bd2.png"}, {"name": "Step 18 - After: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible (44ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-18---After-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-44ms--4b45c44d0cf31d2242e9283a1228d096e70a4856.png"}, {"name": "Step 19 - Before: And element \"getByRole('searchbox', { name: 'description' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-19---Before-And-element-getByRole-searchbox-name-description-should-be-visible-5fc58f0060555ae6533dc2a6adca3161f9e50828.png"}, {"name": "Step 19 - After: And element \"getByRole('searchbox', { name: 'description' })\" should be visible (41ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-19---After-And-element-getByRole-searchbox-name-description-should-be-visible-41ms--6270a7bcce1bd38b0084311bf14186854054b6c5.png"}, {"name": "Step 20 - Before: When I fill \"getBy<PERSON><PERSON>('searchbox', { name: 'description' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-20---Before-When-I-fill-getByRole-searchbox-name-description-with-j<PERSON><PERSON><PERSON>-kpmg-com--296d645610b9e778c7872b128c4e1c6a1a9ca400.png"}, {"name": "Step 20 - After: When I fill \"getBy<PERSON><PERSON>('searchbox', { name: 'description' })\" with \"jagan<PERSON><PERSON>@kpmg.com\" (114ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-20---After-When-I-fill-getByRole-searchbox-name-description-with-j<PERSON><PERSON><PERSON>-kpmg-com-114ms--56ae2a46bcd48db7d37e94182e6e324866ec1b34.png"}, {"name": "Step 21 - Before: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-21---Before-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-a667d3d5601147b2e0f9c177cf464aea28f37db2.png"}, {"name": "Step 21 - After: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000 (2098ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-21---After-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-2098ms--774fd23deb7b32dd1d94fdc1fedd6e89367d13ac.png"}, {"name": "Step 22 - Before: And I wait for element \"getBy<PERSON><PERSON><PERSON>('j<PERSON><PERSON><PERSON>@kpmg.com')\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-22---Before-And-I-wait-for-element-getByLabel-jagan<PERSON><PERSON>-kpmg-com-to-be-visible-with-timeout-10000-1f2b2e4a680ee4a4f529f09fd70658cd7723b221.png"}, {"name": "Step 22 - After: And I wait for element \"getBy<PERSON><PERSON><PERSON>('j<PERSON><PERSON><PERSON>@kpmg.com')\" to be visible with timeout 10000 (40ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-22---After-And-I-wait-for-element-getByLabel-jagan<PERSON><PERSON>-kpmg-com-to-be-visible-with-timeout-10000-40ms--6f1c4ba2191561abd9c7ccebe899bfb152696fca.png"}, {"name": "Step 23 - Before: When I click on \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-23---Before-When-I-click-on-getByL<PERSON><PERSON>-K-Jagannatha--f661b8e8d60d06df5027af944e95ed28700c38e4.png"}, {"name": "Step 23 - After: When I click on \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>')\" (189ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-23---After-When-I-click-on-getByLabel-K-Jagannatha-189ms--0c6e1891dc25b4d26d413ef7b70e2dad2495819e.png"}, {"name": "Step 24 - Before: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Select' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-24---Before-Then-I-wait-for-element-getByRole-button-name-Select-to-be-visible-with-timeout-10000-bb5eb8e5f77fbcb0f91246899856baa37082b5f1.png"}, {"name": "Step 24 - After: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON>' })\" to be visible with timeout 10000 (48ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-24---After-Then-I-wait-for-element-getByRole-button-name-Select-to-be-visible-with-timeout-10000-48ms--a32d4742234e522f6b78dd690c5c9661c3d80978.png"}, {"name": "Step 25 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: '<PERSON>' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-25---Before-When-I-click-on-getByRole-button-name-Select--784c074621942dc2c510460852500b9379eef943.png"}, {"name": "Step 25 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: '<PERSON>' })\" (154ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-25---After-When-I-click-on-getByRole-button-name-Select-154ms--030a565e88e25eb1ea6138b70e48ffaf67eadc8f.png"}, {"name": "Step 26 - Before: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Create' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-26---Before-Then-I-wait-for-element-getByRole-button-name-Create-to-be-visible-with-timeout-10000-b763542a98a9e9a27c3bbec0d1f93bb240d98f18.png"}, {"name": "Step 26 - After: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Create' })\" to be visible with timeout 10000 (47ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-26---After-Then-I-wait-for-element-getByRole-button-name-Create-to-be-visible-with-timeout-10000-47ms--39ba764a1889bba09a3995d9209d7a26f25ccd31.png"}, {"name": "Step 27 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-27---Before-When-I-click-on-getByRole-button-name-Create--8e7a60b33ee67790229e72b208aadbc4897f118c.png"}, {"name": "Step 27 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\" (274ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-27---After-When-I-click-on-getByRole-button-name-Create-274ms--f638d07db4935971a66beae72b711a0f5d596704.png"}, {"name": "Step 28 - Before: Then I wait for element \"getByText('New Featured Result for K,')\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-28---Before-Then-I-wait-for-element-getByText-New-Featured-Result-for-K-to-be-visible-with-timeout-10000-f6477c9e86f1705a8d160c93e8ce515b3d921c2f.png"}, {"name": "Step 28 - After: Then I wait for element \"getByText('New Featured Result for K,')\" to be visible with timeout 10000 (44ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-28---After-Then-I-wait-for-element-getByText-New-Featured-Result-for-K-to-be-visible-with-timeout-10000-44ms--a0012631de2fb079e13fe26732633ed1185319e5.png"}, {"name": "Step 29 - Before: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Title *' })\" with \"Test Title\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-29---Before-When-I-fill-getByRole-textbox-name-Title-with-Test-Title--82933b98f89d9cf88893dc264e823f8c7d2ce9b6.png"}, {"name": "Step 29 - After: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Title *' })\" with \"Test Title\" (262ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-29---After-When-I-fill-getByRole-textbox-name-Title-with-Test-Title-262ms--db9cf60d14043dae766f1c16ef0b9ff8f637d4ab.png"}, {"name": "Step 30 - Before: And I click on \"getBy<PERSON><PERSON>('textbox', { name: 'Description' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-30---Before-And-I-click-on-getByRole-textbox-name-Description--a67b68a467ab99acbd172c45b7b496d39517c125.png"}, {"name": "Step 30 - After: And I click on \"getByRole('textbox', { name: 'Description' })\" (161ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-30---After-And-I-click-on-getByRole-textbox-name-Description-161ms--79ba7af4788bc9bc66bf08949db892e7b07a603f.png"}, {"name": "Step 31 - Before: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Description' })\" with \"Test Description\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-31---Before-And-I-fill-getByRole-textbox-name-Description-with-Test-Description--3cd56fb99987ed2c19025e3616e82dce1ce4a012.png"}, {"name": "Step 31 - After: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Description' })\" with \"Test Description\" (233ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-31---After-And-I-fill-getByRole-textbox-name-Description-with-Test-Description-233ms--91d3e7d084b18b235bbeadce25e6d9fb69e6b616.png"}, {"name": "Step 32 - Before: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Url', exact: true })\" with \"https://www.google.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-32---Before-And-I-fill-getByRole-textbox-name-Url-exact-true-with-https-www-google-com--7355a88b6740ec610b38bc906ef1e290fb7850d8.png"}, {"name": "Step 32 - After: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Url', exact: true })\" with \"https://www.google.com\" (287ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-32---After-And-I-fill-getByRole-textbox-name-Url-exact-true-with-https-www-google-com-287ms--486aae39feab3e6029bd4ab984ea60e34df4a880.png"}, {"name": "Step 33 - Before: And I fill \"getByRole('textbox', { name: 'Search terms (provide a comma' })\" with \"test, terms\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-33---Before-And-I-fill-getByRole-textbox-name-Search-terms-provide-a-comma-with-test-terms--906e0aa12cc436c993c49482b69dc63ef297ede6.png"}, {"name": "Step 33 - After: And I fill \"getByRole('textbox', { name: 'Search terms (provide a comma' })\" with \"test, terms\" (245ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-33---After-And-I-fill-getByRole-textbox-name-Search-terms-provide-a-comma-with-test-terms-245ms--c9be61477a7cfdef1555a0b2c9ab47593f132f1b.png"}, {"name": "Step 34 - Before: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Comments' })\" with \"Test Comments\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-34---Before-And-I-fill-getByRole-textbox-name-Comments-with-Test-Comments--933b0a81cb3cecc18c576997317b639b717447e3.png"}, {"name": "Step 34 - After: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Comments' })\" with \"Test Comments\" (164ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-34---After-And-I-fill-getByRole-textbox-name-Comments-with-Test-Comments-164ms--a46037c757140a99aec49b80aab1a7f3cc89e3f1.png"}, {"name": "Step 35 - Before: Then element \"getByRole('button', { name: 'Publish' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-35---Before-Then-element-getByRole-button-name-Publish-should-be-visible-4859addfb544867be65570cf12cf5040e8f19c0e.png"}, {"name": "Step 35 - After: Then element \"getByRole('button', { name: 'Publish' })\" should be visible (50ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-35---After-Then-element-getByRole-button-name-Publish-should-be-visible-50ms--bc84e347bf3cd98a93bb1249e85c11d45cad322e.png"}, {"name": "Step 36 - Before: And element \"getByRole('button', { name: 'Create' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-36---Before-And-element-getByRole-button-name-Create-should-be-visible-60b14abc7c54b9557b7c1009cd3391299d90478c.png"}, {"name": "Step 36 - After: And element \"getByRole('button', { name: 'Create' })\" should be visible (49ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-36---After-And-element-getByRole-button-name-Create-should-be-visible-49ms--5b75414da0bb9149eaf05b206259c0d24b904eab.png"}, {"name": "Step 37 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-37---Before-When-I-click-on-getByRole-button-name-Create--0685233e6b4b5df3684db5313cc2d69cea7c3901.png"}, {"name": "Step 37 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\" (188ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-37---After-When-I-click-on-getByRole-button-name-Create-188ms--e6b4bc7979739bd87dea1595d10b925579a24fd2.png"}, {"name": "Step 38 - Before: Then I wait for element \"getByRole('heading', { name: 'Confirm creating request!' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-38---Before-Then-I-wait-for-element-getByRole-heading-name-Confirm-creating-request-to-be-visible-with-timeout-10000-f040403e64c6f578dff52635336dc503f23a964c.png"}, {"name": "Step 38 - After: Then I wait for element \"getByRole('heading', { name: 'Confirm creating request!' })\" to be visible with timeout 10000 (55ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-38---After-Then-I-wait-for-element-getByRole-heading-name-Confirm-creating-request-to-be-visible-with-timeout-10000-55ms--2361ea02963cb7c36cc8b7f6cd3115ed1d2daa19.png"}, {"name": "Step 39 - Before: And element \"getByRole('button', { name: 'No' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-39---Before-And-element-getByRole-button-name-No-should-be-visible-f8292252ad93b237c47fcb89cf63e18438a1ef39.png"}, {"name": "Step 39 - After: And element \"getByRole('button', { name: 'No' })\" should be visible (48ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-39---After-And-element-getByRole-button-name-No-should-be-visible-48ms--3d04cc3ecc7dfae7e05e26c5a6575b89e3757b7e.png"}, {"name": "Step 40 - Before: And element \"getByRole('button', { name: 'Yes' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-40---Before-And-element-getByRole-button-name-Yes-should-be-visible-b72c4e8d2b2d70108907846b86815ffa2402addc.png"}, {"name": "Step 40 - After: And element \"getByRole('button', { name: 'Yes' })\" should be visible (46ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-40---After-And-element-getByRole-button-name-Yes-should-be-visible-46ms--ae100be68515a51e3ad8aaf9b5a6d1d500b3be8a.png"}, {"name": "Step 41 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Yes' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-41---Before-When-I-click-on-getByRole-button-name-Yes--bdd7ea5003bc4df16177a92c5e8f7e7fdc5b374a.png"}, {"name": "Step 41 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Yes' })\" (249ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-41---After-When-I-click-on-getByRole-button-name-Yes-249ms--5d42406a28fe7bb256248c03b730b0fee8395ed1.png"}, {"name": "Step 42 - Before: Then I wait for element \"getByRole('heading', { name: 'Created request' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-42---Before-Then-I-wait-for-element-getByRole-heading-name-Created-request-to-be-visible-with-timeout-10000-7b2a99e87f7ed9e397530d5a037529e8bd2f459b.png"}, {"name": "Step 42 - After: Then I wait for element \"getByRole('heading', { name: 'Created request' })\" to be visible with timeout 10000 (3271ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-42---After-Then-I-wait-for-element-getByRole-heading-name-Created-request-to-be-visible-with-timeout-10000-3271ms--dbcd180575d769b28edee8336855745b38f26371.png"}, {"name": "Step 43 - Before: And element \"getByText('Successfully created')\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-43---Before-And-element-getByText-Successfully-created-should-be-visible-4d65ace2813b8bb08dc075fb312c5639326d59dc.png"}, {"name": "Step 43 - After: And element \"getByText('Successfully created')\" should be visible (61ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-43---After-And-element-getByText-Successfully-created-should-be-visible-61ms--66bded44ce792ed3e967bc0a7ef2ce111c979f0c.png"}, {"name": "Step 44 - Before: And element \"getBy<PERSON>ole('button', { name: 'Close' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-44---Before-And-element-getByRole-button-name-Close-should-be-visible-43a7c2b34180deaef4cc694fd086a48cdeb73636.png"}, {"name": "Step 44 - After: And element \"getByRole('button', { name: 'Close' })\" should be visible (74ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-44---After-And-element-getByRole-button-name-Close-should-be-visible-74ms--03c3bae3c57115c34e14dcafd4970e658ea25c0e.png"}, {"name": "Step 45 - Before: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Close' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-45---Before-And-I-click-on-getByRole-button-name-Close--18e335649770501dcb5d367a6475f45f7652944f.png"}, {"name": "Step 45 - After: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Close' })\" (142ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-45---After-And-I-click-on-getByRole-button-name-Close-142ms--ef4a32e8361edfed98a3aa0ce7b7580a78612bbc.png"}, {"name": "Step 46 - Before: Then the \"locator('.MuiChip-label').first()\" element should have exact text \"FeaturedResult\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-46---Before-Then-the-locator-MuiChip-label-first-element-should-have-exact-text-FeaturedResult--deea9332765fc95ab58fdc37bf0a4fa92337f12b.png"}, {"name": "Failure Screenshot", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Failure-Screenshot-68a893639dca7abc0f3f6e6663cdaf94bc586cc7.png"}, {"name": "screenshot", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\trace.zip"}], "errorLocation": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts", "column": 31, "line": 154}}], "status": "unexpected"}], "id": "f0a7916b485de33344dc-fc47a07ec800114403a0", "file": "cucumber-wrapper.spec.ts", "line": 34, "column": 11}]}]}], "errors": [], "stats": {"startTime": "2025-06-11T05:51:27.576Z", "duration": 116823.975, "expected": 0, "skipped": 0, "unexpected": 1, "flaky": 0}}