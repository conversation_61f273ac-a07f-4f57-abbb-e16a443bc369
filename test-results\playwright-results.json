{"config": {"configFile": "C:\\workspace\\Playwright_Automation_Framework\\playwright.config.ts", "rootDir": "C:/workspace/Playwright_Automation_Framework/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report", "open": "always", "host": "localhost", "port": 9323}], ["line", null], ["json", {"outputFile": "test-results/playwright-results.json"}], ["junit", {"outputFile": "test-results/junit-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/workspace/Playwright_Automation_Framework/test-results/playwright-data", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "edge", "name": "edge", "testDir": "C:/workspace/Playwright_Automation_Framework/tests", "testIgnore": [], "testMatch": ["**/*.spec.ts", "**/*.spec.js"], "timeout": 300000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": null}, "suites": [{"title": "cucumber-wrapper.spec.ts", "file": "cucumber-wrapper.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Self Service Featured Results", "file": "cucumber-wrapper.spec.ts", "line": 26, "column": 8, "specs": [{"title": "TC0101- <PERSON><PERSON> and Check the Default Data Source Order", "ok": true, "tags": [], "tests": [{"timeout": 300000, "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "expectedStatus": "passed", "projectId": "edge", "projectName": "edge", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 76005, "errors": [], "stdout": [{"text": "🎬 Starting scenario: TC0101- <PERSON><PERSON> and Check the Default Data Source Order\n"}, {"text": "📁 Feature: Self Service Featured Results\n"}, {"text": "🏷️ Tags: @combined, @self-service, @fr\n"}, {"text": "📋 Step 1/53: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\"\n"}, {"text": "✅ Step completed in 2261ms\n"}, {"text": "📋 Step 2/53: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 6672ms\n"}, {"text": "📋 Step 3/53: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"\n"}, {"text": "✅ Step completed in 176ms\n"}, {"text": "📋 Step 4/53: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"\n"}, {"text": "✅ Step completed in 111ms\n"}, {"text": "📋 Step 5/53: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000\n"}, {"text": "✅ Step completed in 11877ms\n"}, {"text": "📋 Step 6/53: And I wait for element \"getByRole('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000\n"}, {"text": "✅ Step completed in 2529ms\n"}, {"text": "📋 Step 7/53: And I wait for page title to contain \"KPMG Find - Settings\"\n"}, {"text": "✅ Step completed in 7ms\n"}, {"text": "📋 Step 8/53: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\"\n"}, {"text": "✅ Step completed in 196ms\n"}, {"text": "📋 Step 9/53: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 59ms\n"}, {"text": "📋 Step 10/53: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\"\n"}, {"text": "✅ Step completed in 46ms\n"}, {"text": "📋 Step 11/53: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON>ult' })\" should be visible\n"}, {"text": "✅ Step completed in 55ms\n"}, {"text": "📋 Step 12/53: And element \"getByRole('button', { name: 'Can<PERSON>' })\" should be visible\n"}, {"text": "✅ Step completed in 61ms\n"}, {"text": "📋 Step 13/53: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\"\n"}, {"text": "✅ Step completed in 255ms\n"}, {"text": "📋 Step 14/53: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000\n"}, {"text": "✅ Step completed in 55ms\n"}, {"text": "📋 Step 15/53: When I click on \"locator('#menu-').getByText('FeaturedResult')\"\n"}, {"text": "✅ Step completed in 109ms\n"}, {"text": "📋 Step 16/53: When I click on \"getByRole('textbox', { name: 'Selected User' })\"\n"}, {"text": "✅ Step completed in 159ms\n"}, {"text": "📋 Step 17/53: Then I wait for element \"getByRole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 58ms\n"}, {"text": "📋 Step 18/53: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible\n"}, {"text": "✅ Step completed in 41ms\n"}, {"text": "📋 Step 19/53: And element \"getByRole('searchbox', { name: 'description' })\" should be visible\n"}, {"text": "✅ Step completed in 55ms\n"}, {"text": "📋 Step 20/53: When I fill \"getBy<PERSON><PERSON>('searchbox', { name: 'description' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"\n"}, {"text": "✅ Step completed in 95ms\n"}, {"text": "📋 Step 21/53: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 3571ms\n"}, {"text": "📋 Step 22/53: And I wait for element \"getBy<PERSON><PERSON><PERSON>('jagan<PERSON><PERSON>@kpmg.com')\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 46ms\n"}, {"text": "📋 Step 23/53: When I click on \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\"\n"}, {"text": "✅ Step completed in 114ms\n"}, {"text": "📋 Step 24/53: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Select' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 71ms\n"}, {"text": "📋 Step 25/53: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Select' })\"\n"}, {"text": "✅ Step completed in 240ms\n"}, {"text": "📋 Step 26/53: Then I wait for element \"getByR<PERSON>('button', { name: 'Create' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 70ms\n"}, {"text": "📋 Step 27/53: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\"\n"}, {"text": "✅ Step completed in 266ms\n"}, {"text": "📋 Step 28/53: Then I wait for element \"getByText('New Featured Result for K,')\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 44ms\n"}, {"text": "📋 Step 29/53: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Title *' })\" with \"Test Title\"\n"}, {"text": "✅ Step completed in 147ms\n"}, {"text": "📋 Step 30/53: And I click on \"getByRole('textbox', { name: 'Description' })\"\n"}, {"text": "✅ Step completed in 111ms\n"}, {"text": "📋 Step 31/53: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Description' })\" with \"Test Description\"\n"}, {"text": "✅ Step completed in 91ms\n"}, {"text": "📋 Step 32/53: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Url', exact: true })\" with \"https://www.google.com\"\n"}, {"text": "✅ Step completed in 189ms\n"}, {"text": "📋 Step 33/53: And I fill \"getByRole('textbox', { name: 'Search terms (provide a comma' })\" with \"test, terms\"\n"}, {"text": "✅ Step completed in 128ms\n"}, {"text": "📋 Step 34/53: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Comments' })\" with \"Test Comments\"\n"}, {"text": "✅ Step completed in 100ms\n"}, {"text": "📋 Step 35/53: Then element \"getByRole('button', { name: 'Publish' })\" should be visible\n"}, {"text": "✅ Step completed in 82ms\n"}, {"text": "📋 Step 36/53: And element \"getByRole('button', { name: 'Create' })\" should be visible\n"}, {"text": "✅ Step completed in 116ms\n"}, {"text": "📋 Step 37/53: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\"\n"}, {"text": "✅ Step completed in 218ms\n"}, {"text": "📋 Step 38/53: Then I wait for element \"getByRole('heading', { name: 'Confirm creating request!' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 52ms\n"}, {"text": "📋 Step 39/53: And element \"getByRole('button', { name: 'No' })\" should be visible\n"}, {"text": "✅ Step completed in 51ms\n"}, {"text": "📋 Step 40/53: And element \"getByRole('button', { name: 'Yes' })\" should be visible\n"}, {"text": "✅ Step completed in 61ms\n"}, {"text": "📋 Step 41/53: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Yes' })\"\n"}, {"text": "✅ Step completed in 244ms\n"}, {"text": "📋 Step 42/53: Then I wait for element \"getByRole('heading', { name: 'Created request' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 2072ms\n"}, {"text": "📋 Step 43/53: And element \"getByText('Successfully created')\" should be visible\n"}, {"text": "✅ Step completed in 55ms\n"}, {"text": "📋 Step 44/53: And element \"getByRole('button', { name: 'Close' })\" should be visible\n"}, {"text": "✅ Step completed in 53ms\n"}, {"text": "📋 Step 45/53: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Close' })\"\n"}, {"text": "✅ Step completed in 156ms\n"}, {"text": "📋 Step 46/53: Then the \"(//table)[1]//tr[2]/td[4]//span\" element should have exact text \"FeaturedResult\"\n"}, {"text": "✅ Step completed in 28ms\n"}, {"text": "📋 Step 47/53: And the \"(//table)[1]//tr[2]/td[5]/div\" element should have exact text \"Test Title\"\n"}, {"text": "✅ Step completed in 14ms\n"}, {"text": "📋 Step 48/53: And the \"(//table)[1]//tr[2]/td[6]/div\" element should have exact text \"Test Description\"\n"}, {"text": "✅ Step completed in 23ms\n"}, {"text": "📋 Step 49/53: And the \"(//table)[1]//tr[2]/td[8]/div\" element should have exact text \"https://www.google.com\"\n"}, {"text": "✅ Step completed in 15ms\n"}, {"text": "📋 Step 50/53: And the \"((//table)[1]//tr[2]/td[9]//span)[1]\" element should have exact text \"test\"\n"}, {"text": "✅ Step completed in 13ms\n"}, {"text": "📋 Step 51/53: And the \"((//table)[1]//tr[2]/td[9]//span)[2]\" element should have exact text \"terms\"\n"}, {"text": "✅ Step completed in 17ms\n"}, {"text": "📋 Step 52/53: And the \"(//table)[1]//tr[2]/td[10]//span\" element should have exact text \"Submitted\"\n"}, {"text": "✅ Step completed in 16ms\n"}, {"text": "📋 Step 53/53: And the \"(//table)[1]//tr[2]/td[13]\" element should have exact text \"K, Jagannatha\"\n"}, {"text": "✅ Step completed in 31ms\n"}, {"text": "🎉 Scenario completed successfully: TC0101- Login and Check the Default Data Source Order\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-11T06:48:50.998Z", "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "attachments": [{"name": "Step 1 - Before: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--fb638ebdbe80a7756a8e63079085f5659f232f8d.png"}, {"name": "Step 1 - After: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\" (2261ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-2261ms--c301db7883c8ac54fad2dda0014def9f64fa9ef6.png"}, {"name": "Step 2 - Before: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-c181d5006af6c480a37f3073811d405ef4d7d4bb.png"}, {"name": "Step 2 - After: Then I wait for \"getByR<PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000 (6672ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-6672ms--dbcfbedf8ddf18ee8081c74d9174986056ee01a9.png"}, {"name": "Step 3 - Before: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagan<PERSON><PERSON>-kpmg-com--e62e82a00bce764349f7a3421da54aa70d7c5945.png"}, {"name": "Step 3 - After: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\" (176ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagan<PERSON><PERSON>-kpmg-com-176ms--ccc4322a28c3a78507b4e014c4195af45a0cc374.png"}, {"name": "Step 4 - Before: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-4---Before-And-I-click-on-getByRole-button-name-Next--5af3fa92aa35cd659ae9cfc51f250822ceddc396.png"}, {"name": "Step 4 - After: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\" (111ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-4---After-And-I-click-on-getByRole-button-name-Next-111ms--c007bb4fd799ce37174f0d986ee15b0e40044124.png"}, {"name": "Step 5 - Before: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-52592266963c7f3754edee9d81ffa66787e15c0e.png"}, {"name": "Step 5 - After: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000 (11877ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-5---After-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-11877ms--2ca1f66bf3e72b190e2f83211e4510c325579be3.png"}, {"name": "Step 6 - Before: And I wait for element \"getBy<PERSON>ole('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-6---Before-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-0957de0148fa2a9e786ceb186434aefa3c91c425.png"}, {"name": "Step 6 - After: And I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000 (2529ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-6---After-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-2529ms--36c8b5f343da45056eb899a268a5b9ce58f51a6a.png"}, {"name": "Step 7 - Before: And I wait for page title to contain \"KPMG Find - Settings\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-7---Before-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings--2830df252472b9cc100607a4ff267e3799bb792b.png"}, {"name": "Step 7 - After: And I wait for page title to contain \"KPMG Find - Settings\" (7ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-7---After-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings-7ms--440b71e03148f2c9736050313443160bfbc30b80.png"}, {"name": "Step 8 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-8---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--4eedcce63c9f33ebacac9db4e82df0999591233d.png"}, {"name": "Step 8 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\" (196ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-8---After-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of-196ms--de7613aeff7fcc77cea345305fc3df5a7a784d23.png"}, {"name": "Step 9 - Before: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-9---Before-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-bd4ca85717a9d378d4d720c3cd304ba8a6a57294.png"}, {"name": "Step 9 - After: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000 (59ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-9---After-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-59ms--d1d4b443945eb98438c260cee200c7c6e0edf538.png"}, {"name": "Step 10 - Before: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-10---Before-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User--b08e829dda5494d267706751167100a153d21ec3.png"}, {"name": "Step 10 - After: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\" (46ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-10---After-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User-46ms--6c0beee0559e01d3164a0afceb2a5017fa85635e.png"}, {"name": "Step 11 - Before: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON><PERSON>' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-11---Before-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-7bedcb2fba06419cdb4ad65ae27440ff22b67482.png"}, {"name": "Step 11 - After: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON><PERSON>' })\" should be visible (55ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-11---After-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-55ms--7d0dc856e97a788d11d8d4be8ed1b5bc1f3ecbf8.png"}, {"name": "Step 12 - Before: And element \"getByRole('button', { name: 'Can<PERSON>' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-12---Before-And-element-getByRole-button-name-Cancel-should-be-visible-957be121fb9008568d5fd748f3a224aebe5b8c2f.png"}, {"name": "Step 12 - After: And element \"getByRole('button', { name: '<PERSON><PERSON>' })\" should be visible (61ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-12---After-And-element-getByRole-button-name-Cancel-should-be-visible-61ms--cff0ca0bc17760c97bc8f15dce927b9dfbdaa262.png"}, {"name": "Step 13 - Before: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-13---Before-When-I-click-on-getByRole-combobox-name-FeaturedResult--3906cf22d48323e8a2cf8ad26a942a20f3664778.png"}, {"name": "Step 13 - After: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\" (255ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-13---After-When-I-click-on-getByRole-combobox-name-FeaturedResult-255ms--7a7fe71c67cc54b656ea3c6ac43856c436e18c59.png"}, {"name": "Step 14 - Before: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-14---Before-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-a70df5b58cc72fdaf34cf91f3563b23443712d47.png"}, {"name": "Step 14 - After: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000 (55ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-14---After-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-55ms--6f35a48d8f243ce03ea159355546dc6f50a9cb0d.png"}, {"name": "Step 15 - Before: When I click on \"locator('#menu-').getByText('FeaturedResult')\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-15---Before-When-I-click-on-locator-menu--getByText-FeaturedResult--94e86062326fdba1eedfafecd4ffe23d90507b6c.png"}, {"name": "Step 15 - After: When I click on \"locator('#menu-').getByText('FeaturedResult')\" (109ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-15---After-When-I-click-on-locator-menu--getByText-FeaturedResult-109ms--15f40eb865014f05a4e97ea3a43874192eaba18c.png"}, {"name": "Step 16 - Before: When I click on \"getByRole('textbox', { name: 'Selected User' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-16---Before-When-I-click-on-getByRole-textbox-name-Selected-User--0a41f955234b107eefbd2e2e62a51b4c9fcc84e6.png"}, {"name": "Step 16 - After: When I click on \"getByRole('textbox', { name: 'Selected User' })\" (159ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-16---After-When-I-click-on-getByRole-textbox-name-Selected-User-159ms--3cc67b571247b005772296153e96f77946c35435.png"}, {"name": "Step 17 - Before: Then I wait for element \"getByRole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-17---Before-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-a5ab2b811b518f8cc1256e6805e474ce5d1f7db0.png"}, {"name": "Step 17 - After: Then I wait for element \"getBy<PERSON>ole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000 (58ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-17---After-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-58ms--802bb6066aec4ef6d7823437ad37955e882d6b3c.png"}, {"name": "Step 18 - Before: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-18---Before-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-87102f9c9c1e0adef109f27e2255f6ca41060bd2.png"}, {"name": "Step 18 - After: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible (41ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-18---After-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-41ms--4b45c44d0cf31d2242e9283a1228d096e70a4856.png"}, {"name": "Step 19 - Before: And element \"getByRole('searchbox', { name: 'description' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-19---Before-And-element-getByRole-searchbox-name-description-should-be-visible-5fc58f0060555ae6533dc2a6adca3161f9e50828.png"}, {"name": "Step 19 - After: And element \"getByRole('searchbox', { name: 'description' })\" should be visible (55ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-19---After-And-element-getByRole-searchbox-name-description-should-be-visible-55ms--6270a7bcce1bd38b0084311bf14186854054b6c5.png"}, {"name": "Step 20 - Before: When I fill \"getBy<PERSON><PERSON>('searchbox', { name: 'description' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-20---Before-When-I-fill-getByRole-searchbox-name-description-with-j<PERSON><PERSON><PERSON>-kpmg-com--296d645610b9e778c7872b128c4e1c6a1a9ca400.png"}, {"name": "Step 20 - After: When I fill \"getBy<PERSON><PERSON>('searchbox', { name: 'description' })\" with \"jagan<PERSON><PERSON>@kpmg.com\" (95ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-20---After-When-I-fill-getByRole-searchbox-name-description-with-j<PERSON><PERSON><PERSON>-kpmg-com-95ms--56ae2a46bcd48db7d37e94182e6e324866ec1b34.png"}, {"name": "Step 21 - Before: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-21---Before-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-a667d3d5601147b2e0f9c177cf464aea28f37db2.png"}, {"name": "Step 21 - After: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000 (3571ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-21---After-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-3571ms--774fd23deb7b32dd1d94fdc1fedd6e89367d13ac.png"}, {"name": "Step 22 - Before: And I wait for element \"getBy<PERSON><PERSON><PERSON>('j<PERSON><PERSON><PERSON>@kpmg.com')\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-22---Before-And-I-wait-for-element-getByLabel-jagan<PERSON><PERSON>-kpmg-com-to-be-visible-with-timeout-10000-1f2b2e4a680ee4a4f529f09fd70658cd7723b221.png"}, {"name": "Step 22 - After: And I wait for element \"getBy<PERSON><PERSON><PERSON>('j<PERSON><PERSON><PERSON>@kpmg.com')\" to be visible with timeout 10000 (46ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-22---After-And-I-wait-for-element-getByLabel-jagan<PERSON><PERSON>-kpmg-com-to-be-visible-with-timeout-10000-46ms--6f1c4ba2191561abd9c7ccebe899bfb152696fca.png"}, {"name": "Step 23 - Before: When I click on \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-23---Before-When-I-click-on-getByL<PERSON><PERSON>-K-Jagannatha--f661b8e8d60d06df5027af944e95ed28700c38e4.png"}, {"name": "Step 23 - After: When I click on \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>')\" (114ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-23---After-When-I-click-on-getByLabel-K-Jagannatha-114ms--0c6e1891dc25b4d26d413ef7b70e2dad2495819e.png"}, {"name": "Step 24 - Before: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Select' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-24---Before-Then-I-wait-for-element-getByRole-button-name-Select-to-be-visible-with-timeout-10000-bb5eb8e5f77fbcb0f91246899856baa37082b5f1.png"}, {"name": "Step 24 - After: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON>' })\" to be visible with timeout 10000 (71ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-24---After-Then-I-wait-for-element-getByRole-button-name-Select-to-be-visible-with-timeout-10000-71ms--a32d4742234e522f6b78dd690c5c9661c3d80978.png"}, {"name": "Step 25 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: '<PERSON>' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-25---Before-When-I-click-on-getByRole-button-name-Select--784c074621942dc2c510460852500b9379eef943.png"}, {"name": "Step 25 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: '<PERSON>' })\" (240ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-25---After-When-I-click-on-getByRole-button-name-Select-240ms--030a565e88e25eb1ea6138b70e48ffaf67eadc8f.png"}, {"name": "Step 26 - Before: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Create' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-26---Before-Then-I-wait-for-element-getByRole-button-name-Create-to-be-visible-with-timeout-10000-b763542a98a9e9a27c3bbec0d1f93bb240d98f18.png"}, {"name": "Step 26 - After: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Create' })\" to be visible with timeout 10000 (70ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-26---After-Then-I-wait-for-element-getByRole-button-name-Create-to-be-visible-with-timeout-10000-70ms--39ba764a1889bba09a3995d9209d7a26f25ccd31.png"}, {"name": "Step 27 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-27---Before-When-I-click-on-getByRole-button-name-Create--8e7a60b33ee67790229e72b208aadbc4897f118c.png"}, {"name": "Step 27 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\" (266ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-27---After-When-I-click-on-getByRole-button-name-Create-266ms--f638d07db4935971a66beae72b711a0f5d596704.png"}, {"name": "Step 28 - Before: Then I wait for element \"getByText('New Featured Result for K,')\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-28---Before-Then-I-wait-for-element-getByText-New-Featured-Result-for-K-to-be-visible-with-timeout-10000-f6477c9e86f1705a8d160c93e8ce515b3d921c2f.png"}, {"name": "Step 28 - After: Then I wait for element \"getByText('New Featured Result for K,')\" to be visible with timeout 10000 (44ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-28---After-Then-I-wait-for-element-getByText-New-Featured-Result-for-K-to-be-visible-with-timeout-10000-44ms--a0012631de2fb079e13fe26732633ed1185319e5.png"}, {"name": "Step 29 - Before: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Title *' })\" with \"Test Title\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-29---Before-When-I-fill-getByRole-textbox-name-Title-with-Test-Title--82933b98f89d9cf88893dc264e823f8c7d2ce9b6.png"}, {"name": "Step 29 - After: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Title *' })\" with \"Test Title\" (147ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-29---After-When-I-fill-getByRole-textbox-name-Title-with-Test-Title-147ms--db9cf60d14043dae766f1c16ef0b9ff8f637d4ab.png"}, {"name": "Step 30 - Before: And I click on \"getBy<PERSON><PERSON>('textbox', { name: 'Description' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-30---Before-And-I-click-on-getByRole-textbox-name-Description--a67b68a467ab99acbd172c45b7b496d39517c125.png"}, {"name": "Step 30 - After: And I click on \"getByRole('textbox', { name: 'Description' })\" (111ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-30---After-And-I-click-on-getByRole-textbox-name-Description-111ms--79ba7af4788bc9bc66bf08949db892e7b07a603f.png"}, {"name": "Step 31 - Before: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Description' })\" with \"Test Description\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-31---Before-And-I-fill-getByRole-textbox-name-Description-with-Test-Description--3cd56fb99987ed2c19025e3616e82dce1ce4a012.png"}, {"name": "Step 31 - After: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Description' })\" with \"Test Description\" (91ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-31---After-And-I-fill-getByRole-textbox-name-Description-with-Test-Description-91ms--91d3e7d084b18b235bbeadce25e6d9fb69e6b616.png"}, {"name": "Step 32 - Before: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Url', exact: true })\" with \"https://www.google.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-32---Before-And-I-fill-getByRole-textbox-name-Url-exact-true-with-https-www-google-com--7355a88b6740ec610b38bc906ef1e290fb7850d8.png"}, {"name": "Step 32 - After: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Url', exact: true })\" with \"https://www.google.com\" (189ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-32---After-And-I-fill-getByRole-textbox-name-Url-exact-true-with-https-www-google-com-189ms--486aae39feab3e6029bd4ab984ea60e34df4a880.png"}, {"name": "Step 33 - Before: And I fill \"getByRole('textbox', { name: 'Search terms (provide a comma' })\" with \"test, terms\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-33---Before-And-I-fill-getByRole-textbox-name-Search-terms-provide-a-comma-with-test-terms--906e0aa12cc436c993c49482b69dc63ef297ede6.png"}, {"name": "Step 33 - After: And I fill \"getByRole('textbox', { name: 'Search terms (provide a comma' })\" with \"test, terms\" (128ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-33---After-And-I-fill-getByRole-textbox-name-Search-terms-provide-a-comma-with-test-terms-128ms--c9be61477a7cfdef1555a0b2c9ab47593f132f1b.png"}, {"name": "Step 34 - Before: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Comments' })\" with \"Test Comments\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-34---Before-And-I-fill-getByRole-textbox-name-Comments-with-Test-Comments--933b0a81cb3cecc18c576997317b639b717447e3.png"}, {"name": "Step 34 - After: And I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Comments' })\" with \"Test Comments\" (100ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-34---After-And-I-fill-getByRole-textbox-name-Comments-with-Test-Comments-100ms--a46037c757140a99aec49b80aab1a7f3cc89e3f1.png"}, {"name": "Step 35 - Before: Then element \"getByRole('button', { name: 'Publish' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-35---Before-Then-element-getByRole-button-name-Publish-should-be-visible-4859addfb544867be65570cf12cf5040e8f19c0e.png"}, {"name": "Step 35 - After: Then element \"getByRole('button', { name: 'Publish' })\" should be visible (82ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-35---After-Then-element-getByRole-button-name-Publish-should-be-visible-82ms--bc84e347bf3cd98a93bb1249e85c11d45cad322e.png"}, {"name": "Step 36 - Before: And element \"getByRole('button', { name: 'Create' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-36---Before-And-element-getByRole-button-name-Create-should-be-visible-60b14abc7c54b9557b7c1009cd3391299d90478c.png"}, {"name": "Step 36 - After: And element \"getByRole('button', { name: 'Create' })\" should be visible (116ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-36---After-And-element-getByRole-button-name-Create-should-be-visible-116ms--5b75414da0bb9149eaf05b206259c0d24b904eab.png"}, {"name": "Step 37 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-37---Before-When-I-click-on-getByRole-button-name-Create--0685233e6b4b5df3684db5313cc2d69cea7c3901.png"}, {"name": "Step 37 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create' })\" (218ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-37---After-When-I-click-on-getByRole-button-name-Create-218ms--e6b4bc7979739bd87dea1595d10b925579a24fd2.png"}, {"name": "Step 38 - Before: Then I wait for element \"getByRole('heading', { name: 'Confirm creating request!' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-38---Before-Then-I-wait-for-element-getByRole-heading-name-Confirm-creating-request-to-be-visible-with-timeout-10000-f040403e64c6f578dff52635336dc503f23a964c.png"}, {"name": "Step 38 - After: Then I wait for element \"getByRole('heading', { name: 'Confirm creating request!' })\" to be visible with timeout 10000 (52ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-38---After-Then-I-wait-for-element-getByRole-heading-name-Confirm-creating-request-to-be-visible-with-timeout-10000-52ms--2361ea02963cb7c36cc8b7f6cd3115ed1d2daa19.png"}, {"name": "Step 39 - Before: And element \"getByRole('button', { name: 'No' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-39---Before-And-element-getByRole-button-name-No-should-be-visible-f8292252ad93b237c47fcb89cf63e18438a1ef39.png"}, {"name": "Step 39 - After: And element \"getByRole('button', { name: 'No' })\" should be visible (51ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-39---After-And-element-getByRole-button-name-No-should-be-visible-51ms--3d04cc3ecc7dfae7e05e26c5a6575b89e3757b7e.png"}, {"name": "Step 40 - Before: And element \"getByRole('button', { name: 'Yes' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-40---Before-And-element-getByRole-button-name-Yes-should-be-visible-b72c4e8d2b2d70108907846b86815ffa2402addc.png"}, {"name": "Step 40 - After: And element \"getByRole('button', { name: 'Yes' })\" should be visible (61ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-40---After-And-element-getByRole-button-name-Yes-should-be-visible-61ms--ae100be68515a51e3ad8aaf9b5a6d1d500b3be8a.png"}, {"name": "Step 41 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Yes' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-41---Before-When-I-click-on-getByRole-button-name-Yes--bdd7ea5003bc4df16177a92c5e8f7e7fdc5b374a.png"}, {"name": "Step 41 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Yes' })\" (244ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-41---After-When-I-click-on-getByRole-button-name-Yes-244ms--5d42406a28fe7bb256248c03b730b0fee8395ed1.png"}, {"name": "Step 42 - Before: Then I wait for element \"getByRole('heading', { name: 'Created request' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-42---Before-Then-I-wait-for-element-getByRole-heading-name-Created-request-to-be-visible-with-timeout-10000-7b2a99e87f7ed9e397530d5a037529e8bd2f459b.png"}, {"name": "Step 42 - After: Then I wait for element \"getByRole('heading', { name: 'Created request' })\" to be visible with timeout 10000 (2072ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-42---After-Then-I-wait-for-element-getByRole-heading-name-Created-request-to-be-visible-with-timeout-10000-2072ms--dbcd180575d769b28edee8336855745b38f26371.png"}, {"name": "Step 43 - Before: And element \"getByText('Successfully created')\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-43---Before-And-element-getByText-Successfully-created-should-be-visible-4d65ace2813b8bb08dc075fb312c5639326d59dc.png"}, {"name": "Step 43 - After: And element \"getByText('Successfully created')\" should be visible (55ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-43---After-And-element-getByText-Successfully-created-should-be-visible-55ms--66bded44ce792ed3e967bc0a7ef2ce111c979f0c.png"}, {"name": "Step 44 - Before: And element \"getBy<PERSON>ole('button', { name: 'Close' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-44---Before-And-element-getByRole-button-name-Close-should-be-visible-43a7c2b34180deaef4cc694fd086a48cdeb73636.png"}, {"name": "Step 44 - After: And element \"getByRole('button', { name: 'Close' })\" should be visible (53ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-44---After-And-element-getByRole-button-name-Close-should-be-visible-53ms--03c3bae3c57115c34e14dcafd4970e658ea25c0e.png"}, {"name": "Step 45 - Before: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Close' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-45---Before-And-I-click-on-getByRole-button-name-Close--18e335649770501dcb5d367a6475f45f7652944f.png"}, {"name": "Step 45 - After: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Close' })\" (156ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-45---After-And-I-click-on-getByRole-button-name-Close-156ms--ef4a32e8361edfed98a3aa0ce7b7580a78612bbc.png"}, {"name": "Step 46 - Before: Then the \"(//table)[1]//tr[2]/td[4]//span\" element should have exact text \"FeaturedResult\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-46---Before-Then-the-table-1-tr-2-td-4-span-element-should-have-exact-text-FeaturedResult--deea9332765fc95ab58fdc37bf0a4fa92337f12b.png"}, {"name": "Step 46 - After: Then the \"(//table)[1]//tr[2]/td[4]//span\" element should have exact text \"FeaturedResult\" (28ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-46---After-Then-the-table-1-tr-2-td-4-span-element-should-have-exact-text-FeaturedResult-28ms--5ee7e7553a794d69ec29765252b4f0a1a2275c7a.png"}, {"name": "Step 47 - Before: And the \"(//table)[1]//tr[2]/td[5]/div\" element should have exact text \"Test Title\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-47---Before-And-the-table-1-tr-2-td-5-div-element-should-have-exact-text-Test-Title--5de218d31f70d9b38e84127afd395571ce4a12c3.png"}, {"name": "Step 47 - After: And the \"(//table)[1]//tr[2]/td[5]/div\" element should have exact text \"Test Title\" (14ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-47---After-And-the-table-1-tr-2-td-5-div-element-should-have-exact-text-Test-Title-14ms--8e3d090abeba1b38d8dd6bc1b53e357bed814051.png"}, {"name": "Step 48 - Before: And the \"(//table)[1]//tr[2]/td[6]/div\" element should have exact text \"Test Description\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-48---Before-And-the-table-1-tr-2-td-6-div-element-should-have-exact-text-Test-Description--52fb7b78240a089d991d76539057b88df95d32b7.png"}, {"name": "Step 48 - After: And the \"(//table)[1]//tr[2]/td[6]/div\" element should have exact text \"Test Description\" (23ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-48---After-And-the-table-1-tr-2-td-6-div-element-should-have-exact-text-Test-Description-23ms--6cb79ff34ca1ce3326b2c7a40a40deae3801eb19.png"}, {"name": "Step 49 - Before: And the \"(//table)[1]//tr[2]/td[8]/div\" element should have exact text \"https://www.google.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-49---Before-And-the-table-1-tr-2-td-8-div-element-should-have-exact-text-https-www-google-com--6680d1ed3aa5834da085ea877391b6c553720471.png"}, {"name": "Step 49 - After: And the \"(//table)[1]//tr[2]/td[8]/div\" element should have exact text \"https://www.google.com\" (15ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-49---After-And-the-table-1-tr-2-td-8-div-element-should-have-exact-text-https-www-google-com-15ms--2404d2cb95ba38d950e921c6d77a3672a8229ab2.png"}, {"name": "Step 50 - Before: And the \"((//table)[1]//tr[2]/td[9]//span)[1]\" element should have exact text \"test\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-50---Before-And-the-table-1-tr-2-td-9-span-1-element-should-have-exact-text-test--4ac45d27fb0a1a30001465aacff0fef3624fda9b.png"}, {"name": "Step 50 - After: And the \"((//table)[1]//tr[2]/td[9]//span)[1]\" element should have exact text \"test\" (13ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-50---After-And-the-table-1-tr-2-td-9-span-1-element-should-have-exact-text-test-13ms--d3e34774b08f4a0b27f9517fa8673f641db62a1a.png"}, {"name": "Step 51 - Before: And the \"((//table)[1]//tr[2]/td[9]//span)[2]\" element should have exact text \"terms\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-51---Before-And-the-table-1-tr-2-td-9-span-2-element-should-have-exact-text-terms--6c2555324f5fcf4997c3b33e688b36a0d6bac2c2.png"}, {"name": "Step 51 - After: And the \"((//table)[1]//tr[2]/td[9]//span)[2]\" element should have exact text \"terms\" (17ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-51---After-And-the-table-1-tr-2-td-9-span-2-element-should-have-exact-text-terms-17ms--8f01241e354523c6fbb88656b7edef3d46a1c0ca.png"}, {"name": "Step 52 - Before: And the \"(//table)[1]//tr[2]/td[10]//span\" element should have exact text \"Submitted\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-52---Before-And-the-table-1-tr-2-td-10-span-element-should-have-exact-text-Submitted--bb7691f9e156e3ec79429a8b550ab644c8dd25bf.png"}, {"name": "Step 52 - After: And the \"(//table)[1]//tr[2]/td[10]//span\" element should have exact text \"Submitted\" (16ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-52---After-And-the-table-1-tr-2-td-10-span-element-should-have-exact-text-Submitted-16ms--e7329686ce03ba188dfdc5471219a07f74a1d388.png"}, {"name": "Step 53 - Before: And the \"(//table)[1]//tr[2]/td[13]\" element should have exact text \"K, Jagannatha\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-53---Before-And-the-table-1-tr-2-td-13-element-should-have-exact-text-K-<PERSON><PERSON><PERSON><PERSON>--4ee73695e068577768853c290086200d37b5e538.png"}, {"name": "Step 53 - After: And the \"(//table)[1]//tr[2]/td[13]\" element should have exact text \"<PERSON>, <PERSON>agan<PERSON>a\" (31ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-53---After-And-the-table-1-tr-2-td-13-element-should-have-exact-text-K-<PERSON>agannatha-31ms--6e43bacf8dcf3cbac5f9352bf3f689762484934e.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\video.webm"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\trace.zip"}]}], "status": "expected"}], "id": "f0a7916b485de33344dc-fc47a07ec800114403a0", "file": "cucumber-wrapper.spec.ts", "line": 34, "column": 11}]}]}], "errors": [], "stats": {"startTime": "2025-06-11T06:48:49.500Z", "duration": 84502.565, "expected": 1, "skipped": 0, "unexpected": 0, "flaky": 0}}