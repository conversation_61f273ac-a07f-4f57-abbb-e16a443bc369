{"config": {"configFile": "C:\\workspace\\Playwright_Automation_Framework\\playwright.config.ts", "rootDir": "C:/workspace/Playwright_Automation_Framework/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report", "open": "always", "host": "localhost", "port": 9323}], ["line", null], ["json", {"outputFile": "test-results/playwright-results.json"}], ["junit", {"outputFile": "test-results/junit-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/workspace/Playwright_Automation_Framework/test-results/playwright-data", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "edge", "name": "edge", "testDir": "C:/workspace/Playwright_Automation_Framework/tests", "testIgnore": [], "testMatch": ["**/*.spec.ts", "**/*.spec.js"], "timeout": 300000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": null}, "suites": [{"title": "cucumber-wrapper.spec.ts", "file": "cucumber-wrapper.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Self Service Featured Results", "file": "cucumber-wrapper.spec.ts", "line": 26, "column": 8, "specs": [{"title": "TC0101- <PERSON><PERSON> and Check the Default Data Source Order", "ok": false, "tags": [], "tests": [{"timeout": 300000, "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "expectedStatus": "passed", "projectId": "edge", "projectName": "edge", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 32974, "error": {"message": "Error: page.screenshot: Target page, context or browser has been closed", "stack": "Error: page.screenshot: Target page, context or browser has been closed\n    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:270:18)\n    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9", "location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts", "column": 18, "line": 270}, "snippet": "\u001b[90m   at \u001b[39m..\\src\\core\\playwright-cucumber-runner.ts:270\n\n\u001b[0m \u001b[90m 268 |\u001b[39m       \u001b[90m// Take failure screenshot\u001b[39m\n \u001b[90m 269 |\u001b[39m       \u001b[36mconst\u001b[39m failureScreenshot \u001b[33m=\u001b[39m path\u001b[33m.\u001b[39mjoin(testInfo\u001b[33m.\u001b[39moutputDir\u001b[33m,\u001b[39m \u001b[32m'failure-screenshot.png'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 270 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mscreenshot({ path\u001b[33m:\u001b[39m failureScreenshot\u001b[33m,\u001b[39m fullPage\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 271 |\u001b[39m       \u001b[36mawait\u001b[39m testInfo\u001b[33m.\u001b[39mattach(\u001b[32m'Failure Screenshot'\u001b[39m\u001b[33m,\u001b[39m {\n \u001b[90m 272 |\u001b[39m         path\u001b[33m:\u001b[39m failureScreenshot\u001b[33m,\u001b[39m\n \u001b[90m 273 |\u001b[39m         contentType\u001b[33m:\u001b[39m \u001b[32m'image/png'\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts", "column": 18, "line": 270}, "message": "Error: page.screenshot: Target page, context or browser has been closed\n\n\u001b[90m   at \u001b[39m..\\src\\core\\playwright-cucumber-runner.ts:270\n\n\u001b[0m \u001b[90m 268 |\u001b[39m       \u001b[90m// Take failure screenshot\u001b[39m\n \u001b[90m 269 |\u001b[39m       \u001b[36mconst\u001b[39m failureScreenshot \u001b[33m=\u001b[39m path\u001b[33m.\u001b[39mjoin(testInfo\u001b[33m.\u001b[39moutputDir\u001b[33m,\u001b[39m \u001b[32m'failure-screenshot.png'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 270 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mscreenshot({ path\u001b[33m:\u001b[39m failureScreenshot\u001b[33m,\u001b[39m fullPage\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 271 |\u001b[39m       \u001b[36mawait\u001b[39m testInfo\u001b[33m.\u001b[39mattach(\u001b[32m'Failure Screenshot'\u001b[39m\u001b[33m,\u001b[39m {\n \u001b[90m 272 |\u001b[39m         path\u001b[33m:\u001b[39m failureScreenshot\u001b[33m,\u001b[39m\n \u001b[90m 273 |\u001b[39m         contentType\u001b[33m:\u001b[39m \u001b[32m'image/png'\u001b[39m\u001b[0m\n\u001b[2m    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:270:18)\u001b[22m\n\u001b[2m    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9\u001b[22m"}], "stdout": [{"text": "🎬 Starting scenario: TC0101- <PERSON><PERSON> and Check the Default Data Source Order\n"}, {"text": "📁 Feature: Self Service Featured Results\n"}, {"text": "🏷️ Tags: @combined, @self-service, @fr\n"}, {"text": "📋 Step 1/48: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\"\n"}, {"text": "✅ Step completed in 3955ms\n"}, {"text": "📋 Step 2/48: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 7760ms\n"}, {"text": "📋 Step 3/48: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"\n"}, {"text": "✅ Step completed in 165ms\n"}, {"text": "📋 Step 4/48: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"\n"}, {"text": "✅ Step completed in 149ms\n"}, {"text": "📋 Step 5/48: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000\n"}, {"text": "[\u001b[31merror\u001b[39m]: ❌ Step failed: Then I wait for element \"getByRole('button', { name: 'Setting<PERSON>' })\" to be visible with timeout 60000\r\n"}], "stderr": [{"text": "❌ Scenario failed: TC0101- <PERSON><PERSON> and Check the Default Data Source Order locator.waitFor: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for getBy<PERSON><PERSON>('button', { name: 'Setting<PERSON>' }) to be visible\u001b[22m\n\u001b[2m    - waiting for\" https://gfs2.glb.kworld.kpmg.com/adfs/ls/?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%26es…\" navigation to finish...\u001b[22m\n\u001b[2m    - navigated to \"https://gfs2.glb.kworld.kpmg.com/adfs/ls/?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%26es…\"\u001b[22m\n\u001b[2m    - waiting for\" https://gfs2.glb.kworld.kpmg.com/adfs/ls/wia?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%2…\" navigation to finish...\u001b[22m\n\u001b[2m    - navigated to \"https://gfs2.glb.kworld.kpmg.com/adfs/ls/wia?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%2…\"\u001b[22m\n\u001b[2m    - waiting for navigation to finish...\u001b[22m\n\u001b[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P8QIuVg8NsOEQ7Nf3s512kuvL5G7OxMKOPmxzhEMydDLJ5qH-4HAuc01XR…\"\u001b[22m\n\u001b[2m    - waiting for\" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…\" navigation to finish...\u001b[22m\n\u001b[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P-cFXrRPM0fOc4CVwfbjlzlc4VBw37FZ5g_OY9tIE14qqTI2_ajrzNwWaQ…\"\u001b[22m\n\u001b[2m    - waiting for\" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…\" navigation to finish...\u001b[22m\n\u001b[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P88UQ75CI-owpCWOB_Pz5G2URf8ettxDsrcbeMC5E_tLTH6Mz9Fesda5aP…\"\u001b[22m\n\u001b[2m    - waiting for\" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…\" navigation to finish...\u001b[22m\n\u001b[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P9xVue0Fy26j3ilvJ_X9QgorPTVk2k2OUMMBq0i1BMi-yjf3tGWtKjwU8V…\"\u001b[22m\n\u001b[2m    - waiting for\" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…\" navigation to finish...\u001b[22m\n\u001b[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P9zH864M85G3xj2XC2VI--Wkt4zXZqXvzTJROLRib3FIjNjPInITG9memX…\"\u001b[22m\n\u001b[2m    - waiting for\" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…\" navigation to finish...\u001b[22m\n\u001b[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P_vhH2fCBb2qS11Vx9o7o9nbk1QKm5QWfl5x5y6JB7pSHU6EKDY49vJ5Nq…\"\u001b[22m\n\n    at PlaywrightWorld.waitForElement \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-world.ts:436:19\u001b[90m)\u001b[39m\n    at Object.handler \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\step-definition-registry.ts:189:21\u001b[90m)\u001b[39m\n    at StepDefinitionRegistry.executeStep \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\step-definition-registry.ts:361:23\u001b[90m)\u001b[39m\n    at PlaywrightCucumberRunner.executeStep \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-cucumber-runner.ts:196:31\u001b[90m)\u001b[39m\n    at PlaywrightCucumberRunner.executeScenario \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-cucumber-runner.ts:231:20\u001b[90m)\u001b[39m\n    at \u001b[90mC:\\workspace\\Playwright_Automation_Framework\\\u001b[39mtests\\cucumber-wrapper.spec.ts:45:9 {\n  [\u001b[32mSymbol(step)\u001b[39m]: {\n    stepId: \u001b[32m'pw:api@31'\u001b[39m,\n    location: {\n      file: \u001b[32m'C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-world.ts'\u001b[39m,\n      line: \u001b[33m436\u001b[39m,\n      column: \u001b[33m19\u001b[39m,\n      function: \u001b[32m'PlaywrightWorld.waitForElement'\u001b[39m\n    },\n    category: \u001b[32m'pw:api'\u001b[39m,\n    title: \u001b[32m\"locator.getByRole('button', { name: 'Settings' }).waitFor\"\u001b[39m,\n    apiName: \u001b[32m'locator.waitFor'\u001b[39m,\n    params: {\n      selector: \u001b[32m'internal:role=button[name=\"Settings\"i]'\u001b[39m,\n      strict: \u001b[33mtrue\u001b[39m,\n      omitReturnValue: \u001b[33mtrue\u001b[39m,\n      state: \u001b[32m'visible'\u001b[39m,\n      timeout: \u001b[33m60000\u001b[39m\n    },\n    boxedStack: \u001b[90mundefined\u001b[39m,\n    steps: [],\n    attachmentIndices: [],\n    info: TestStepInfoImpl {\n      annotations: [],\n      _testInfo: \u001b[36m[TestInfoImpl]\u001b[39m,\n      _stepId: \u001b[32m'pw:api@31'\u001b[39m\n    },\n    complete: \u001b[36m[Function: complete]\u001b[39m,\n    endWallTime: \u001b[33m1749620603574\u001b[39m,\n    error: {\n      message: \u001b[32m'Error: locator.waitFor: Target page, context or browser has been closed\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for getByRole('button', { name: 'Settings' }) to be visible\\x1B[22m\\n\"\u001b[39m +\n        \u001b[32m'\\x1B[2m    - waiting for\" https://gfs2.glb.kworld.kpmg.com/adfs/ls/?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%26es…\" navigation to finish...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - navigated to \"https://gfs2.glb.kworld.kpmg.com/adfs/ls/?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%26es…\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - waiting for\" https://gfs2.glb.kworld.kpmg.com/adfs/ls/wia?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%2…\" navigation to finish...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - navigated to \"https://gfs2.glb.kworld.kpmg.com/adfs/ls/wia?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%2…\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - waiting for navigation to finish...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P8QIuVg8NsOEQ7Nf3s512kuvL5G7OxMKOPmxzhEMydDLJ5qH-4HAuc01XR…\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - waiting for\" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…\" navigation to finish...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P-cFXrRPM0fOc4CVwfbjlzlc4VBw37FZ5g_OY9tIE14qqTI2_ajrzNwWaQ…\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - waiting for\" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…\" navigation to finish...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P88UQ75CI-owpCWOB_Pz5G2URf8ettxDsrcbeMC5E_tLTH6Mz9Fesda5aP…\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - waiting for\" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…\" navigation to finish...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P9xVue0Fy26j3ilvJ_X9QgorPTVk2k2OUMMBq0i1BMi-yjf3tGWtKjwU8V…\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - waiting for\" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…\" navigation to finish...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P9zH864M85G3xj2XC2VI--Wkt4zXZqXvzTJROLRib3FIjNjPInITG9memX…\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - waiting for\" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…\" navigation to finish...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P_vhH2fCBb2qS11Vx9o7o9nbk1QKm5QWfl5x5y6JB7pSHU6EKDY49vJ5Nq…\"\\x1B[22m\\n'\u001b[39m,\n      stack: \u001b[32m'Error: locator.waitFor: Target page, context or browser has been closed\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for getByRole('button', { name: 'Settings' }) to be visible\\x1B[22m\\n\"\u001b[39m +\n        \u001b[32m'\\x1B[2m    - waiting for\" https://gfs2.glb.kworld.kpmg.com/adfs/ls/?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%26es…\" navigation to finish...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - navigated to \"https://gfs2.glb.kworld.kpmg.com/adfs/ls/?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%26es…\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - waiting for\" https://gfs2.glb.kworld.kpmg.com/adfs/ls/wia?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%2…\" navigation to finish...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - navigated to \"https://gfs2.glb.kworld.kpmg.com/adfs/ls/wia?client-request-id=01975d83-699d-78a7-9b09-430ad35b81a1&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%2…\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - waiting for navigation to finish...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P8QIuVg8NsOEQ7Nf3s512kuvL5G7OxMKOPmxzhEMydDLJ5qH-4HAuc01XR…\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - waiting for\" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…\" navigation to finish...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P-cFXrRPM0fOc4CVwfbjlzlc4VBw37FZ5g_OY9tIE14qqTI2_ajrzNwWaQ…\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - waiting for\" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…\" navigation to finish...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P88UQ75CI-owpCWOB_Pz5G2URf8ettxDsrcbeMC5E_tLTH6Mz9Fesda5aP…\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - waiting for\" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…\" navigation to finish...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P9xVue0Fy26j3ilvJ_X9QgorPTVk2k2OUMMBq0i1BMi-yjf3tGWtKjwU8V…\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - waiting for\" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…\" navigation to finish...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P9zH864M85G3xj2XC2VI--Wkt4zXZqXvzTJROLRib3FIjNjPInITG9memX…\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - waiting for\" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…\" navigation to finish...\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m    - navigated to \"https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P_vhH2fCBb2qS11Vx9o7o9nbk1QKm5QWfl5x5y6JB7pSHU6EKDY49vJ5Nq…\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightWorld.waitForElement (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-world.ts:436:19)\\n'\u001b[39m +\n        \u001b[32m'    at Object.handler (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\step-definition-registry.ts:189:21)\\n'\u001b[39m +\n        \u001b[32m'    at StepDefinitionRegistry.executeStep (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\step-definition-registry.ts:361:23)\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightCucumberRunner.executeStep (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts:196:31)\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightCucumberRunner.executeScenario (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts:231:20)\\n'\u001b[39m +\n        \u001b[32m'    at C:\\\\workspace\\\\Playwright_Automation_Framework\\\\tests\\\\cucumber-wrapper.spec.ts:45:9'\u001b[39m,\n      cause: \u001b[90mundefined\u001b[39m\n    }\n  }\n}\n"}], "retry": 0, "startTime": "2025-06-11T05:42:51.200Z", "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "attachments": [{"name": "Step 1 - Before: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--fb638ebdbe80a7756a8e63079085f5659f232f8d.png"}, {"name": "Step 1 - After: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\" (3955ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-3955ms--c301db7883c8ac54fad2dda0014def9f64fa9ef6.png"}, {"name": "Step 2 - Before: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-c181d5006af6c480a37f3073811d405ef4d7d4bb.png"}, {"name": "Step 2 - After: Then I wait for \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000 (7760ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-7760ms--dbcfbedf8ddf18ee8081c74d9174986056ee01a9.png"}, {"name": "Step 3 - Before: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagan<PERSON><PERSON>-kpmg-com--e62e82a00bce764349f7a3421da54aa70d7c5945.png"}, {"name": "Step 3 - After: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\" (165ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagan<PERSON><PERSON>-kpmg-com-165ms--ccc4322a28c3a78507b4e014c4195af45a0cc374.png"}, {"name": "Step 4 - Before: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-4---Before-And-I-click-on-getByRole-button-name-Next--5af3fa92aa35cd659ae9cfc51f250822ceddc396.png"}, {"name": "Step 4 - After: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\" (149ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-4---After-And-I-click-on-getByRole-button-name-Next-149ms--c007bb4fd799ce37174f0d986ee15b0e40044124.png"}, {"name": "Step 5 - Before: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\attachments\\Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-52592266963c7f3754edee9d81ffa66787e15c0e.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\video.webm"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-edge\\trace.zip"}], "errorLocation": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts", "column": 18, "line": 270}}], "status": "unexpected"}], "id": "f0a7916b485de33344dc-fc47a07ec800114403a0", "file": "cucumber-wrapper.spec.ts", "line": 34, "column": 11}]}]}], "errors": [], "stats": {"startTime": "2025-06-11T05:42:48.063Z", "duration": 38683.821, "expected": 0, "skipped": 0, "unexpected": 1, "flaky": 0}}