{"config": {"configFile": "C:\\workspace\\Playwright_Automation_Framework\\playwright.config.ts", "rootDir": "C:/workspace/Playwright_Automation_Framework/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report", "open": "always", "host": "localhost", "port": 9323}], ["line", null], ["json", {"outputFile": "test-results/playwright-results.json"}], ["junit", {"outputFile": "test-results/junit-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/workspace/Playwright_Automation_Framework/test-results/playwright-data", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "edge", "name": "edge", "testDir": "C:/workspace/Playwright_Automation_Framework/tests", "testIgnore": [], "testMatch": ["**/*.spec.ts", "**/*.spec.js"], "timeout": 300000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": null}, "suites": [{"title": "viewport-test.spec.ts", "file": "viewport-test.spec.ts", "column": 0, "line": 0, "specs": [{"title": "Verify viewport configuration", "ok": true, "tags": [], "tests": [{"timeout": 300000, "annotations": [], "expectedStatus": "passed", "projectId": "edge", "projectName": "edge", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 4982, "errors": [], "stdout": [{"text": "=== Viewport Test ===\n"}, {"text": "Actual viewport: 1920x1080\n"}, {"text": "Window inner size: 1920x1080\n"}, {"text": "Window outer size: 1936x1100\n"}, {"text": "Screen size: 1920x1080\n"}, {"text": "Expected viewport: 1920x1080\n"}, {"text": "✅ Viewport test passed!\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-11T05:49:01.921Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\viewport-test-Verify-viewport-configuration-edge\\video.webm"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\viewport-test-Verify-viewport-configuration-edge\\trace.zip"}]}], "status": "expected"}], "id": "165d6365e5bfce0add29-fe224cdcb25642e273cf", "file": "viewport-test.spec.ts", "line": 3, "column": 5}]}], "errors": [], "stats": {"startTime": "2025-06-11T05:48:59.274Z", "duration": 9499.165, "expected": 1, "skipped": 0, "unexpected": 0, "flaky": 0}}