{"config": {"configFile": "C:\\workspace\\Playwright_Automation_Framework\\playwright.config.ts", "rootDir": "C:/workspace/Playwright_Automation_Framework/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report", "open": "always", "host": "localhost", "port": 9323}], ["line", null], ["json", {"outputFile": "test-results/playwright-results.json"}], ["junit", {"outputFile": "test-results/junit-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/workspace/Playwright_Automation_Framework/test-results/playwright-data", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "edge", "name": "edge", "testDir": "C:/workspace/Playwright_Automation_Framework/tests", "testIgnore": [], "testMatch": ["**/*.spec.ts", "**/*.spec.js"], "timeout": 300000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": null}, "suites": [{"title": "cucumber-wrapper.spec.ts", "file": "cucumber-wrapper.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Self Service Featured Results", "file": "cucumber-wrapper.spec.ts", "line": 26, "column": 8, "specs": [{"title": "TC0101- Create Featured Result with Test Data", "ok": false, "tags": [], "tests": [{"timeout": 300000, "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr, @smoke, @create-featured-result"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "expectedStatus": "passed", "projectId": "edge", "projectName": "edge", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 57524, "error": {"message": "TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for get<PERSON><PERSON><PERSON><PERSON><PERSON>('K, Jagannath<PERSON>') to be visible\u001b[22m\n", "stack": "TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>') to be visible\u001b[22m\n\n    at PlaywrightWorld.waitForElement (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts:697:19)\n    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:189:21)\n    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:361:23)\n    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:225:31)\n    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:303:20)\n    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9", "location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts", "column": 19, "line": 697}, "snippet": "\u001b[90m   at \u001b[39m..\\src\\core\\playwright-world.ts:697\n\n\u001b[0m \u001b[90m 695 |\u001b[39m     \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mlogger\u001b[33m.\u001b[39minfo(\u001b[32m`⏳ Waiting for element: ${selector}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 696 |\u001b[39m     \u001b[36mconst\u001b[39m element \u001b[33m=\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mparseLocator(selector)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 697 |\u001b[39m     \u001b[36mawait\u001b[39m element\u001b[33m.\u001b[39mwaitFor({ state\u001b[33m:\u001b[39m \u001b[32m'visible'\u001b[39m\u001b[33m,\u001b[39m timeout })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 698 |\u001b[39m\n \u001b[90m 699 |\u001b[39m     \u001b[90m// Action highlight after element becomes visible (red)\u001b[39m\n \u001b[90m 700 |\u001b[39m     \u001b[36mif\u001b[39m (\u001b[33mEnvConfig\u001b[39m\u001b[33m.\u001b[39m\u001b[33mHIGHLIGHT_ELEMENTS\u001b[39m) {\u001b[0m"}, "errors": [{"location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts", "column": 19, "line": 697}, "message": "TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>') to be visible\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\src\\core\\playwright-world.ts:697\n\n\u001b[0m \u001b[90m 695 |\u001b[39m     \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mlogger\u001b[33m.\u001b[39minfo(\u001b[32m`⏳ Waiting for element: ${selector}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 696 |\u001b[39m     \u001b[36mconst\u001b[39m element \u001b[33m=\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mparseLocator(selector)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 697 |\u001b[39m     \u001b[36mawait\u001b[39m element\u001b[33m.\u001b[39mwaitFor({ state\u001b[33m:\u001b[39m \u001b[32m'visible'\u001b[39m\u001b[33m,\u001b[39m timeout })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 698 |\u001b[39m\n \u001b[90m 699 |\u001b[39m     \u001b[90m// Action highlight after element becomes visible (red)\u001b[39m\n \u001b[90m 700 |\u001b[39m     \u001b[36mif\u001b[39m (\u001b[33mEnvConfig\u001b[39m\u001b[33m.\u001b[39m\u001b[33mHIGHLIGHT_ELEMENTS\u001b[39m) {\u001b[0m\n\u001b[2m    at PlaywrightWorld.waitForElement (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts:697:19)\u001b[22m\n\u001b[2m    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:189:21)\u001b[22m\n\u001b[2m    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:361:23)\u001b[22m\n\u001b[2m    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:225:31)\u001b[22m\n\u001b[2m    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:303:20)\u001b[22m\n\u001b[2m    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9\u001b[22m"}], "stdout": [{"text": "🎬 Starting scenario: TC0101- Create Featured Result with Test Data\n"}, {"text": "📁 Feature: Self Service Featured Results\n"}, {"text": "🏷️ Tags: @combined, @self-service, @fr, @smoke, @create-featured-result\n"}, {"text": "🏗️ Executing 7 background steps\n"}, {"text": "📋 Background Step 1/7: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\"\n"}, {"text": "✅ Background step completed in 3879ms\n"}, {"text": "📋 Background Step 2/7: And I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000\n"}, {"text": "✅ Background step completed in 6251ms\n"}, {"text": "📋 Background Step 3/7: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"\n"}, {"text": "✅ Background step completed in 88ms\n"}, {"text": "📋 Background Step 4/7: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"\n"}, {"text": "✅ Background step completed in 166ms\n"}, {"text": "📋 Background Step 5/7: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON>ting<PERSON>' })\" to be visible with timeout 60000\n"}, {"text": "✅ Background step completed in 12962ms\n"}, {"text": "📋 Background Step 6/7: And I wait for element \"getByRole('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000\n"}, {"text": "✅ Background step completed in 2837ms\n"}, {"text": "📋 Background Step 7/7: And I wait for page title to contain \"KPMG Find - Settings\"\n"}, {"text": "✅ Background step completed in 8ms\n"}, {"text": "🏗️ Background steps completed successfully\n"}, {"text": "📋 Step 1/46: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\"\n"}, {"text": "✅ Step completed in 257ms\n"}, {"text": "📋 Step 2/46: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 80ms\n"}, {"text": "📋 Step 3/46: And the \"locator('#displayName-label')\" element should have exact text \"Selected User\"\n"}, {"text": "✅ Step completed in 39ms\n"}, {"text": "📋 Step 4/46: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON>ult' })\" should be visible\n"}, {"text": "✅ Step completed in 60ms\n"}, {"text": "📋 Step 5/46: And element \"getByRole('button', { name: 'Can<PERSON>' })\" should be visible\n"}, {"text": "✅ Step completed in 85ms\n"}, {"text": "📋 Step 6/46: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON>R<PERSON><PERSON>' })\"\n"}, {"text": "✅ Step completed in 245ms\n"}, {"text": "📋 Step 7/46: And I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000\n"}, {"text": "✅ Step completed in 50ms\n"}, {"text": "📋 Step 8/46: And I click on \"locator('#menu-').getByText('FeaturedResult')\"\n"}, {"text": "✅ Step completed in 157ms\n"}, {"text": "📋 Step 9/46: And I click on \"getByR<PERSON>('textbox', { name: 'Selected User' })\"\n"}, {"text": "✅ Step completed in 167ms\n"}, {"text": "📋 Step 10/46: Then I wait for element \"getByRole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 53ms\n"}, {"text": "📋 Step 11/46: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible\n"}, {"text": "✅ Step completed in 54ms\n"}, {"text": "📋 Step 12/46: And element \"getByRole('searchbox', { name: 'description' })\" should be visible\n"}, {"text": "✅ Step completed in 107ms\n"}, {"text": "📋 Step 13/46: When I fill \"getBy<PERSON><PERSON>('searchbox', { name: 'description' })\" with \"<username>\"\n"}, {"text": "✅ Step completed in 143ms\n"}, {"text": "📋 Step 14/46: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000\n"}, {"text": "[\u001b[31merror\u001b[39m]: ❌ Step failed: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000\r\n"}], "stderr": [{"text": "❌ Scena<PERSON> failed: TC0101- Create Featured Result with Test Data locator.waitFor: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>') to be visible\u001b[22m\n\n    at PlaywrightWorld.waitForElement \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-world.ts:697:19\u001b[90m)\u001b[39m\n    at Object.handler \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\step-definition-registry.ts:189:21\u001b[90m)\u001b[39m\n    at StepDefinitionRegistry.executeStep \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\step-definition-registry.ts:361:23\u001b[90m)\u001b[39m\n    at PlaywrightCucumberRunner.executeStep \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-cucumber-runner.ts:225:31\u001b[90m)\u001b[39m\n    at PlaywrightCucumberRunner.executeScenario \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-cucumber-runner.ts:303:20\u001b[90m)\u001b[39m\n    at \u001b[90mC:\\workspace\\Playwright_Automation_Framework\\\u001b[39mtests\\cucumber-wrapper.spec.ts:45:9 {\n  name: \u001b[32m'TimeoutError'\u001b[39m,\n  [\u001b[32mSymbol(step)\u001b[39m]: {\n    stepId: \u001b[32m'pw:api@128'\u001b[39m,\n    location: {\n      file: \u001b[32m'C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-world.ts'\u001b[39m,\n      line: \u001b[33m697\u001b[39m,\n      column: \u001b[33m19\u001b[39m,\n      function: \u001b[32m'PlaywrightWorld.waitForElement'\u001b[39m\n    },\n    category: \u001b[32m'pw:api'\u001b[39m,\n    title: \u001b[32m\"locator.getByLabel('K, Jagannatha').waitFor\"\u001b[39m,\n    apiName: \u001b[32m'locator.waitFor'\u001b[39m,\n    params: {\n      selector: \u001b[32m'internal:label=\"K, Jagannatha\"i'\u001b[39m,\n      strict: \u001b[33mtrue\u001b[39m,\n      omitReturnValue: \u001b[33mtrue\u001b[39m,\n      state: \u001b[32m'visible'\u001b[39m,\n      timeout: \u001b[33m10000\u001b[39m\n    },\n    boxedStack: \u001b[90mundefined\u001b[39m,\n    steps: [],\n    attachmentIndices: [],\n    info: TestStepInfoImpl {\n      annotations: [],\n      _testInfo: \u001b[36m[TestInfoImpl]\u001b[39m,\n      _stepId: \u001b[32m'pw:api@128'\u001b[39m\n    },\n    complete: \u001b[36m[Function: complete]\u001b[39m,\n    endWallTime: \u001b[33m1749627816951\u001b[39m,\n    error: {\n      message: \u001b[32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for getByLabel('K, Jagannatha') to be visible\\x1B[22m\\n\"\u001b[39m,\n      stack: \u001b[32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for getByLabel('K, Jagannatha') to be visible\\x1B[22m\\n\"\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightWorld.waitForElement (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-world.ts:697:19)\\n'\u001b[39m +\n        \u001b[32m'    at Object.handler (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\step-definition-registry.ts:189:21)\\n'\u001b[39m +\n        \u001b[32m'    at StepDefinitionRegistry.executeStep (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\step-definition-registry.ts:361:23)\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightCucumberRunner.executeStep (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts:225:31)\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightCucumberRunner.executeScenario (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts:303:20)\\n'\u001b[39m +\n        \u001b[32m'    at C:\\\\workspace\\\\Playwright_Automation_Framework\\\\tests\\\\cucumber-wrapper.spec.ts:45:9'\u001b[39m,\n      cause: \u001b[90mundefined\u001b[39m\n    }\n  }\n}\n"}], "retry": 0, "startTime": "2025-06-11T07:42:42.057Z", "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr, @smoke, @create-featured-result"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "attachments": [{"name": "Background Step 1 - Before: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Background-Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--1ed918cd9d00c8331e9570e5c951a15860c8b78b.png"}, {"name": "Background Step 1 - After: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\" (3879ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Background-Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-3879ms--2d47c1632dd701d7641365727d5b000bb40c8933.png"}, {"name": "Background Step 2 - Before: And I wait for \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Background-Step-2---Before-And-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-e645b6ea45ab0e08ccb6307d976cc30115fb60c4.png"}, {"name": "Background Step 2 - After: And I wait for \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000 (6251ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Background-Step-2---After-And-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-6251ms--685f233c9bdd62d5ec28c517376fb49ab4e73f02.png"}, {"name": "Background Step 3 - Before: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Background-Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-j<PERSON><PERSON><PERSON>-kpmg-com--e508fdc3e5316574a1fe8b5419e442b454b07df5.png"}, {"name": "Background Step 3 - After: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\" (88ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Background-Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-j<PERSON><PERSON><PERSON>-kpmg-com-88ms--d331ee7be14eb349aac3bafb4d6905ef66ccb352.png"}, {"name": "Background Step 4 - Before: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Background-Step-4---Before-And-I-click-on-getByRole-button-name-Next--4524f1a2802fc3df67503ada045d1834a9d74352.png"}, {"name": "Background Step 4 - After: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\" (166ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Background-Step-4---After-And-I-click-on-getByRole-button-name-Next-166ms--cda1e7babd7bbfef4b1dec9261ebe9015f56b42b.png"}, {"name": "Background Step 5 - Before: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Background-Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-47b2be8c572c1f9718a04b0972e27d58ed07b4f9.png"}, {"name": "Background Step 5 - After: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000 (12962ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Background-Step-5---After-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-12962ms--645cba9334fa0369926e83cf7e866eb904f64d7a.png"}, {"name": "Background Step 6 - Before: And I wait for element \"getByRole('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Background-Step-6---Before-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-ad00efba73dcfb2cd51bbfb06cffd1959d8c339f.png"}, {"name": "Background Step 6 - After: And I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000 (2837ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Background-Step-6---After-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-2837ms--5f00919785b8035d846136de2ff4db89b91f6af7.png"}, {"name": "Background Step 7 - Before: And I wait for page title to contain \"KPMG Find - Settings\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Background-Step-7---Before-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings--0239d938f4c55037a15df7a26cc1e73a3ec4b4c3.png"}, {"name": "Background Step 7 - After: And I wait for page title to contain \"KPMG Find - Settings\" (8ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Background-Step-7---After-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings-8ms--8c92114b20cdcbb0e96f36bf0395a176ee8768bd.png"}, {"name": "Step 1 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-1---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--f2828ab2648124860832dd644cd1767783c0dec8.png"}, {"name": "Step 1 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\" (257ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-1---After-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of-257ms--06477d74cbaefe5df5a87efdabf42c5fd1c0a10b.png"}, {"name": "Step 2 - Before: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-2---Before-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-b24003d3b0e9cf8cc2a5a972d4d6330dc6080ab8.png"}, {"name": "Step 2 - After: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000 (80ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-2---After-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-80ms--07a41814d029951c1f7881c704d6771ca186ca84.png"}, {"name": "Step 3 - Before: And the \"locator('#displayName-label')\" element should have exact text \"Selected User\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-3---Before-And-the-locator-displayName-label-element-should-have-exact-text-Selected-User--bf340e2c3b83032fcaaf4cadf9e04aab0cb79cd3.png"}, {"name": "Step 3 - After: And the \"locator('#displayName-label')\" element should have exact text \"Selected User\" (39ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-3---After-And-the-locator-displayName-label-element-should-have-exact-text-Selected-User-39ms--bf86f760271704dbce04fee33364385aa7c268a5.png"}, {"name": "Step 4 - Before: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON><PERSON>' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-4---Before-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-a35c12eb1651736b8d9d060685cec96d3c92c1a0.png"}, {"name": "Step 4 - After: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON><PERSON>' })\" should be visible (60ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-4---After-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-60ms--bd8c227cc60bd97bf9a74c08b270dba264b39ede.png"}, {"name": "Step 5 - Before: And element \"getByRole('button', { name: 'Can<PERSON>' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-5---Before-And-element-getByRole-button-name-Cancel-should-be-visible-069f0384df40cfa2238de3769ebe2e8817c87121.png"}, {"name": "Step 5 - After: And element \"getByRole('button', { name: '<PERSON><PERSON>' })\" should be visible (85ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-5---After-And-element-getByRole-button-name-Cancel-should-be-visible-85ms--a694e77e54c65df095ce2ac9515dd7c6bc5f5d62.png"}, {"name": "Step 6 - Before: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-6---Before-When-I-click-on-getByRole-combobox-name-FeaturedResult--59c57a1cbf3372a032792b4e366f7392b6dd9811.png"}, {"name": "Step 6 - After: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\" (245ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-6---After-When-I-click-on-getByRole-combobox-name-FeaturedResult-245ms--ce435f584089f50105395564653eed96b865e0af.png"}, {"name": "Step 7 - Before: And I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-7---Before-And-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-9acdcc0a3df57547bff1f45788a6e47ae7b94eb6.png"}, {"name": "Step 7 - After: And I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000 (50ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-7---After-And-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-50ms--078df80df95e60b956aaf2d4a1d6cfae271b3abf.png"}, {"name": "Step 8 - Before: And I click on \"locator('#menu-').getByText('FeaturedResult')\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-8---Before-And-I-click-on-locator-menu--getByText-FeaturedResult--8984c6eb37da62b6fe24f8020e8a86929d852076.png"}, {"name": "Step 8 - After: And I click on \"locator('#menu-').getByText('FeaturedResult')\" (157ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-8---After-And-I-click-on-locator-menu--getByText-FeaturedResult-157ms--14439f5f6e28200efadce6c12cd11b144b577aab.png"}, {"name": "Step 9 - Before: And I click on \"getByR<PERSON>('textbox', { name: 'Selected User' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-9---Before-And-I-click-on-getByRole-textbox-name-Selected-User--758761d760b7f8ed8ad12402c0ff4319afb9e75a.png"}, {"name": "Step 9 - After: And I click on \"getByRole('textbox', { name: 'Selected User' })\" (167ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-9---After-And-I-click-on-getByRole-textbox-name-Selected-User-167ms--5952c13f9f36c7f25a81a7de81f8e4593227d6b3.png"}, {"name": "Step 10 - Before: Then I wait for element \"getByRole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-10---Before-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-ee40225c8e6bcf18620ec6a629b7fda755c098cd.png"}, {"name": "Step 10 - After: Then I wait for element \"getByRole('heading', { name: 'Select user for the request' })\" to be visible with timeout 10000 (53ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-10---After-Then-I-wait-for-element-getByRole-heading-name-Select-user-for-the-request-to-be-visible-with-timeout-10000-53ms--c9c577a9a3af63534dc537a2dcc83d5450234823.png"}, {"name": "Step 11 - Before: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-11---Before-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-c8bfbd64a2fe0559a3affcc099f5c73717cb5b4f.png"}, {"name": "Step 11 - After: And element \"getByRole('dialog', { name: 'Select user for the request' }).locator('svg')\" should be visible (54ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-11---After-And-element-getByRole-dialog-name-Select-user-for-the-request-locator-svg-should-be-visible-54ms--7074c9afe29722b27e7d8581436675c1ec5978cd.png"}, {"name": "Step 12 - Before: And element \"getByRole('searchbox', { name: 'description' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-12---Before-And-element-getByRole-searchbox-name-description-should-be-visible-325271eceb1032d714dc2addc3c8178f0127568a.png"}, {"name": "Step 12 - After: And element \"getByRole('searchbox', { name: 'description' })\" should be visible (107ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-12---After-And-element-getByRole-searchbox-name-description-should-be-visible-107ms--9920e1bf6c789dacf8181eafd547de11357b6a80.png"}, {"name": "Step 13 - Before: When I fill \"getBy<PERSON><PERSON>('searchbox', { name: 'description' })\" with \"<username>\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-13---Before-When-I-fill-getByRole-searchbox-name-description-with-username--ac0b9dc364042201118550a9f2f0700d5e61e233.png"}, {"name": "Step 13 - After: When I fill \"getBy<PERSON><PERSON>('searchbox', { name: 'description' })\" with \"<username>\" (143ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-13---After-When-I-fill-getByRole-searchbox-name-description-with-username-143ms--6965e568a6b19ef762c2027b3832bef7ef5e8a19.png"}, {"name": "Step 14 - Before: Then I wait for element \"get<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON>, <PERSON><PERSON><PERSON><PERSON>')\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Step-14---Before-Then-I-wait-for-element-getByLabel-K-Jagannatha-to-be-visible-with-timeout-10000-c07f0bc2d1efb1416305d2e72aeaf49df41d15f7.png"}, {"name": "Failure Screenshot", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\attachments\\Failure-Screenshot-542d11fe5ab6020a20eb743e1391b31b77491061.png"}, {"name": "screenshot", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-843c7-tured-Result-with-Test-Data-edge\\trace.zip"}], "errorLocation": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts", "column": 19, "line": 697}}], "status": "unexpected"}], "id": "f0a7916b485de33344dc-b36f91be3d546e93349f", "file": "cucumber-wrapper.spec.ts", "line": 34, "column": 11}]}]}], "errors": [], "stats": {"startTime": "2025-06-11T07:42:40.152Z", "duration": 63648.564000000006, "expected": 0, "skipped": 0, "unexpected": 1, "flaky": 0}}