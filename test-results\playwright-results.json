{"config": {"configFile": "C:\\workspace\\Playwright_Automation_Framework\\playwright.config.ts", "rootDir": "C:/workspace/Playwright_Automation_Framework/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report", "open": "always", "host": "localhost", "port": 9323}], ["line", null], ["json", {"outputFile": "test-results/playwright-results.json"}], ["junit", {"outputFile": "test-results/junit-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/workspace/Playwright_Automation_Framework/test-results/playwright-data", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "C:/workspace/Playwright_Automation_Framework/tests", "testIgnore": [], "testMatch": ["**/*.spec.ts", "**/*.spec.js"], "timeout": 300000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": null}, "suites": [{"title": "cucumber-wrapper.spec.ts", "file": "cucumber-wrapper.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Self Service Featured Results", "file": "cucumber-wrapper.spec.ts", "line": 26, "column": 8, "specs": [{"title": "TC0101- <PERSON><PERSON> and Check the Default Data Source Order", "ok": false, "tags": [], "tests": [{"timeout": 300000, "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 46106, "error": {"message": "Error: locator.waitFor: Unexpected token \"locator(\" while parsing css selector \"locator('#menu-')\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - waiting for locator('#menu-') >> internal:text=\"FeaturedResult\"i to be visible\u001b[22m\n", "stack": "Error: locator.waitFor: Unexpected token \"locator(\" while parsing css selector \"locator('#menu-')\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - waiting for locator('#menu-') >> internal:text=\"FeaturedResult\"i to be visible\u001b[22m\n\n    at PlaywrightWorld.waitForElement (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts:335:19)\n    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:170:21)\n    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:334:23)\n    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:196:31)\n    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:231:20)\n    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9", "location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts", "column": 19, "line": 335}, "snippet": "\u001b[90m   at \u001b[39m..\\src\\core\\playwright-world.ts:335\n\n\u001b[0m \u001b[90m 333 |\u001b[39m     \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mlogger\u001b[33m.\u001b[39minfo(\u001b[32m`⏳ Waiting for element: ${selector}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 334 |\u001b[39m     \u001b[36mconst\u001b[39m element \u001b[33m=\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mparse<PERSON><PERSON>ator(selector)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 335 |\u001b[39m     \u001b[36mawait\u001b[39m element\u001b[33m.\u001b[39mwaitFor({ state\u001b[33m:\u001b[39m \u001b[32m'visible'\u001b[39m\u001b[33m,\u001b[39m timeout })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 336 |\u001b[39m   }\n \u001b[90m 337 |\u001b[39m\n \u001b[90m 338 |\u001b[39m   \u001b[90m/**\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts", "column": 19, "line": 335}, "message": "Error: locator.waitFor: Unexpected token \"locator(\" while parsing css selector \"locator('#menu-')\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - waiting for locator('#menu-') >> internal:text=\"FeaturedResult\"i to be visible\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\src\\core\\playwright-world.ts:335\n\n\u001b[0m \u001b[90m 333 |\u001b[39m     \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mlogger\u001b[33m.\u001b[39minfo(\u001b[32m`⏳ Waiting for element: ${selector}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 334 |\u001b[39m     \u001b[36mconst\u001b[39m element \u001b[33m=\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mparseLocator(selector)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 335 |\u001b[39m     \u001b[36mawait\u001b[39m element\u001b[33m.\u001b[39mwaitFor({ state\u001b[33m:\u001b[39m \u001b[32m'visible'\u001b[39m\u001b[33m,\u001b[39m timeout })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 336 |\u001b[39m   }\n \u001b[90m 337 |\u001b[39m\n \u001b[90m 338 |\u001b[39m   \u001b[90m/**\u001b[39m\u001b[0m\n\u001b[2m    at PlaywrightWorld.waitForElement (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts:335:19)\u001b[22m\n\u001b[2m    at Object.handler (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:170:21)\u001b[22m\n\u001b[2m    at StepDefinitionRegistry.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\step-definition-registry.ts:334:23)\u001b[22m\n\u001b[2m    at PlaywrightCucumberRunner.executeStep (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:196:31)\u001b[22m\n\u001b[2m    at PlaywrightCucumberRunner.executeScenario (C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-cucumber-runner.ts:231:20)\u001b[22m\n\u001b[2m    at C:\\workspace\\Playwright_Automation_Framework\\tests\\cucumber-wrapper.spec.ts:45:9\u001b[22m"}], "stdout": [{"text": "🎬 Starting scenario: TC0101- <PERSON><PERSON> and Check the Default Data Source Order\n"}, {"text": "📁 Feature: Self Service Featured Results\n"}, {"text": "🏷️ Tags: @combined, @self-service, @fr\n"}, {"text": "📋 Step 1/27: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\"\n"}, {"text": "✅ Step completed in 3975ms\n"}, {"text": "📋 Step 2/27: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 8539ms\n"}, {"text": "📋 Step 3/27: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"\n"}, {"text": "✅ Step completed in 85ms\n"}, {"text": "📋 Step 4/27: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"\n"}, {"text": "✅ Step completed in 120ms\n"}, {"text": "📋 Step 5/27: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000\n"}, {"text": "✅ Step completed in 11918ms\n"}, {"text": "📋 Step 6/27: And I wait for element \"getByRole('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000\n"}, {"text": "✅ Step completed in 3329ms\n"}, {"text": "📋 Step 7/27: And I wait for page title to contain \"KPMG Find - Settings\"\n"}, {"text": "✅ Step completed in 13ms\n"}, {"text": "📋 Step 8/27: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\"\n"}, {"text": "✅ Step completed in 260ms\n"}, {"text": "📋 Step 9/27: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000\n"}, {"text": "✅ Step completed in 43ms\n"}, {"text": "📋 Step 10/27: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\"\n"}, {"text": "[\u001b[33mwarn\u001b[39m]: ⚠️ No step definition found for: \"Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\"\"\r\n"}, {"text": "✅ Step completed in 543ms\n"}, {"text": "📋 Step 11/27: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON>ult' })\" should be visible\n"}, {"text": "[\u001b[33mwarn\u001b[39m]: ⚠️ No step definition found for: \"And element \"getByRole('combobox', { name: 'FeaturedR<PERSON>ult' })\" should be visible\"\r\n"}, {"text": "✅ Step completed in 542ms\n"}, {"text": "📋 Step 12/27: And element \"getByRole('button', { name: 'Can<PERSON>' })\" should be visible\n"}, {"text": "[\u001b[33mwarn\u001b[39m]: ⚠️ No step definition found for: \"And element \"getByRole('button', { name: 'Cancel' })\" should be visible\"\r\n"}, {"text": "✅ Step completed in 527ms\n"}, {"text": "📋 Step 13/27: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\"\n"}, {"text": "✅ Step completed in 123ms\n"}, {"text": "📋 Step 14/27: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000\n"}, {"text": "[\u001b[31merror\u001b[39m]: ❌ Step failed: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000\r\n"}], "stderr": [{"text": "❌ Scenario failed: TC0101- Login and Check the Default Data Source Order locator.waitFor: Unexpected token \"locator(\" while parsing css selector \"locator('#menu-')\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - waiting for locator('#menu-') >> internal:text=\"FeaturedResult\"i to be visible\u001b[22m\n\n    at PlaywrightWorld.waitForElement \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-world.ts:335:19\u001b[90m)\u001b[39m\n    at Object.handler \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\step-definition-registry.ts:170:21\u001b[90m)\u001b[39m\n    at StepDefinitionRegistry.executeStep \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\step-definition-registry.ts:334:23\u001b[90m)\u001b[39m\n    at PlaywrightCucumberRunner.executeStep \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-cucumber-runner.ts:196:31\u001b[90m)\u001b[39m\n    at PlaywrightCucumberRunner.executeScenario \u001b[90m(C:\\workspace\\Playwright_Automation_Framework\\\u001b[39msrc\\core\\playwright-cucumber-runner.ts:231:20\u001b[90m)\u001b[39m\n    at \u001b[90mC:\\workspace\\Playwright_Automation_Framework\\\u001b[39mtests\\cucumber-wrapper.spec.ts:45:9 {\n  name: \u001b[32m'Error'\u001b[39m,\n  [\u001b[32mSymbol(step)\u001b[39m]: {\n    stepId: \u001b[32m'pw:api@76'\u001b[39m,\n    location: {\n      file: \u001b[32m'C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-world.ts'\u001b[39m,\n      line: \u001b[33m335\u001b[39m,\n      column: \u001b[33m19\u001b[39m,\n      function: \u001b[32m'PlaywrightWorld.waitForElement'\u001b[39m\n    },\n    category: \u001b[32m'pw:api'\u001b[39m,\n    title: \u001b[32m`locator.waitFor(locator('#menu-') >> internal:text=\"FeaturedResult\"i)`\u001b[39m,\n    apiName: \u001b[32m'locator.waitFor'\u001b[39m,\n    params: {\n      selector: \u001b[32m`locator('#menu-') >> internal:text=\"FeaturedResult\"i`\u001b[39m,\n      strict: \u001b[33mtrue\u001b[39m,\n      omitReturnValue: \u001b[33mtrue\u001b[39m,\n      state: \u001b[32m'visible'\u001b[39m,\n      timeout: \u001b[33m30000\u001b[39m\n    },\n    boxedStack: \u001b[90mundefined\u001b[39m,\n    steps: [],\n    attachmentIndices: [],\n    info: TestStepInfoImpl {\n      annotations: [],\n      _testInfo: \u001b[36m[TestInfoImpl]\u001b[39m,\n      _stepId: \u001b[32m'pw:api@76'\u001b[39m\n    },\n    complete: \u001b[36m[Function: complete]\u001b[39m,\n    endWallTime: \u001b[33m1749615941088\u001b[39m,\n    error: {\n      message: \u001b[32m`Error: locator.waitFor: Unexpected token \"locator(\" while parsing css selector \"locator('#menu-')\". Did you mean to CSS.escape it?\\n`\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m`\\x1B[2m  - waiting for locator('#menu-') >> internal:text=\"FeaturedResult\"i to be visible\\x1B[22m\\n`\u001b[39m,\n      stack: \u001b[32m`Error: locator.waitFor: Unexpected token \"locator(\" while parsing css selector \"locator('#menu-')\". Did you mean to CSS.escape it?\\n`\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m`\\x1B[2m  - waiting for locator('#menu-') >> internal:text=\"FeaturedResult\"i to be visible\\x1B[22m\\n`\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightWorld.waitForElement (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-world.ts:335:19)\\n'\u001b[39m +\n        \u001b[32m'    at Object.handler (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\step-definition-registry.ts:170:21)\\n'\u001b[39m +\n        \u001b[32m'    at StepDefinitionRegistry.executeStep (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\step-definition-registry.ts:334:23)\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightCucumberRunner.executeStep (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts:196:31)\\n'\u001b[39m +\n        \u001b[32m'    at PlaywrightCucumberRunner.executeScenario (C:\\\\workspace\\\\Playwright_Automation_Framework\\\\src\\\\core\\\\playwright-cucumber-runner.ts:231:20)\\n'\u001b[39m +\n        \u001b[32m'    at C:\\\\workspace\\\\Playwright_Automation_Framework\\\\tests\\\\cucumber-wrapper.spec.ts:45:9'\u001b[39m,\n      cause: \u001b[90mundefined\u001b[39m\n    }\n  }\n}\n"}], "retry": 0, "startTime": "2025-06-11T04:24:58.346Z", "annotations": [{"type": "feature", "description": "Self Service Featured Results"}, {"type": "tags", "description": "@combined, @self-service, @fr"}, {"type": "file", "description": "src\\features\\self-service-fr.feature"}], "attachments": [{"name": "Step 1 - Before: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-1---Before-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com--9f5ae14be54d00e3a62c2b16aaf7cc1484e376f8.png"}, {"name": "Step 1 - After: Given I navigate to URL \"https://es-settings-staging.kpmg.com/\" (3975ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-1---After-Given-I-navigate-to-URL-https-es-settings-staging-kpmg-com-3975ms--a1a087f0e3c500721ff9ae627164f2a156e6def3.png"}, {"name": "Step 2 - Before: Then I wait for \"getByRole('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-2---Before-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-1d25b4a7f9215540528bcae43fdce44b2e129411.png"}, {"name": "Step 2 - After: Then I wait for \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" to be visible with timeout 10000 (8539ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-2---After-Then-I-wait-for-getByRole-textbox-name-Sign-in-with-your-KPMG-email-to-be-visible-with-timeout-10000-8539ms--73617745d97bb32ec3dc8ae51580cc93e4a45228.png"}, {"name": "Step 3 - Before: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-3---Before-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-jagan<PERSON><PERSON>-kpmg-com--316da82eef654cae4ad12d47f8fe1ffa375efaa6.png"}, {"name": "Step 3 - After: When I fill \"getBy<PERSON><PERSON>('textbox', { name: 'Sign in with your KPMG email' })\" with \"jagan<PERSON><PERSON>@kpmg.com\" (85ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-3---After-When-I-fill-getByRole-textbox-name-Sign-in-with-your-KPMG-email-with-j<PERSON><PERSON><PERSON>-kpmg-com-85ms--4a9f4d18cec09338fddd537da3174525295c5e1c.png"}, {"name": "Step 4 - Before: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-4---Before-And-I-click-on-getByRole-button-name-Next--996138976132e5a6f4d8dbc56204b69cf5c74dd9.png"}, {"name": "Step 4 - After: And I click on \"getBy<PERSON><PERSON>('button', { name: 'Next' })\" (120ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-4---After-And-I-click-on-getByRole-button-name-Next-120ms--3f82437afad13e30321af3d78ac8f5fa484586c5.png"}, {"name": "Step 5 - Before: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-5---Before-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-b7eb0d29174c8085f8fb05af67b2219b6e33c240.png"}, {"name": "Step 5 - After: Then I wait for element \"getBy<PERSON><PERSON>('button', { name: '<PERSON><PERSON><PERSON>' })\" to be visible with timeout 60000 (11918ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-5---After-Then-I-wait-for-element-getByRole-button-name-Settings-to-be-visible-with-timeout-60000-11918ms--3dd4df820ca306c48c56e1222ae6fb296e3fa9cb.png"}, {"name": "Step 6 - Before: And I wait for element \"getBy<PERSON>ole('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-6---Before-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-9a62bc5c83bd200eb2a7dff944a05f818f65627a.png"}, {"name": "Step 6 - After: And I wait for element \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\" to be visible with timeout 30000 (3329ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-6---After-And-I-wait-for-element-getByRole-button-name-Create-a-request-on-behalf-of-to-be-visible-with-timeout-30000-3329ms--17fc87cf7487f9ec6179fdd411bb5055ed61bea4.png"}, {"name": "Step 7 - Before: And I wait for page title to contain \"KPMG Find - Settings\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-7---Before-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings--e0bfffde38f7f27a22628c6df8759960773211f9.png"}, {"name": "Step 7 - After: And I wait for page title to contain \"KPMG Find - Settings\" (13ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-7---After-And-I-wait-for-page-title-to-contain-KPMG-Find---Settings-13ms--4b83bbf3501667bb17e22b4c76e9fb6dc6462bf1.png"}, {"name": "Step 8 - Before: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-8---Before-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of--589f5cbca4ff4d2eb36011127d110c26f4ff274a.png"}, {"name": "Step 8 - After: When I click on \"getBy<PERSON><PERSON>('button', { name: 'Create a request on behalf of' })\" (260ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-8---After-When-I-click-on-getByRole-button-name-Create-a-request-on-behalf-of-260ms--03f7be8836af0bea3f8d59039c40bcb3f07cf292.png"}, {"name": "Step 9 - Before: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-9---Before-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-04edb0a85796d1d977f53b8c4037f0ff4bbd750a.png"}, {"name": "Step 9 - After: Then I wait for element \"getByRole('heading', { name: 'Create a requests on behalf' })\" to be visible with timeout 10000 (43ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-9---After-Then-I-wait-for-element-getByRole-heading-name-Create-a-requests-on-behalf-to-be-visible-with-timeout-10000-43ms--c971768b63e560fabc0ff2286d7a76cb56abac01.png"}, {"name": "Step 10 - Before: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-10---Before-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User--165c17b6050f87ae4a620d027e9fd607edf2912b.png"}, {"name": "Step 10 - After: Then the \"locator('#displayName-label')\" element should have exact text \"Selected User\" (543ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-10---After-Then-the-locator-displayName-label-element-should-have-exact-text-Selected-User-543ms--feb725ac02ac5f75d4bed7dedb852bf3829125f9.png"}, {"name": "Step 11 - Before: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON><PERSON>' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-11---Before-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-f466302481ff8688838ef58344d6d1badc735b93.png"}, {"name": "Step 11 - After: And element \"getByRole('combobox', { name: 'FeaturedR<PERSON><PERSON>' })\" should be visible (542ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-11---After-And-element-getByRole-combobox-name-FeaturedResult-should-be-visible-542ms--f84da7efb281bbb123b9457fafd025189069862f.png"}, {"name": "Step 12 - Before: And element \"getByRole('button', { name: 'Can<PERSON>' })\" should be visible", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-12---Before-And-element-getByRole-button-name-Cancel-should-be-visible-3dd38e4bd0b48ac6cd8e82e706f3ceaa7cb695e4.png"}, {"name": "Step 12 - After: And element \"getByRole('button', { name: '<PERSON><PERSON>' })\" should be visible (527ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-12---After-And-element-getByRole-button-name-Cancel-should-be-visible-527ms--58ad1c6ff6ad0e844b8e1bb1067a683d738f6616.png"}, {"name": "Step 13 - Before: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\"", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-13---Before-When-I-click-on-getByRole-combobox-name-FeaturedR<PERSON>ult--b44ff2847df8960e31125198a9f555b9005c3f2c.png"}, {"name": "Step 13 - After: When I click on \"getBy<PERSON><PERSON>('combobox', { name: '<PERSON><PERSON><PERSON><PERSON>' })\" (123ms)", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-13---After-When-I-click-on-getByRole-combobox-name-FeaturedResult-123ms--f94b88f5efa8be97575230b453269d0c8c81733e.png"}, {"name": "Step 14 - Before: Then I wait for \"locator('#menu-').getByText('FeaturedResult')\" to be visible with timeout 30000", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Step-14---Before-Then-I-wait-for-locator-menu--getByText-FeaturedResult-to-be-visible-with-timeout-30000-b63538221cab462f800c2d0ec0aa353bd3fd08f7.png"}, {"name": "Failure Screenshot", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\attachments\\Failure-Screenshot-8bb9cb48c026463c729a9ab3c7ce68e5cf4ff1b8.png"}, {"name": "screenshot", "contentType": "image/png", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\workspace\\Playwright_Automation_Framework\\test-results\\playwright-data\\cucumber-wrapper-Self-Serv-df70a-e-Default-Data-Source-Order-chromium\\trace.zip"}], "errorLocation": {"file": "C:\\workspace\\Playwright_Automation_Framework\\src\\core\\playwright-world.ts", "column": 19, "line": 335}}], "status": "unexpected"}], "id": "f0a7916b485de33344dc-83e8223fa8d0c7f1f561", "file": "cucumber-wrapper.spec.ts", "line": 34, "column": 11}]}]}], "errors": [], "stats": {"startTime": "2025-06-11T04:24:55.967Z", "duration": 52405.732, "expected": 0, "skipped": 0, "unexpected": 1, "flaky": 0}}