import { Given, When, Then } from '@cucumber/cucumber';
import { CustomWorld } from './world';
import { expect } from '@playwright/test';
import { EnvConfig } from '../config/env.config';
import { Logger } from '../utils/logger';
import { faker } from '@faker-js/faker';
import path from 'path';
import fs from 'fs';

// Create a logger
const logger = new Logger('UtilitySteps');

/**
 * Utility Steps
 * These steps provide utility functions for test automation
 */

// ========== SCREENSHOT STEPS ==========

// Note: 'I take a screenshot named' step is already defined in universal.steps.ts

/**
 * Take a screenshot of a specific element
 */
When('I take a screenshot of {string} named {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string, name: string) {
  logger.info(`Taking screenshot of element ${selector} named: ${name}`);
  
  try {
    // Get the element
    const locator = this.getLocator(selector);
    
    // Wait for the element to be visible
    await locator.waitFor({ state: 'visible', timeout: 30000 });
    
    // Create the screenshots directory if it doesn't exist
    const screenshotDir = EnvConfig.SCREENSHOT_DIR || './test-results/screenshots';
    if (!fs.existsSync(screenshotDir)) {
      fs.mkdirSync(screenshotDir, { recursive: true });
    }
    
    // Take the screenshot
    const screenshotPath = path.join(screenshotDir, `${name}.png`);
    await locator.screenshot({ path: screenshotPath });
    
    logger.info(`Screenshot saved to: ${screenshotPath}`);
  } catch (error) {
    logger.warn(`Failed to take screenshot of element ${selector}: ${error}`);
    // Take a full page screenshot instead
    const screenshotDir = EnvConfig.SCREENSHOT_DIR || './test-results/screenshots';
    const screenshotPath = path.join(screenshotDir, `${name}-fallback.png`);
    await this.getPage().screenshot({ path: screenshotPath });
    logger.info(`Fallback screenshot saved to: ${screenshotPath}`);
  }
});

/**
 * Take a highlighted screenshot of a specific element
 */
When('I take a highlighted screenshot of {string} named {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string, name: string) {
  logger.info(`Taking highlighted screenshot of element ${selector} named: ${name}`);

  try {
    const page = this.getPage();
    const locator = this.getLocator(selector);

    // Wait for the element to be visible
    await locator.waitFor({ state: 'visible', timeout: 30000 });

    // Create the screenshots directory if it doesn't exist
    const screenshotDir = EnvConfig.SCREENSHOT_DIR || './test-results/screenshots';
    if (!fs.existsSync(screenshotDir)) {
      fs.mkdirSync(screenshotDir, { recursive: true });
    }

    // Highlight the element
    await locator.evaluate((element: HTMLElement) => {
      if (element) {
        element.style.outline = '3px solid #ff0000';
        element.style.outlineOffset = '2px';
        element.style.boxShadow = '0 0 10px #ff0000';
        element.style.backgroundColor = 'rgba(255, 0, 0, 0.1)';
      }
    });

    // Small delay to ensure highlight is visible
    await page.waitForTimeout(100);

    // Take the screenshot
    const screenshotPath = path.join(screenshotDir, `${name}-highlighted.png`);
    await page.screenshot({ path: screenshotPath, fullPage: true });

    // Remove highlight
    await locator.evaluate((element: HTMLElement) => {
      if (element) {
        element.style.outline = '';
        element.style.outlineOffset = '';
        element.style.boxShadow = '';
        element.style.backgroundColor = '';
      }
    });

    logger.info(`Highlighted screenshot saved to: ${screenshotPath}`);
  } catch (error) {
    logger.warn(`Failed to take highlighted screenshot of element ${selector}: ${error}`);
    // Take a regular screenshot instead
    const screenshotDir = EnvConfig.SCREENSHOT_DIR || './test-results/screenshots';
    const screenshotPath = path.join(screenshotDir, `${name}-fallback.png`);
    await this.getPage().screenshot({ path: screenshotPath });
    logger.info(`Fallback screenshot saved to: ${screenshotPath}`);
  }
});

// ========== STORAGE STEPS ==========

/**
 * Store a value from an element for later use
 */
When('I store the text from {string} as {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string, variableName: string) {
  logger.info(`Storing text from element ${selector} as variable: ${variableName}`);
  
  try {
    // Get the element
    const locator = this.getLocator(selector);
    
    // Wait for the element to be visible with a longer timeout
    await locator.waitFor({ state: 'visible', timeout: 30000 });
    
    // Get the text content
    const text = await locator.innerText();
    
    // Store the value in the world object
    this.setVariable(variableName, text);
    
    logger.info(`Stored value: ${text} as ${variableName}`);
  } catch (error) {
    // For demo purposes, store a mock value if the element is not found
    logger.warn(`Element ${selector} not found or not visible. Using mock value for demo.`);
    this.setVariable(variableName, `Mock value for ${variableName}`);
  }
});

/**
 * Store an attribute value from an element for later use
 */
When('I store the attribute {string} from {string} as {string}', { timeout: 60000 },
  async function(this: CustomWorld, attribute: string, selector: string, variableName: string) {
    logger.info(`Storing attribute ${attribute} from element ${selector} as variable: ${variableName}`);
    
    try {
      // Get the element
      const locator = this.getLocator(selector);
      
      // Wait for the element to be visible with a longer timeout
      await locator.waitFor({ state: 'visible', timeout: 30000 });
      
      // Get the attribute value
      const value = await locator.getAttribute(attribute);
      
      // Store the value in the world object
      this.setVariable(variableName, value || '');
      
      logger.info(`Stored value: ${value} as ${variableName}`);
    } catch (error) {
      // For demo purposes, store a mock value if the element is not found
      logger.warn(`Element ${selector} not found or not visible. Using mock value for demo.`);
      this.setVariable(variableName, `Mock attribute value for ${variableName}`);
    }
  });

/**
 * Use a stored variable in a step
 */
When('I fill {string} with stored variable {string}', { timeout: 60000 },
  async function(this: CustomWorld, selector: string, variableName: string) {
    // Get the stored value
    const value = this.getVariable(variableName);
    
    if (value === undefined) {
      throw new Error(`Variable ${variableName} is not defined`);
    }
    
    logger.info(`Filling ${selector} with stored variable ${variableName}: ${value}`);
    
    try {
      // Get the element
      const locator = this.getLocator(selector);
      
      // Wait for the element to be visible with a longer timeout
      await locator.waitFor({ state: 'visible', timeout: 30000 });
      
      // Fill the element with the stored value
      await this.smartInteraction.fill(locator, value);
    } catch (error) {
      logger.warn(`Failed to fill ${selector} with stored variable ${variableName}: ${error}`);
    }
  });

// ========== RANDOM DATA STEPS ==========

/**
 * Generate a random email and store it
 */
When('I generate a random email as {string}', async function(this: CustomWorld, variableName: string) {
  const email = faker.internet.email();
  logger.info(`Generated random email: ${email}`);
  
  // Store the value in the world object
  this.setVariable(variableName, email);
});

/**
 * Generate a random username and store it
 */
When('I generate a random username as {string}', async function(this: CustomWorld, variableName: string) {
  // Use a more modern approach to avoid the deprecated method
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  const username = `${firstName.toLowerCase()}${lastName.toLowerCase()}${faker.number.int(999)}`;
  
  logger.info(`Generated random username: ${username}`);
  
  // Store the value in the world object
  this.setVariable(variableName, username);
});

/**
 * Generate a random password and store it
 */
When('I generate a random password as {string}', async function(this: CustomWorld, variableName: string) {
  const password = faker.internet.password({ length: 12, memorable: true });
  logger.info(`Generated random password: ${password}`);
  
  // Store the value in the world object
  this.setVariable(variableName, password);
});

/**
 * Generate a random name and store it
 */
When('I generate a random name as {string}', async function(this: CustomWorld, variableName: string) {
  const name = faker.person.fullName();
  logger.info(`Generated random name: ${name}`);
  
  // Store the value in the world object
  this.setVariable(variableName, name);
});

/**
 * Generate a random phone number and store it
 */
When('I generate a random phone number as {string}', async function(this: CustomWorld, variableName: string) {
  const phoneNumber = faker.phone.number();
  logger.info(`Generated random phone number: ${phoneNumber}`);
  
  // Store the value in the world object
  this.setVariable(variableName, phoneNumber);
});

/**
 * Generate a random address and store it
 */
When('I generate a random address as {string}', async function(this: CustomWorld, variableName: string) {
  const address = faker.location.streetAddress();
  logger.info(`Generated random address: ${address}`);
  
  // Store the value in the world object
  this.setVariable(variableName, address);
});

/**
 * Generate a random number within a range and store it
 */
When('I generate a random number between {int} and {int} as {string}',
  async function(this: CustomWorld, min: number, max: number, variableName: string) {
    const number = faker.number.int({ min, max });
    logger.info(`Generated random number between ${min} and ${max}: ${number}`);
    
    // Store the value in the world object
    this.setVariable(variableName, number.toString());
});

// ========== CONDITIONAL STEPS ==========

/**
 * Execute steps only if an element is visible
 */
When('if {string} is visible, then:', { timeout: 60000 }, async function(this: CustomWorld, selector: string, dataTable: any) {
  logger.info(`Checking if element ${selector} is visible`);
  
  // Get the element
  const locator = this.getLocator(selector);
  
  // Check if the element is visible
  const isVisible = await locator.isVisible();
  
  if (isVisible) {
    logger.info(`Element ${selector} is visible, executing conditional steps`);
    
    // Execute each step in the data table
    const steps = dataTable.raw().map((row: string[]) => row[0].trim());
    for (const step of steps) {
      await this.executeStep(step);
    }
  } else {
    logger.info(`Element ${selector} is not visible, skipping conditional steps`);
  }
});

/**
 * Execute steps only if an element is not visible
 */
When('if {string} is not visible, then:', { timeout: 60000 }, async function(this: CustomWorld, selector: string, dataTable: any) {
  logger.info(`Checking if element ${selector} is not visible`);
  
  // Get the element
  const locator = this.getLocator(selector);
  
  // Check if the element is visible
  const isVisible = await locator.isVisible();
  
  if (!isVisible) {
    logger.info(`Element ${selector} is not visible, executing conditional steps`);
    
    // Execute each step in the data table
    const steps = dataTable.raw().map((row: string[]) => row[0].trim());
    for (const step of steps) {
      await this.executeStep(step);
    }
  } else {
    logger.info(`Element ${selector} is visible, skipping conditional steps`);
  }
});

/**
 * Execute steps only if text is present on the page
 */
When('if page contains text {string}, then:', { timeout: 60000 }, async function(this: CustomWorld, text: string, dataTable: any) {
  logger.info(`Checking if page contains text: ${text}`);
  
  // Check if the page contains the text
  const textLocator = this.getPage().getByText(text);
  const isTextPresent = await textLocator.isVisible();
  
  if (isTextPresent) {
    logger.info(`Page contains text: ${text}, executing conditional steps`);
    
    // Execute each step in the data table
    const steps = dataTable.raw().map((row: string[]) => row[0].trim());
    for (const step of steps) {
      await this.executeStep(step);
    }
  } else {
    logger.info(`Page does not contain text: ${text}, skipping conditional steps`);
  }
});

// ========== BROWSER STEPS ==========

/**
 * Set browser viewport size
 */
When('I set viewport size to width {int} and height {int}', async function(this: CustomWorld, width: number, height: number) {
  logger.info(`Setting viewport size to ${width}x${height}`);
  await this.getPage().setViewportSize({ width, height });
});

// Note: 'I emulate device' step is already defined in universal.steps.ts

// Export the steps
export { };
