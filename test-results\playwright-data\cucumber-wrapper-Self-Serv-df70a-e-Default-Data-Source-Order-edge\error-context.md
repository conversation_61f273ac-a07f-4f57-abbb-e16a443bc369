# Test info

- Name: Self Service Featured Results >> TC0101- Login and Check the Default Data Source Order
- Location: C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:34:11

# Error details

```
Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 101 elements:
    1) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('.MuiChip-label').first()
    2) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">test</span> aka locator('span').filter({ hasText: /^test$/ }).first()
    3) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">terms</span> aka getByText('terms').nth(1)
    4) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka getByRole('row', { name: 'Edit Share 75 FeaturedResult' }).getByRole('button').nth(2)
    5) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('td:nth-child(14) > div > .MuiChip-root > .MuiChip-label').first()
    6) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">FeaturedResult</span> aka locator('tr:nth-child(3) > td:nth-child(4) > .MuiChip-root > .MuiChip-label')
    7) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">test</span> aka locator('span').filter({ hasText: /^test$/ }).nth(1)
    8) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">terms</span> aka getByText('terms').nth(2)
    9) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">Submitted</span> aka getByRole('row', { name: 'Edit Share 74 FeaturedResult' }).getByRole('button').nth(2)
    10) <span class="MuiChip-label MuiChip-labelMedium css-qbwvub">All</span> aka locator('tr:nth-child(3) > td:nth-child(14) > div > .MuiChip-root > .MuiChip-label')
    ...

Call log:
  - expect.toHaveText with timeout 30000ms
  - waiting for locator('.MuiChip-label')

    at Object.handler (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:154:31)
    at StepDefinitionRegistry.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:361:23)
    at PlaywrightCucumberRunner.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:196:31)
    at PlaywrightCucumberRunner.executeScenario (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:231:20)
    at C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:45:9
```

# Page snapshot

```yaml
- banner:
  - link:
    - /url: /
  - link "All Requests":
    - /url: "#/allrequests/"
    - paragraph: All Requests
  - link "Ad Settings":
    - /url: "#/adsettings/"
    - paragraph: Ad Settings
  - link "Popup Settings":
    - /url: "#/popupsettings/"
    - paragraph: Popup Settings
  - link "Translation Settings":
    - /url: "#/translationsettings/"
    - paragraph: Translation Settings
  - link "OI / Teams Translation Settings":
    - /url: "#/oitranslationsettings/"
    - paragraph: OI / Teams Translation Settings
  - link "ES-Settings Translation":
    - /url: "#/settingstranslationsettings/"
    - paragraph: ES-Settings Translation
  - link "Featured Results":
    - /url: "#/featuredresults/"
    - paragraph: Featured Results
  - link "Contact Us Tiles":
    - /url: "#/contactustiles/"
    - paragraph: Contact Us Tiles
  - link "Did You Mean":
    - /url: "#/didyoumean/"
    - paragraph: Did You Mean
  - link "Search Tips":
    - /url: "#/searchtips/"
    - paragraph: Search Tips
  - link "Subscriptions":
    - /url: "#/subscriptions/"
    - paragraph: Subscriptions
  - link "Synonyms":
    - /url: "#/synonyms/"
    - paragraph: Synonyms
  - button "Settings"
- heading "All Requests" [level=2]
- textbox "Search"
- button "Clear Search" [disabled]
- button "Export"
- button "Create a request on behalf of a user": Create a Request
- button "Send expiration alerts": Expiration Alerts
- table:
  - rowgroup:
    - row "Actions ID Type Title Description Image Link Search Terms Status Modified On Expiry Date Created By Countries":
      - columnheader:
        - checkbox
      - columnheader "Actions":
        - button "Actions" [disabled]
      - columnheader "ID":
        - button "ID":
          - columnheader "ID"
      - columnheader "Type":
        - button "Type":
          - columnheader "Type"
      - columnheader "Title":
        - button "Title":
          - columnheader "Title"
      - columnheader "Description":
        - button "Description":
          - columnheader "Description"
      - columnheader "Image":
        - button "Image"
      - columnheader "Link":
        - button "Link"
      - columnheader "Search Terms":
        - button "Search Terms"
      - columnheader "Status":
        - button "Status":
          - columnheader "Status"
      - columnheader "Modified On":
        - button "Modified On":
          - columnheader "Modified On"
      - columnheader "Expiry Date":
        - button "Expiry Date":
          - columnheader "Expiry Date"
      - columnheader "Created By":
        - button "Created By":
          - columnheader "Created By"
      - columnheader "Countries":
        - button "Countries":
          - columnheader "Countries"
  - rowgroup:
    - row "All":
      - cell
      - cell
      - cell
      - cell "All":
        - combobox "All"
      - cell
      - cell
      - cell
      - cell:
        - textbox
      - cell:
        - textbox
      - cell "All":
        - combobox "All"
      - cell
      - cell
      - cell:
        - textbox
      - cell:
        - textbox
    - row "Edit Share 75 FeaturedResult Test Title Test Description https://www.google.com test terms Submitted 06/11/2025 06/11/2026 K, Jagannatha All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "75"
      - cell "FeaturedResult"
      - cell "Test Title"
      - cell "Test Description"
      - cell
      - cell "https://www.google.com"
      - cell "test terms"
      - cell "Submitted":
        - button "Submitted"
      - cell "06/11/2025"
      - cell "06/11/2026"
      - cell "K, Jagannatha"
      - cell "All"
    - row "Edit Share 74 FeaturedResult Test Title Test Description https://www.google.com test terms Submitted 06/11/2025 06/11/2026 K, Jagannatha All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "74"
      - cell "FeaturedResult"
      - cell "Test Title"
      - cell "Test Description"
      - cell
      - cell "https://www.google.com"
      - cell "test terms"
      - cell "Submitted":
        - button "Submitted"
      - cell "06/11/2025"
      - cell "06/11/2026"
      - cell "K, Jagannatha"
      - cell "All"
    - row "Edit Share 5 AdWord gggggggggggggg ad url gggggggggggggggg Submitted 06/11/2025 06/05/2026 Sabagh, Omar All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "5"
      - cell "AdWord"
      - cell "gggggggggggggg"
      - cell
      - cell "ad url":
        - img "ad url"
      - cell
      - cell "gggggggggggggggg"
      - cell "Submitted":
        - button "Submitted"
      - cell "06/11/2025"
      - cell "06/05/2026"
      - cell "Sabagh, Omar"
      - cell "All"
    - row "Edit Share 6 FeaturedResult test test https://asdf.com test Submitted 06/06/2025 06/06/2026 Segura, Javier (extern) All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "6"
      - cell "FeaturedResult"
      - cell "test"
      - cell "test"
      - cell
      - cell "https://asdf.com"
      - cell "test"
      - cell "Submitted":
        - button "Submitted"
      - cell "06/06/2025"
      - cell "06/06/2026"
      - cell "Segura, Javier (extern)"
      - cell "All"
    - row "Edit Share 6 AdWord Bypass Bypass bypass Submitted 06/05/2025 06/05/2026 Burger, Bob All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "6"
      - cell "AdWord"
      - cell "Bypass"
      - cell "Bypass"
      - cell
      - cell
      - cell "bypass"
      - cell "Submitted":
        - button "Submitted"
      - cell "06/05/2025"
      - cell "06/05/2026"
      - cell "Burger, Bob"
      - cell "All"
    - row "Edit Share 7 AdWord Bypass Bypass bypass Submitted 06/05/2025 06/05/2026 Burger, Bob All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "7"
      - cell "AdWord"
      - cell "Bypass"
      - cell "Bypass"
      - cell
      - cell
      - cell "bypass"
      - cell "Submitted":
        - button "Submitted"
      - cell "06/05/2025"
      - cell "06/05/2026"
      - cell "Burger, Bob"
      - cell "All"
    - row "Edit Share 8 AdWord Bypass Bypass bypass Submitted 06/05/2025 06/05/2026 Burger, Bob All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "8"
      - cell "AdWord"
      - cell "Bypass"
      - cell "Bypass"
      - cell
      - cell
      - cell "bypass"
      - cell "Submitted":
        - button "Submitted"
      - cell "06/05/2025"
      - cell "06/05/2026"
      - cell "Burger, Bob"
      - cell "All"
    - row "Edit Share 2 AdWord awrgreg awegaweg awegwegaweg Submitted 06/05/2025 06/05/2026 Longo, Alexander All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "2"
      - cell "AdWord"
      - cell "awrgreg"
      - cell "awegaweg"
      - cell
      - cell
      - cell "awegwegaweg"
      - cell "Submitted":
        - button "Submitted"
      - cell "06/05/2025"
      - cell "06/05/2026"
      - cell "Longo, Alexander"
      - cell "All"
    - row "Edit Share 3 AdWord awrgreg awegaweg awegwegaweg Submitted 06/05/2025 06/05/2026 Longo, Alexander All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "3"
      - cell "AdWord"
      - cell "awrgreg"
      - cell "awegaweg"
      - cell
      - cell
      - cell "awegwegaweg"
      - cell "Submitted":
        - button "Submitted"
      - cell "06/05/2025"
      - cell "06/05/2026"
      - cell "Longo, Alexander"
      - cell "All"
    - row "Edit Share 4 AdWord awrgreg awegaweg awegwegaweg Submitted 06/05/2025 06/05/2026 Longo, Alexander All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "4"
      - cell "AdWord"
      - cell "awrgreg"
      - cell "awegaweg"
      - cell
      - cell
      - cell "awegwegaweg"
      - cell "Submitted":
        - button "Submitted"
      - cell "06/05/2025"
      - cell "06/05/2026"
      - cell "Longo, Alexander"
      - cell "All"
    - row "Edit Share 1 AdWord testuuu test ad url testing Submitted 06/05/2025 06/05/2026 Longo, Alexander All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "1"
      - cell "AdWord"
      - cell "testuuu"
      - cell "test"
      - cell "ad url":
        - img "ad url"
      - cell
      - cell "testing"
      - cell "Submitted":
        - button "Submitted"
      - cell "06/05/2025"
      - cell "06/05/2026"
      - cell "Longo, Alexander"
      - cell "All"
    - row "Edit Share 4 AdWord testtttt tttt Submitted 06/05/2025 06/05/2026 Sabagh, Omar All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "4"
      - cell "AdWord"
      - cell "testtttt"
      - cell
      - cell
      - cell
      - cell "tttt"
      - cell "Submitted":
        - button "Submitted"
      - cell "06/05/2025"
      - cell "06/05/2026"
      - cell "Sabagh, Omar"
      - cell "All"
    - 'row "Edit Share 3 AdWord tttttttt XSS XSS XSS XSS --!> x \"> CLICKME \"> ipt>alert(''XSS'');ipt> @im\\port''\\ja\\vasc\\ript:alert(\\\"XSS\\\")''; alert(\\\"XSS\\\")''); ?> \"> window.alert(\"Bonjour !\"); onload=alert(''XSS'')> \"> ''\">>XSS BODY{background:url(\"alert(''XSS'')\")} alert(\"XSS\")''?> \" onfocus=alert(document.domain) \"> <\" li {list-style-image: url(\\\"alert(''XSS'')\\\");}XSS perl -e ''print \\\"alert(\\\"XSS\\\")\\\";'' > out perl -e ''print \\\"\\\";'' > out alert(1) \"> [color=red width=expression(alert(123))][color] Execute(MsgBox(chr(88)&chr(83)&chr(83)))< \"> ''\"> ''\"> ''\"\"> <<< ''> ''>\"> } test Submitted 06/05/2025 06/05/2026 Sabagh, Omar All"':
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "3"
      - cell "AdWord"
      - cell "tttttttt"
      - 'cell "XSS XSS XSS XSS --!> x \"> CLICKME \"> ipt>alert(''XSS'');ipt> @im\\port''\\ja\\vasc\\ript:alert(\\\"XSS\\\")''; alert(\\\"XSS\\\")''); ?> \"> window.alert(\"Bonjour !\"); onload=alert(''XSS'')> \"> ''\">>XSS BODY{background:url(\"alert(''XSS'')\")} alert(\"XSS\")''?> \" onfocus=alert(document.domain) \"> <\" li {list-style-image: url(\\\"alert(''XSS'')\\\");}XSS perl -e ''print \\\"alert(\\\"XSS\\\")\\\";'' > out perl -e ''print \\\"\\\";'' > out alert(1) \"> [color=red width=expression(alert(123))][color] Execute(MsgBox(chr(88)&chr(83)&chr(83)))< \"> ''\"> ''\"> ''\"\"> <<< ''> ''>\"> }"': XSS XSS XSS XSS --!&gt; x "&gt; CLICKME...
      - cell
      - cell
      - cell "test"
      - cell "Submitted":
        - button "Submitted"
      - cell "06/05/2025"
      - cell "06/05/2026"
      - cell "Sabagh, Omar"
      - cell "All"
    - row "Edit Share 30 FeaturedResult Test Featured Result Test Description https://find.kpmg.com testing Submitted 05/29/2025 05/29/2026 Sanil, Shashwath All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "30"
      - cell "FeaturedResult"
      - cell "Test Featured Result"
      - cell "Test Description"
      - cell
      - cell "https://find.kpmg.com"
      - cell "testing"
      - cell "Submitted":
        - button "Submitted"
      - cell "05/29/2025"
      - cell "05/29/2026"
      - cell "Sanil, Shashwath"
      - cell "All"
    - row "Edit Share 29 FeaturedResult Test Featured Result Test Description https://find.kpmg.com testing Submitted 05/29/2025 05/29/2026 Sanil, Shashwath All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "29"
      - cell "FeaturedResult"
      - cell "Test Featured Result"
      - cell "Test Description"
      - cell
      - cell "https://find.kpmg.com"
      - cell "testing"
      - cell "Submitted":
        - button "Submitted"
      - cell "05/29/2025"
      - cell "05/29/2026"
      - cell "Sanil, Shashwath"
      - cell "All"
    - row "Edit Share 28 FeaturedResult Test Featured Result Test Description https://find.kpmg.com testing Submitted 05/29/2025 05/29/2026 Sanil, Shashwath All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "28"
      - cell "FeaturedResult"
      - cell "Test Featured Result"
      - cell "Test Description"
      - cell
      - cell "https://find.kpmg.com"
      - cell "testing"
      - cell "Submitted":
        - button "Submitted"
      - cell "05/29/2025"
      - cell "05/29/2026"
      - cell "Sanil, Shashwath"
      - cell "All"
    - row "Edit Share 27 FeaturedResult Test Featured Result Test Description https://find.kpmg.com testing Submitted 05/29/2025 05/29/2026 Sanil, Shashwath All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "27"
      - cell "FeaturedResult"
      - cell "Test Featured Result"
      - cell "Test Description"
      - cell
      - cell "https://find.kpmg.com"
      - cell "testing"
      - cell "Submitted":
        - button "Submitted"
      - cell "05/29/2025"
      - cell "05/29/2026"
      - cell "Sanil, Shashwath"
      - cell "All"
    - row "Edit Share 7 AdWord test12345 test12345 Published 05/16/2025 05/16/2026 Gupta, Surabhi All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "7"
      - cell "AdWord"
      - cell "test12345"
      - cell "test12345"
      - cell
      - cell
      - cell
      - cell "Published":
        - button "Published"
      - cell "05/16/2025"
      - cell "05/16/2026"
      - cell "Gupta, Surabhi"
      - cell "All"
    - row "Edit Share 73 Acronym PSO Primary Sharepoint Owner Published 04/01/2025 K, Jagannatha All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "73"
      - cell "Acronym"
      - cell "PSO"
      - cell
      - cell
      - cell
      - cell "Primary Sharepoint Owner"
      - cell "Published":
        - button "Published"
      - cell "04/01/2025"
      - cell
      - cell "K, Jagannatha"
      - cell "All"
    - row "Edit Share 72 Synonym work job Published 04/01/2025 K, Jagannatha All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "72"
      - cell "Synonym"
      - cell "work"
      - cell
      - cell
      - cell
      - cell "job"
      - cell "Published":
        - button "Published"
      - cell "04/01/2025"
      - cell
      - cell "K, Jagannatha"
      - cell "All"
    - row "Edit Share 82 Synonym acronym acronym Published 04/01/2025 Srinivasa, Pavitra All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "82"
      - cell "Synonym"
      - cell "acronym"
      - cell
      - cell
      - cell
      - cell "acronym"
      - cell "Published":
        - button "Published"
      - cell "04/01/2025"
      - cell
      - cell "Srinivasa, Pavitra"
      - cell "All"
    - row "Edit Share 71 SpellingSuggestion sharepointer Published 04/01/2025 K, Jagannatha All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "71"
      - cell "SpellingSuggestion"
      - cell "sharepointer"
      - cell
      - cell
      - cell
      - cell
      - cell "Published":
        - button "Published"
      - cell "04/01/2025"
      - cell
      - cell "K, Jagannatha"
      - cell "All"
    - row "Edit Share 81 Synonym Advisory Consulting Published 04/01/2025 Srinivasa, Pavitra All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "81"
      - cell "Synonym"
      - cell "Advisory"
      - cell
      - cell
      - cell
      - cell "Consulting"
      - cell "Published":
        - button "Published"
      - cell "04/01/2025"
      - cell
      - cell "Srinivasa, Pavitra"
      - cell "All"
    - row "Edit Share 69 Synonym submit enter click Published 03/28/2025 K, Jagannatha All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "69"
      - cell "Synonym"
      - cell "submit"
      - cell
      - cell
      - cell
      - cell "enter click"
      - cell "Published":
        - button "Published"
      - cell "03/28/2025"
      - cell
      - cell "K, Jagannatha"
      - cell "All"
    - row "Edit Share 6 AdWord test test hello //es-settings-staging.kpmg.com/#/myrequests/ test Submitted 03/27/2025 01/23/2026 Gupta, Surabhi All":
      - cell:
        - checkbox
      - cell "Edit Share":
        - button "Edit"
        - button "Share"
      - cell "6"
      - cell "AdWord"
      - cell "test"
      - cell "test hello"
      - cell
      - cell //es-settings-staging.kpmg.com/#/myrequests/
      - cell "test"
      - cell "Submitted":
        - button "Submitted"
      - cell "03/27/2025"
      - cell "01/23/2026"
      - cell "Gupta, Surabhi"
      - cell "All"
- table:
  - rowgroup:
    - 'row "Rows per page: 25 rows First Page Previous Page 1-25 of 130 Next Page Last Page"':
      - 'cell "Rows per page: 25 rows First Page Previous Page 1-25 of 130 Next Page Last Page"':
        - paragraph: "Rows per page:"
        - 'combobox "Rows per page: 25 rows"': 25 rows
        - button "First Page" [disabled]
        - button "Previous Page" [disabled]
        - text: 1-25 of 130
        - button "Next Page"
        - button "Last Page"
```

# Test source

```ts
   54 |       },
   55 |       'Visit a URL'
   56 |     );
   57 |
   58 |     // Click steps
   59 |     this.registerStep(
   60 |       /^I click on "([^"]*)"$/,
   61 |       async (world: PlaywrightWorld, selector: string) => {
   62 |         await world.clickElement(selector);
   63 |       },
   64 |       'Click on an element'
   65 |     );
   66 |
   67 |     this.registerStep(
   68 |       /^I click the "([^"]*)" button$/,
   69 |       async (world: PlaywrightWorld, selector: string) => {
   70 |         await world.clickElement(selector);
   71 |       },
   72 |       'Click a button'
   73 |     );
   74 |
   75 |     // Input steps
   76 |     this.registerStep(
   77 |       /^I fill "([^"]*)" with "([^"]*)"$/,
   78 |       async (world: PlaywrightWorld, selector: string, value: string) => {
   79 |         await world.fillField(selector, value);
   80 |       },
   81 |       'Fill an input field'
   82 |     );
   83 |
   84 |     this.registerStep(
   85 |       /^I enter "([^"]*)" in "([^"]*)"$/,
   86 |       async (world: PlaywrightWorld, value: string, selector: string) => {
   87 |         await world.fillField(selector, value);
   88 |       },
   89 |       'Enter text in an input field'
   90 |     );
   91 |
   92 |     this.registerStep(
   93 |       /^I type "([^"]*)" in "([^"]*)"$/,
   94 |       async (world: PlaywrightWorld, value: string, selector: string) => {
   95 |         await world.typeInField(selector, value);
   96 |       },
   97 |       'Type text in an input field'
   98 |     );
   99 |
  100 |     // Assertion steps
  101 |     this.registerStep(
  102 |       /^I should see "([^"]*)"$/,
  103 |       async (world: PlaywrightWorld, text: string) => {
  104 |         await world.verifyTextPresent(text);
  105 |       },
  106 |       'Verify text is visible on page'
  107 |     );
  108 |
  109 |     this.registerStep(
  110 |       /^the page should contain "([^"]*)"$/,
  111 |       async (world: PlaywrightWorld, text: string) => {
  112 |         await world.verifyTextPresent(text);
  113 |       },
  114 |       'Verify page contains text'
  115 |     );
  116 |
  117 |     this.registerStep(
  118 |       /^"([^"]*)" should be visible$/,
  119 |       async (world: PlaywrightWorld, selector: string) => {
  120 |         await world.verifyElementVisible(selector);
  121 |       },
  122 |       'Verify element is visible'
  123 |     );
  124 |
  125 |     this.registerStep(
  126 |       /^the page title should contain "([^"]*)"$/,
  127 |       async (world: PlaywrightWorld, text: string) => {
  128 |         await world.verifyTitleContains(text);
  129 |       },
  130 |       'Verify page title contains text'
  131 |     );
  132 |
  133 |     this.registerStep(
  134 |       /^"([^"]*)" should contain text "([^"]*)"$/,
  135 |       async (world: PlaywrightWorld, selector: string, text: string) => {
  136 |         await world.verifyElementContainsText(selector, text);
  137 |       },
  138 |       'Verify element contains text'
  139 |     );
  140 |
  141 |     // Additional visibility assertion steps
  142 |     this.registerStep(
  143 |       /^element "([^"]*)" should be visible$/,
  144 |       async (world: PlaywrightWorld, selector: string) => {
  145 |         await world.verifyElementVisible(selector);
  146 |       },
  147 |       'Verify element is visible (alternative phrasing)'
  148 |     );
  149 |
  150 |     this.registerStep(
  151 |       /^the "([^"]*)" element should have exact text "([^"]*)"$/,
  152 |       async (world: PlaywrightWorld, selector: string, text: string) => {
  153 |         const element = world.getLocator(selector);
> 154 |         await expect(element).toHaveText(text);
      |                               ^ Error: expect.toHaveText: Error: strict mode violation: locator('.MuiChip-label') resolved to 101 elements:
  155 |       },
  156 |       'Verify element has exact text (with "the" and "element")'
  157 |     );
  158 |
  159 |     this.registerStep(
  160 |       /^"([^"]*)" should have exact text "([^"]*)"$/,
  161 |       async (world: PlaywrightWorld, selector: string, text: string) => {
  162 |         const element = world.getLocator(selector);
  163 |         await expect(element).toHaveText(text);
  164 |       },
  165 |       'Verify element has exact text'
  166 |     );
  167 |
  168 |     // Wait steps
  169 |     this.registerStep(
  170 |       /^I wait for (\d+) seconds?$/,
  171 |       async (world: PlaywrightWorld, seconds: string) => {
  172 |         const duration = parseInt(seconds) * 1000;
  173 |         await world.wait(duration);
  174 |       },
  175 |       'Wait for specified seconds'
  176 |     );
  177 |
  178 |     this.registerStep(
  179 |       /^I wait for "([^"]*)" to be visible$/,
  180 |       async (world: PlaywrightWorld, selector: string) => {
  181 |         await world.waitForElement(selector);
  182 |       },
  183 |       'Wait for element to be visible'
  184 |     );
  185 |
  186 |     this.registerStep(
  187 |       /^I wait for element "([^"]*)" to be visible with timeout (\d+)$/,
  188 |       async (world: PlaywrightWorld, selector: string, timeout: string) => {
  189 |         await world.waitForElement(selector, parseInt(timeout));
  190 |       },
  191 |       'Wait for element to be visible with timeout'
  192 |     );
  193 |
  194 |     this.registerStep(
  195 |       /^I wait for "([^"]*)" to be visible with timeout (\d+)$/,
  196 |       async (world: PlaywrightWorld, selector: string, timeout: string) => {
  197 |         await world.waitForElement(selector, parseInt(timeout));
  198 |       },
  199 |       'Wait for element to be visible with timeout (alternative syntax)'
  200 |     );
  201 |
  202 |     this.registerStep(
  203 |       /^I wait for page to load$/,
  204 |       async (world: PlaywrightWorld) => {
  205 |         await world.waitForPageLoad();
  206 |       },
  207 |       'Wait for page to load'
  208 |     );
  209 |
  210 |     this.registerStep(
  211 |       /^I wait for page title to contain "([^"]*)"$/,
  212 |       async (world: PlaywrightWorld, text: string) => {
  213 |         // Wait for title to contain the text with robust navigation handling
  214 |         let attempts = 0;
  215 |         const maxAttempts = 60; // 60 seconds for SharePoint navigation
  216 |
  217 |         this.logger.info(`Waiting for page title to contain: "${text}"`);
  218 |
  219 |         while (attempts < maxAttempts) {
  220 |           try {
  221 |             // Get title directly without waiting for network idle (SharePoint has ongoing requests)
  222 |             const title = await world.page.title();
  223 |             this.logger.info(`Current title: "${title}" (attempt ${attempts + 1})`);
  224 |
  225 |             if (title.includes(text)) {
  226 |               this.logger.info(`✅ Title contains expected text: "${text}"`);
  227 |               return;
  228 |             }
  229 |
  230 |             // Wait a bit before next attempt
  231 |             await world.wait(1000);
  232 |             attempts++;
  233 |
  234 |           } catch (error) {
  235 |             const errorMessage = error instanceof Error ? error.message : String(error);
  236 |             this.logger.warn(`Error getting title, retrying... (${errorMessage})`);
  237 |             await world.wait(2000);
  238 |             attempts++;
  239 |           }
  240 |         }
  241 |
  242 |         // Final attempt to get current title for error message
  243 |         try {
  244 |           const finalTitle = await world.page.title();
  245 |           throw new Error(`Timeout waiting for page title to contain "${text}". Current title: "${finalTitle}"`);
  246 |         } catch {
  247 |           throw new Error(`Timeout waiting for page title to contain "${text}". Could not retrieve current title.`);
  248 |         }
  249 |       },
  250 |       'Wait for page title to contain text'
  251 |     );
  252 |
  253 |     this.registerStep(
  254 |       /^I wait for (\d+) milliseconds$/,
```