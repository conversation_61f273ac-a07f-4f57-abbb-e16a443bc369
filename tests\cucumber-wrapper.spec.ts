import { test, expect } from '@playwright/test';
import { PlaywrightCucumberRunner } from '../src/core/playwright-cucumber-runner';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Native Playwright-Cucumber Integration
 *
 * This converts Cucumber scenarios directly into Playwright tests,
 * providing proper step-by-step logging and native Playwright reporting.
 */

// Get test configuration from environment variables
const TEST_TAGS = process.env.TEST_TAGS || '@spo';

// Initialize the runner
const runner = new PlaywrightCucumberRunner();

// Load features synchronously at module load time
const features = runner.loadFeaturesSync('src/features/**/*.feature');

console.log(`🥒 Loaded ${features.length} features for tags: ${TEST_TAGS}`);

// Generate tests for each feature and scenario
for (const feature of features) {
  test.describe(feature.name, () => {

    for (const scenario of feature.scenarios) {
      // Apply tag filtering
      if (TEST_TAGS && !runner.matchesTags(scenario.tags, TEST_TAGS)) {
        continue;
      }

      test(scenario.name, async ({ page, context }, testInfo) => {
        // Add annotations
        testInfo.annotations.push({ type: 'feature', description: feature.name });
        testInfo.annotations.push({ type: 'tags', description: scenario.tags.join(', ') });
        testInfo.annotations.push({ type: 'file', description: feature.file });

        console.log(`🎬 Starting scenario: ${scenario.name}`);
        console.log(`📁 Feature: ${feature.name}`);
        console.log(`🏷️ Tags: ${scenario.tags.join(', ')}`);

        // Execute the scenario using the runner (with background steps)
        await runner.executeScenario(scenario, page, context, testInfo, feature.backgroundSteps);
      });
    }
  });
}
