import { test, expect } from '@playwright/test';
import { PlaywrightCucumberRunner } from '../src/core/playwright-cucumber-runner';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Native Playwright-Cucumber Integration
 *
 * This converts Cucumber scenarios directly into Playwright tests,
 * providing proper step-by-step logging and native Playwright reporting.
 */

// Get test configuration from environment variables
const TEST_TAGS = process.env.TEST_TAGS || '@spo';

// Initialize the runner
const runner = new PlaywrightCucumberRunner();

// Load features synchronously at module load time
const features = runner.loadFeaturesSync('src/features/**/*.feature');

console.log(`🥒 Loaded ${features.length} features for tags: ${TEST_TAGS}`);

// Generate tests for each feature and scenario
for (const feature of features) {
  test.describe(feature.name, () => {
    // Group scenarios by base name (without [1], [2], etc.)
    const scenarioGroups = new Map<string, typeof feature.scenarios>();

    for (const scenario of feature.scenarios) {
      if (TEST_TAGS && !runner.matchesTags(scenario.tags, TEST_TAGS)) {
        continue;
      }

      // Extract base scenario name (remove [1], [2], etc.)
      const baseName = scenario.name.replace(/\s*\[\d+\]$/, '');

      if (!scenarioGroups.has(baseName)) {
        scenarioGroups.set(baseName, []);
      }
      scenarioGroups.get(baseName)!.push(scenario);
    }

    // Create tests for each scenario group
    for (const [baseName, scenarios] of scenarioGroups) {
      if (scenarios.length > 1) {
        // Multiple scenarios (from Scenario Outline) - run in same browser session
        test(`${baseName} - All Data Sets`, async ({ page, context }, testInfo) => {
          // Add annotations
          testInfo.annotations.push({ type: 'feature', description: feature.name });
          testInfo.annotations.push({ type: 'tags', description: scenarios[0].tags.join(', ') });
          testInfo.annotations.push({ type: 'file', description: feature.file });
          testInfo.annotations.push({ type: 'data-sets', description: `${scenarios.length} data sets` });

          console.log(`🎬 Starting scenario group: ${baseName}`);
          console.log(`📁 Feature: ${feature.name}`);
          console.log(`🔢 Data sets: ${scenarios.length}`);
          console.log(`🏷️ Tags: ${scenarios[0].tags.join(', ')}`);

          // Execute background steps once
          if (feature.backgroundSteps.length > 0) {
            console.log(`🏗️ Executing background steps once for all data sets`);
            await runner.executeBackgroundSteps(feature.backgroundSteps, page, context, testInfo);
          }

          // Execute each data set in the same browser session
          for (let i = 0; i < scenarios.length; i++) {
            const scenario = scenarios[i];
            console.log(`\n📊 Executing data set ${i + 1}/${scenarios.length}: ${scenario.name}`);

            // Reset to clean state before each data set (except first)
            if (i > 0) {
              console.log(`🔄 Resetting to clean state for next data set`);
              await runner.resetToCleanState(page, context, testInfo);
            }

            // Execute scenario steps (without background)
            await runner.executeScenarioSteps(scenario, page, context, testInfo, i + 1);
          }
        });
      } else {
        // Single scenario - run normally
        const scenario = scenarios[0];
        test(scenario.name, async ({ page, context }, testInfo) => {
          // Add annotations
          testInfo.annotations.push({ type: 'feature', description: feature.name });
          testInfo.annotations.push({ type: 'tags', description: scenario.tags.join(', ') });
          testInfo.annotations.push({ type: 'file', description: feature.file });

          console.log(`🎬 Starting scenario: ${scenario.name}`);
          console.log(`📁 Feature: ${feature.name}`);
          console.log(`🏷️ Tags: ${scenario.tags.join(', ')}`);

          // Execute the scenario using the runner (with background steps)
          await runner.executeScenario(scenario, page, context, testInfo, feature.backgroundSteps);
        });
      }
    }
  });
}
