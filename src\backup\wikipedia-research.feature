@research @wikipedia
Feature: Wikipedia Research
  As a researcher
  I want to search for information, navigate between articles, and use various Wikipedia features
  So that I can gather knowledge efficiently

  Background:
    Given I navigate to "https://www.wikipedia.org"
    And I wait for page to load

  @smoke @search
  Scenario: Search for an article on Wikipedia
    When I fill "#searchInput" with "Automation testing"
    And I click on "button[type='submit']"
    And I wait for "#firstHeading" to be visible
    Then the "#firstHeading" element should contain text "Automation testing"
    And the page should contain text "Software testing"
    And the page should contain text "Quality assurance"

  @language-selection
  Scenario: Change Wikipedia language
    When I click on "#js-link-box-en"
    And I wait for "#p-logo" to be visible
    Then the page should contain text "English"
    
    When I click on "#p-lang-label"
    And I wait for "#p-lang .interlanguage-link" to be visible
    And I click on ".interlanguage-link:contains('Español')"
    And I wait for "#p-logo" to be visible
    Then the page should contain text "Español"
    And the page URL should contain "es.wikipedia.org"

  @article-navigation
  Scenario: Navigate between linked articles
    When I fill "#searchInput" with "Solar System"
    And I click on "button[type='submit']"
    And I wait for "#firstHeading" to be visible
    Then the "#firstHeading" element should contain text "Solar System"
    
    When I store the text from "#firstHeading" as "first_article"
    And I click on "a:contains('Earth')"
    And I wait for "#firstHeading" to be visible
    Then the "#firstHeading" element should contain text "Earth"
    And the "#firstHeading" element should not contain text stored in variable "first_article"
    
    When I click on "a:contains('Moon')"
    And I wait for "#firstHeading" to be visible
    Then the "#firstHeading" element should contain text "Moon"
    
    When I click on "#ca-history"
    And I wait for "#pagehistory" to be visible
    Then the page should contain text "Revision history"

  @table-of-contents
  Scenario: Use table of contents to navigate within an article
    When I fill "#searchInput" with "Artificial intelligence"
    And I click on "button[type='submit']"
    And I wait for "#firstHeading" to be visible
    Then the "#firstHeading" element should contain text "Artificial intelligence"
    
    When I click on "#toc a:contains('History')"
    Then the page URL should contain "#History"
    
    When I click on "#toc a:contains('Applications')"
    Then the page URL should contain "#Applications"
    
    When I click on "#toc a:contains('Philosophy')"
    Then the page URL should contain "#Philosophy"

  @references
  Scenario: Check article references
    When I fill "#searchInput" with "COVID-19 pandemic"
    And I click on "button[type='submit']"
    And I wait for "#firstHeading" to be visible
    Then the "#firstHeading" element should contain text "COVID-19 pandemic"
    
    When I click on "#toc a:contains('References')"
    Then the page URL should contain "#References"
    And the page should contain text "References"
    
    When I click on ".reference a:first-child"
    And I wait for ".mw-parser-output .reference-text" to be visible
    Then the page should contain text "cite"

  @categories
  Scenario: Browse article categories
    When I fill "#searchInput" with "Machine learning"
    And I click on "button[type='submit']"
    And I wait for "#firstHeading" to be visible
    Then the "#firstHeading" element should contain text "Machine learning"
    
    When I scroll to "#catlinks"
    Then the page should contain text "Categories"
    
    When I click on "a:contains('Artificial intelligence')"
    And I wait for "#firstHeading" to be visible
    Then the "#firstHeading" element should contain text "Category:"
    And the page should contain text "Artificial intelligence"
    
    When I click on "a.CategoryTreeLabel:first-child"
    And I wait for ".mw-category" to be visible
    Then there should be more than 0 ".mw-category-group" elements

  @featured-content
  Scenario: View featured content
    When I click on "#js-link-box-en"
    And I wait for "#p-logo" to be visible
    
    When I click on "a:contains('Featured content')"
    And I wait for "#firstHeading" to be visible
    Then the page should contain text "Featured content"
    
    When I click on "a:contains('Featured articles')"
    And I wait for "#firstHeading" to be visible
    Then the page should contain text "Featured articles"
    And there should be more than 0 ".featured_article_metadata" elements
    
    When I click on ".featured_article_metadata a:first-child"
    And I wait for "#firstHeading" to be visible
    Then the page should contain text "Featured article"

  @random-article
  Scenario: View random articles
    When I click on "#js-link-box-en"
    And I wait for "#p-logo" to be visible
    
    When I click on "a:contains('Random article')"
    And I wait for "#firstHeading" to be visible
    And I store the text from "#firstHeading" as "first_random"
    
    When I click on "a:contains('Random article')"
    And I wait for "#firstHeading" to be visible
    And I store the text from "#firstHeading" as "second_random"
    
    Then the stored variable "first_random" should not equal stored variable "second_random"

  @media-content
  Scenario: View media content in articles
    When I fill "#searchInput" with "Eiffel Tower"
    And I click on "button[type='submit']"
    And I wait for "#firstHeading" to be visible
    Then the "#firstHeading" element should contain text "Eiffel Tower"
    
    When I click on ".infobox-image img"
    And I wait for ".mw-mmv-image" to be visible
    Then the page should contain text "Eiffel Tower"
    
    When I click on ".mw-mmv-next-image"
    And I wait for 1000 milliseconds
    Then the ".mw-mmv-image" element should be visible
