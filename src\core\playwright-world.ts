import { Page, BrowserContext, expect } from '@playwright/test';
import { Lo<PERSON> } from '../utils/logger';
import { UiHelper } from '../utils/ui.helper';
import { EnvConfig } from '../config/env.config';

/**
 * Playwright World
 * 
 * A simplified world class for Playwright tests that doesn't depend on Cucumber.
 * This provides the same interface as CustomWorld but without Cucumber dependencies.
 */

export class PlaywrightWorld {
  public page!: Page;
  public context!: BrowserContext;
  private logger: Logger;
  private uiHelper!: UiHelper;

  constructor() {
    this.logger = new Logger('PlaywrightWorld');
  }

  /**
   * Initialize the world with Playwright page and context
   */
  async init(page: Page, context: BrowserContext): Promise<void> {
    this.page = page;
    this.context = context;
    this.uiHelper = new UiHelper(page);
    this.logger.info('🌍 Playwright world initialized');
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    this.logger.info('🧹 Cleaning up Playwright world');
    // Page and context cleanup is handled by Playwright test framework
  }

  /**
   * Get current page URL
   */
  getCurrentUrl(): string {
    return this.page.url();
  }

  /**
   * Get page title
   */
  async getTitle(): Promise<string> {
    return await this.page.title();
  }

  /**
   * Take a screenshot
   */
  async takeScreenshot(name?: string): Promise<string> {
    const timestamp = Date.now();
    const filename = name ? `${name}-${timestamp}.png` : `screenshot-${timestamp}.png`;
    const path = `test-results/screenshots/${filename}`;

    await this.page.screenshot({
      path,
      fullPage: true
    });

    this.logger.info(`📸 Screenshot saved: ${path}`);
    return path;
  }

  /**
   * Take a screenshot with element highlighting
   */
  async takeScreenshotWithHighlight(selector: string, name?: string): Promise<string> {
    try {
      const locator = this.getLocator(selector);

      // Highlight the element
      await this.uiHelper.highlightLocator(locator, { duration: 0 }); // No auto-remove

      // Take screenshot
      const timestamp = Date.now();
      const filename = name ? `${name}-highlighted-${timestamp}.png` : `screenshot-highlighted-${timestamp}.png`;
      const path = `test-results/screenshots/${filename}`;

      await this.page.screenshot({
        path,
        fullPage: true
      });

      // Remove highlight
      await locator.evaluate((element: HTMLElement) => {
        if (element) {
          element.style.outline = element.getAttribute('data-original-outline') || '';
          element.style.outlineOffset = element.getAttribute('data-original-outline-offset') || '';
          element.style.boxShadow = element.getAttribute('data-original-box-shadow') || '';
          element.style.transition = '';
          element.removeAttribute('data-original-outline');
          element.removeAttribute('data-original-outline-offset');
          element.removeAttribute('data-original-box-shadow');
        }
      });

      this.logger.info(`📸 Highlighted screenshot saved: ${path}`);
      return path;
    } catch (error) {
      this.logger.warn(`Failed to take highlighted screenshot: ${error}`);
      return await this.takeScreenshot(name);
    }
  }

  /**
   * Wait for page to load
   */
  async waitForPageLoad(): Promise<void> {
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Navigate to URL
   */
  async navigateTo(url: string): Promise<void> {
    this.logger.info(`🌐 Navigating to: ${url}`);
    await this.page.goto(url);
    await this.waitForPageLoad();
  }

  /**
   * Quick, non-blocking element highlighting for actions (red)
   */
  private async quickHighlight(locator: any): Promise<void> {
    try {
      // Very fast highlight with minimal duration for actions
      await Promise.race([
        locator.first().evaluate((element: HTMLElement) => {
          if (element) {
            // Apply quick red highlight for actions
            element.style.outline = '2px solid #ff0000';
            element.style.outlineOffset = '1px';
            element.style.boxShadow = '0 0 5px #ff0000';
            element.style.backgroundColor = 'rgba(255, 0, 0, 0.1)';
            element.style.transition = 'all 0.2s ease';

            // Auto-remove after short delay
            setTimeout(() => {
              element.style.outline = '';
              element.style.outlineOffset = '';
              element.style.boxShadow = '';
              element.style.backgroundColor = '';
              element.style.transition = '';
            }, 300);
          }
        }),
        // Timeout after 500ms to prevent hanging
        new Promise((_, reject) => setTimeout(() => reject(new Error('Action highlight timeout')), 500))
      ]);
    } catch {
      // Silently ignore all highlighting errors
    }
  }

  /**
   * Assertion highlighting for verification steps - more prominent and longer duration (green)
   */
  private async assertionHighlight(locator: any): Promise<void> {
    try {
      // More prominent highlight for assertions
      await Promise.race([
        locator.first().evaluate((element: HTMLElement) => {
          if (element) {
            // Apply assertion highlight with green color for success
            element.style.outline = '3px solid #00ff00';
            element.style.outlineOffset = '2px';
            element.style.boxShadow = '0 0 10px #00ff00, inset 0 0 5px rgba(0, 255, 0, 0.2)';
            element.style.backgroundColor = 'rgba(0, 255, 0, 0.15)';
            element.style.transition = 'all 0.2s ease';

            // Auto-remove after longer delay for assertions
            setTimeout(() => {
              element.style.outline = '';
              element.style.outlineOffset = '';
              element.style.boxShadow = '';
              element.style.backgroundColor = '';
              element.style.transition = '';
            }, 800);
          }
        }),
        // Timeout after 1 second to prevent hanging
        new Promise((_, reject) => setTimeout(() => reject(new Error('Assertion highlight timeout')), 1000))
      ]);
    } catch {
      // Silently ignore all highlighting errors
    }
  }

  /**
   * Assertion highlighting for verification steps - more prominent and longer duration
   */
  private async assertionHighlight(locator: any): Promise<void> {
    try {
      // More prominent highlight for assertions
      await Promise.race([
        locator.evaluate((element: HTMLElement) => {
          if (element) {
            // Apply assertion highlight with green color for success
            element.style.outline = '3px solid #00ff00';
            element.style.outlineOffset = '2px';
            element.style.boxShadow = '0 0 10px #00ff00, inset 0 0 5px rgba(0, 255, 0, 0.2)';
            element.style.backgroundColor = 'rgba(0, 255, 0, 0.1)';

            // Auto-remove after longer delay for assertions
            setTimeout(() => {
              element.style.outline = '';
              element.style.outlineOffset = '';
              element.style.boxShadow = '';
              element.style.backgroundColor = '';
            }, 800);
          }
        }),
        // Timeout after 1 second to prevent hanging
        new Promise((_, reject) => setTimeout(() => reject(new Error('Assertion highlight timeout')), 1000))
      ]);
    } catch {
      // Silently ignore all highlighting errors
    }
  }

  /**
   * Get a Playwright locator from string (public method for step definitions)
   */
  public getLocator(selector: string): any {
    return this.parseLocator(selector);
  }

  /**
   * Parse Playwright locator from string
   */
  private parseLocator(selector: string): any {
    // Handle chained locators like getByLabel('Datasources').getByText('Global', { exact: true })
    // or locator('#menu-').getByText('FeaturedResult')
    // or locator('.MuiChip-label').first()
    if (selector.includes('.getBy') || selector.startsWith('locator(') || selector.includes('.first()') || selector.includes('.last()') || selector.includes('.nth(')) {
      return this.parseChainedLocator(selector);
    }

    // Check if it's a Playwright locator method
    if (selector.startsWith('getByRole(')) {
      return this.parseGetByRole(selector);
    } else if (selector.startsWith('getByText(')) {
      return this.parseGetByText(selector);
    } else if (selector.startsWith('getByLabel(')) {
      return this.parseGetByLabel(selector);
    } else if (selector.startsWith('getByPlaceholder(')) {
      return this.parseGetByPlaceholder(selector);
    } else if (selector.startsWith('getByTestId(')) {
      return this.parseGetByTestId(selector);
    } else {
      // Regular CSS selector
      return this.page.locator(selector);
    }
  }

  /**
   * Parse getByRole locator
   */
  private parseGetByRole(selector: string) {
    // Extract role and options from getByRole('role', { options })
    const match = selector.match(/getByRole\('([^']+)'(?:,\s*({[^}]+}))?\)/);
    if (!match) {
      throw new Error(`Invalid getByRole syntax: ${selector}`);
    }

    const role = match[1];
    const optionsStr = match[2];

    if (optionsStr) {
      // Parse options like { name: 'text', exact: true }
      try {
        const options = eval(`(${optionsStr})`);
        return this.page.getByRole(role as any, options);
      } catch (error) {
        this.logger.warn(`Failed to parse getByRole options: ${optionsStr}, using role only`);
        return this.page.getByRole(role as any);
      }
    } else {
      return this.page.getByRole(role as any);
    }
  }

  /**
   * Parse getByText locator
   */
  private parseGetByText(selector: string) {
    const match = selector.match(/getByText\('([^']+)'(?:,\s*({[^}]+}))?\)/);
    if (!match) {
      throw new Error(`Invalid getByText syntax: ${selector}`);
    }

    const text = match[1];
    const optionsStr = match[2];

    if (optionsStr) {
      try {
        const options = eval(`(${optionsStr})`);
        return this.page.getByText(text, options);
      } catch (error) {
        return this.page.getByText(text);
      }
    } else {
      return this.page.getByText(text);
    }
  }

  /**
   * Parse getByLabel locator
   */
  private parseGetByLabel(selector: string) {
    const match = selector.match(/getByLabel\('([^']+)'(?:,\s*({[^}]+}))?\)/);
    if (!match) {
      throw new Error(`Invalid getByLabel syntax: ${selector}`);
    }

    const label = match[1];
    const optionsStr = match[2];

    if (optionsStr) {
      try {
        const options = eval(`(${optionsStr})`);
        return this.page.getByLabel(label, options);
      } catch (error) {
        return this.page.getByLabel(label);
      }
    } else {
      return this.page.getByLabel(label);
    }
  }

  /**
   * Parse getByPlaceholder locator
   */
  private parseGetByPlaceholder(selector: string) {
    const match = selector.match(/getByPlaceholder\('([^']+)'(?:,\s*({[^}]+}))?\)/);
    if (!match) {
      throw new Error(`Invalid getByPlaceholder syntax: ${selector}`);
    }

    const placeholder = match[1];
    const optionsStr = match[2];

    if (optionsStr) {
      try {
        const options = eval(`(${optionsStr})`);
        return this.page.getByPlaceholder(placeholder, options);
      } catch (error) {
        return this.page.getByPlaceholder(placeholder);
      }
    } else {
      return this.page.getByPlaceholder(placeholder);
    }
  }

  /**
   * Parse getByTestId locator
   */
  private parseGetByTestId(selector: string) {
    const match = selector.match(/getByTestId\('([^']+)'\)/);
    if (!match) {
      throw new Error(`Invalid getByTestId syntax: ${selector}`);
    }

    const testId = match[1];
    return this.page.getByTestId(testId);
  }

  /**
   * Parse chained locators like getByLabel('Datasources').getByText('Global', { exact: true })
   * or locator('#menu-').getByText('FeaturedResult')
   * or locator('.MuiChip-label').first()
   */
  private parseChainedLocator(selector: string): any {
    try {
      // Handle locator() calls with chained methods
      if (selector.startsWith('locator(')) {
        return this.parseLocatorWithChain(selector);
      }

      // Handle simple locator with position methods like .first(), .last(), .nth()
      if (selector.includes('.first()') || selector.includes('.last()') || selector.includes('.nth(')) {
        return this.parseLocatorWithPosition(selector);
      }

      // Split the selector into parts for regular chained locators
      const parts = selector.split('.getBy');

      if (parts.length < 2) {
        throw new Error(`Invalid chained locator: ${selector}`);
      }

      // Parse the first part without recursion
      let locator: any;
      const firstPart = parts[0];

      if (firstPart.startsWith('getByRole(')) {
        locator = this.parseGetByRole(firstPart);
      } else if (firstPart.startsWith('getByText(')) {
        locator = this.parseGetByText(firstPart);
      } else if (firstPart.startsWith('getByLabel(')) {
        locator = this.parseGetByLabel(firstPart);
      } else if (firstPart.startsWith('getByPlaceholder(')) {
        locator = this.parseGetByPlaceholder(firstPart);
      } else if (firstPart.startsWith('getByTestId(')) {
        locator = this.parseGetByTestId(firstPart);
      } else {
        locator = this.page.locator(firstPart);
      }

      // Chain the remaining parts
      for (let i = 1; i < parts.length; i++) {
        const part = 'getBy' + parts[i];

        if (part.startsWith('getByText(')) {
          const match = part.match(/getByText\('([^']+)'(?:,\s*({[^}]+}))?\)/);
          if (match) {
            const text = match[1];
            const optionsStr = match[2];

            if (optionsStr) {
              try {
                const options = eval(`(${optionsStr})`);
                locator = locator.getByText(text, options);
              } catch {
                locator = locator.getByText(text);
              }
            } else {
              locator = locator.getByText(text);
            }
          }
        } else if (part.startsWith('getByRole(')) {
          const match = part.match(/getByRole\('([^']+)'(?:,\s*({[^}]+}))?\)/);
          if (match) {
            const role = match[1];
            const optionsStr = match[2];

            if (optionsStr) {
              try {
                const options = eval(`(${optionsStr})`);
                locator = locator.getByRole(role as any, options);
              } catch {
                locator = locator.getByRole(role as any);
              }
            } else {
              locator = locator.getByRole(role as any);
            }
          }
        }
        // Add more chained locator types as needed
      }

      return locator;

    } catch (error) {
      this.logger.warn(`Failed to parse chained locator: ${selector}, falling back to simple locator`);
      return this.page.locator(selector);
    }
  }

  /**
   * Parse locators with position methods like locator('.MuiChip-label').first()
   */
  private parseLocatorWithPosition(selector: string): any {
    try {
      // Handle .first()
      if (selector.endsWith('.first()')) {
        const baseSelector = selector.replace('.first()', '');
        // Avoid recursion - directly create locator for simple CSS selectors
        if (baseSelector.startsWith('locator(')) {
          // Extract CSS selector from locator('selector')
          const match = baseSelector.match(/^locator\('([^']+)'\)$/);
          if (match) {
            return this.page.locator(match[1]).first();
          }
        } else if (!baseSelector.includes('getBy') && !baseSelector.includes('.')) {
          // Simple CSS selector
          return this.page.locator(baseSelector).first();
        } else {
          // Complex selector - use recursive parsing but avoid infinite loop
          const baseLocator = this.parseLocatorDirect(baseSelector);
          return baseLocator.first();
        }
      }

      // Handle .last()
      if (selector.endsWith('.last()')) {
        const baseSelector = selector.replace('.last()', '');
        if (baseSelector.startsWith('locator(')) {
          const match = baseSelector.match(/^locator\('([^']+)'\)$/);
          if (match) {
            return this.page.locator(match[1]).last();
          }
        } else if (!baseSelector.includes('getBy') && !baseSelector.includes('.')) {
          return this.page.locator(baseSelector).last();
        } else {
          const baseLocator = this.parseLocatorDirect(baseSelector);
          return baseLocator.last();
        }
      }

      // Handle .nth(index)
      const nthMatch = selector.match(/(.+)\.nth\((\d+)\)$/);
      if (nthMatch) {
        const baseSelector = nthMatch[1];
        const index = parseInt(nthMatch[2]);
        if (baseSelector.startsWith('locator(')) {
          const match = baseSelector.match(/^locator\('([^']+)'\)$/);
          if (match) {
            return this.page.locator(match[1]).nth(index);
          }
        } else if (!baseSelector.includes('getBy') && !baseSelector.includes('.')) {
          return this.page.locator(baseSelector).nth(index);
        } else {
          const baseLocator = this.parseLocatorDirect(baseSelector);
          return baseLocator.nth(index);
        }
      }

      // If no position method found, fall back to regular parsing
      return this.page.locator(selector);

    } catch (error) {
      this.logger.warn(`Failed to parse locator with position: ${selector}, falling back to simple locator`);
      return this.page.locator(selector);
    }
  }

  /**
   * Direct locator parsing without position methods to avoid recursion
   */
  private parseLocatorDirect(selector: string): any {
    // Check if it's a Playwright locator method
    if (selector.startsWith('getByRole(')) {
      return this.parseGetByRole(selector);
    } else if (selector.startsWith('getByText(')) {
      return this.parseGetByText(selector);
    } else if (selector.startsWith('getByLabel(')) {
      return this.parseGetByLabel(selector);
    } else if (selector.startsWith('getByPlaceholder(')) {
      return this.parseGetByPlaceholder(selector);
    } else if (selector.startsWith('getByTestId(')) {
      return this.parseGetByTestId(selector);
    } else {
      // Regular CSS selector
      return this.page.locator(selector);
    }
  }

  /**
   * Parse locator() calls with chained methods like locator('#menu-').getByText('FeaturedResult')
   */
  private parseLocatorWithChain(selector: string): any {
    try {
      // Extract the CSS selector from locator('selector')
      const locatorMatch = selector.match(/^locator\('([^']+)'\)/);
      if (!locatorMatch) {
        throw new Error(`Invalid locator syntax: ${selector}`);
      }

      const cssSelector = locatorMatch[1];
      let locator = this.page.locator(cssSelector);

      // Check if there are chained methods
      const remainingSelector = selector.substring(locatorMatch[0].length);
      if (remainingSelector.startsWith('.getBy')) {
        // Parse chained methods
        const chainedParts = remainingSelector.split('.getBy');

        for (let i = 1; i < chainedParts.length; i++) {
          const part = 'getBy' + chainedParts[i];

          if (part.startsWith('getByText(')) {
            const match = part.match(/getByText\('([^']+)'(?:,\s*({[^}]+}))?\)/);
            if (match) {
              const text = match[1];
              const optionsStr = match[2];

              if (optionsStr) {
                try {
                  const options = eval(`(${optionsStr})`);
                  locator = locator.getByText(text, options);
                } catch {
                  locator = locator.getByText(text);
                }
              } else {
                locator = locator.getByText(text);
              }
            }
          } else if (part.startsWith('getByRole(')) {
            const match = part.match(/getByRole\('([^']+)'(?:,\s*({[^}]+}))?\)/);
            if (match) {
              const role = match[1];
              const optionsStr = match[2];

              if (optionsStr) {
                try {
                  const options = eval(`(${optionsStr})`);
                  locator = locator.getByRole(role as any, options);
                } catch {
                  locator = locator.getByRole(role as any);
                }
              } else {
                locator = locator.getByRole(role as any);
              }
            }
          } else if (part.startsWith('getByLabel(')) {
            const match = part.match(/getByLabel\('([^']+)'(?:,\s*({[^}]+}))?\)/);
            if (match) {
              const label = match[1];
              const optionsStr = match[2];

              if (optionsStr) {
                try {
                  const options = eval(`(${optionsStr})`);
                  locator = locator.getByLabel(label, options);
                } catch {
                  locator = locator.getByLabel(label);
                }
              } else {
                locator = locator.getByLabel(label);
              }
            }
          }
          // Add more chained locator types as needed
        }
      }

      return locator;

    } catch (error) {
      this.logger.warn(`Failed to parse locator with chain: ${selector}, falling back to simple locator`);
      return this.page.locator(selector);
    }
  }

  /**
   * Click on element with action highlighting (red)
   */
  async clickElement(selector: string): Promise<void> {
    this.logger.info(`🖱️ Clicking: ${selector}`);
    const element = this.parseLocator(selector);

    // Action highlight before clicking (red)
    if (EnvConfig.HIGHLIGHT_ELEMENTS) {
      try {
        await this.quickHighlight(element);
      } catch {
        // Ignore highlighting errors
      }
    }

    await element.click();
  }

  /**
   * Fill input field with action highlighting (red)
   */
  async fillField(selector: string, value: string): Promise<void> {
    this.logger.info(`⌨️ Filling ${selector} with: ${value}`);
    const element = this.parseLocator(selector);

    // Action highlight before filling (red)
    if (EnvConfig.HIGHLIGHT_ELEMENTS) {
      try {
        await this.quickHighlight(element);
      } catch {
        // Ignore highlighting errors
      }
    }

    await element.fill(value);
  }

  /**
   * Type in input field
   */
  async typeInField(selector: string, value: string): Promise<void> {
    this.logger.info(`⌨️ Typing ${value} in: ${selector}`);
    const element = this.parseLocator(selector);
    await element.type(value);
  }

  /**
   * Wait for element to be visible with action highlighting (red)
   */
  async waitForElement(selector: string, timeout: number = 30000): Promise<void> {
    this.logger.info(`⏳ Waiting for element: ${selector}`);
    const element = this.parseLocator(selector);
    await element.waitFor({ state: 'visible', timeout });

    // Action highlight after element becomes visible (red)
    if (EnvConfig.HIGHLIGHT_ELEMENTS) {
      try {
        await this.quickHighlight(element);
      } catch {
        // Ignore highlighting errors
      }
    }
  }

  /**
   * Check if element is visible
   */
  async isElementVisible(selector: string): Promise<boolean> {
    try {
      const element = this.parseLocator(selector);
      return await element.isVisible();
    } catch {
      return false;
    }
  }

  /**
   * Get element text
   */
  async getElementText(selector: string): Promise<string> {
    const element = this.parseLocator(selector);
    return await element.textContent() || '';
  }

  /**
   * Press keyboard key
   */
  async pressKey(key: string): Promise<void> {
    this.logger.info(`⌨️ Pressing key: ${key}`);
    await this.page.keyboard.press(key);
  }

  /**
   * Scroll to element
   */
  async scrollToElement(selector: string): Promise<void> {
    this.logger.info(`📜 Scrolling to: ${selector}`);
    const element = this.parseLocator(selector);
    await element.scrollIntoViewIfNeeded();
  }

  /**
   * Wait for specified time
   */
  async wait(milliseconds: number): Promise<void> {
    this.logger.info(`⏳ Waiting for ${milliseconds}ms`);
    await this.page.waitForTimeout(milliseconds);
  }

  /**
   * Verify text is present on page
   */
  async verifyTextPresent(text: string): Promise<void> {
    this.logger.info(`🔍 Verifying page contains: ${text}`);
    const bodyLocator = this.page.locator('body');
    await expect(bodyLocator).toContainText(text);
  }

  /**
   * Verify element is visible with assertion highlighting (green)
   */
  async verifyElementVisible(selector: string): Promise<void> {
    this.logger.info(`👁️ Verifying element is visible: ${selector}`);
    const element = this.parseLocator(selector);

    await expect(element).toBeVisible();

    // Assertion highlight for verification (green)
    try {
      await this.assertionHighlight(element);
    } catch {
      // Ignore highlighting errors
    }
  }

  /**
   * Verify page title contains text
   */
  async verifyTitleContains(text: string): Promise<void> {
    this.logger.info(`📄 Verifying title contains: ${text}`);
    const title = await this.getTitle();
    if (!title.includes(text)) {
      throw new Error(`Expected title to contain "${text}", but got "${title}"`);
    }
  }

  /**
   * Verify element contains text with assertion highlighting (green)
   */
  async verifyElementContainsText(selector: string, text: string): Promise<void> {
    this.logger.info(`🔍 Verifying ${selector} contains: ${text}`);
    const element = this.parseLocator(selector);

    await expect(element).toContainText(text);

    // Assertion highlight for text verification (green)
    try {
      await this.assertionHighlight(element);
    } catch {
      // Ignore highlighting errors
    }
  }
}
