@news @bbc
Feature: BBC News Website
  As a news reader
  I want to browse news articles, watch videos, and use various BBC features
  So that I can stay informed about current events

  Background:
    Given I navigate to "https://www.bbc.com/news"
    And I wait for page to load

  @smoke @headlines
  Scenario: View top headlines
    When I wait for ".gs-c-promo-heading" to be visible
    Then there should be more than 3 ".gs-c-promo-heading" elements
    
    When I click on ".gs-c-promo-heading:first-child"
    And I wait for ".article-headline" to be visible
    And I store the text from ".article-headline" as "headline_text"
    Then the page title should contain stored variable "headline_text"
    And the page should contain text "Published"
    And the page should contain text "Share"

  @news-categories
  Scenario: Browse different news categories
    When I click on "nav.nw-c-nav__wide a:contains('World')"
    And I wait for ".nw-c-nav__wide-secondary" to be visible
    Then the page should contain text "World"
    And the page URL should contain "/world"
    
    When I click on "nav.nw-c-nav__wide a:contains('Business')"
    And I wait for ".nw-c-nav__wide-secondary" to be visible
    Then the page should contain text "Business"
    And the page URL should contain "/business"
    
    When I click on "nav.nw-c-nav__wide a:contains('Technology')"
    And I wait for ".nw-c-nav__wide-secondary" to be visible
    Then the page should contain text "Technology"
    And the page URL should contain "/technology"
    
    When I click on "nav.nw-c-nav__wide a:contains('Science')"
    And I wait for ".nw-c-nav__wide-secondary" to be visible
    Then the page should contain text "Science"
    And the page URL should contain "/science"

  @search-news
  Scenario: Search for news articles
    When I click on "a:contains('Search BBC')"
    And I wait for "#search-input" to be visible
    And I fill "#search-input" with "climate change"
    And I click on "button.ssrcss-1qmiys6-IconButton"
    And I wait for ".ssrcss-v2iqb2-Container" to be visible
    Then the page should contain text "climate change"
    And there should be more than 0 ".ssrcss-1v7bxtk-StyledContainer" elements
    
    When I click on ".ssrcss-1v7bxtk-StyledContainer:first-child a"
    And I wait for ".article-headline" to be visible
    Then the page should contain text "climate"

  @video-content
  Scenario: Watch video content
    When I click on "a:contains('Video')"
    And I wait for ".gs-c-promo-heading" to be visible
    And I click on ".gs-c-promo-heading:first-child"
    And I wait for ".media-player" to be visible
    Then the page should contain text "Video"
    And the ".media-player" element should be visible
    
    When I click on "button.tp-play-button"
    And I wait for 3000 milliseconds
    Then the "button.tp-pause-button" element should be visible

  @live-coverage
  Scenario: Check live news coverage
    When I click on "a:contains('Live')"
    And I wait for ".gs-c-promo-heading" to be visible
    And I click on ".gs-c-promo-heading:first-child"
    And I wait for ".lx-stream" to be visible
    Then the page should contain text "Live"
    And the page should contain text "Latest updates"
    And there should be more than 0 ".lx-stream__post" elements

  @most-read
  Scenario: View most read articles
    When I scroll to ".nw-c-most-read"
    Then the page should contain text "Most Read"
    And there should be more than 0 ".nw-c-most-read__items" elements
    
    When I click on ".nw-c-most-read__items li:first-child a"
    And I wait for ".article-headline" to be visible
    Then the page should contain text "Published"
    And the page should contain text "Share"

  @news-by-region
  Scenario: View news by region
    When I click on "a:contains('UK')"
    And I wait for ".nw-c-nav__wide-secondary" to be visible
    Then the page should contain text "UK"
    And the page URL should contain "/uk"
    
    When I click on "a:contains('England')"
    And I wait for ".nw-c-nav__wide-secondary" to be visible
    Then the page should contain text "England"
    And the page URL should contain "/england"
    
    When I click on "a:contains('London')"
    And I wait for ".gs-c-promo-heading" to be visible
    Then the page should contain text "London"
    And the page URL should contain "/london"

  @share-article
  Scenario: Share a news article
    When I click on ".gs-c-promo-heading:first-child"
    And I wait for ".article-headline" to be visible
    
    When I click on "button:contains('Share')"
    And I wait for ".ssrcss-1f3alqp-MenuContainer" to be visible
    Then the page should contain text "Facebook"
    And the page should contain text "Twitter"
    And the page should contain text "Email"
    
    When I click on "button:contains('Copy link')"
    Then the page should contain text "Copied"

  @news-alerts
  Scenario: Sign up for news alerts
    When I click on ".gs-c-promo-heading:first-child"
    And I wait for ".article-headline" to be visible
    
    When I scroll to "a:contains('Get news alerts')"
    And I click on "a:contains('Get news alerts')"
    And I wait for page to load
    Then the page should contain text "BBC News Daily"
    And the page should contain text "Email"
    And the page should contain text "Subscribe"
    
    When I fill "input[type='email']" with "<EMAIL>"
    And I click on "button:contains('Subscribe')"
    Then the page should contain text "Thank you"
