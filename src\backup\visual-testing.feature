@visual
Feature: Visual Testing
  As a QA engineer
  I want to verify the visual appearance of the application
  So that I can detect visual regressions

  Background:
    Given I navigate to "https://example.com"
    And I wait for page to load

  @smoke @visual
  Scenario: Verify login page appearance
    When I navigate to "https://example.com/login"
    And I wait for page to load
    Then the page should match the baseline image "login-page"

  @visual
  Scenario: Verify login form appearance
    When I navigate to "https://example.com/login"
    And I wait for page to load
    Then the element "#login-form" should match the baseline image "login-form"

  @visual
  Scenario: Verify login button appearance with custom threshold
    When I navigate to "https://example.com/login"
    And I wait for page to load
    Then the element "#login-button" should match the baseline image "login-button" with threshold 0.2

  @visual
  Scenario: Update baseline images
    When I navigate to "https://example.com/login"
    And I wait for page to load
    When I update the baseline image "login-page" with the current page
    And I update the baseline image "login-form" with the element "#login-form"
    And I update the baseline image "login-button" with the element "#login-button"

  @visual
  Scenario: Verify CSS properties
    When I navigate to "https://example.com/login"
    And I wait for page to load
    Then the element "#login-button" should have CSS property "background-color" with value "rgb(0, 123, 255)"
    And the element "#login-button" should have CSS property "color" with value "rgb(255, 255, 255)"
    And the element "#login-button" should have CSS property "border-radius" with value "4px"

  @visual
  Scenario: Verify element visibility
    When I navigate to "https://example.com/login"
    And I wait for page to load
    Then the element "#login-form" should be visually visible
    And the element "#hidden-element" should be visually hidden
