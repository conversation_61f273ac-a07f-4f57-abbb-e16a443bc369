"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MicrosoftAuth = void 0;
const logger_1 = require("../utils/logger");
const framework_config_1 = require("../config/framework.config");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
/**
 * Microsoft Authentication Manager
 *
 * Handles Microsoft SSO authentication with session persistence and reuse.
 * Supports various Microsoft authentication flows including:
 * - Azure AD authentication
 * - Office 365 authentication
 * - SharePoint Online authentication
 * - Multi-factor authentication (MFA)
 */
class MicrosoftAuth {
    logger;
    frameworkConfig;
    sessionPath;
    constructor() {
        this.logger = new logger_1.Logger('MicrosoftAuth');
        this.frameworkConfig = framework_config_1.FrameworkConfig.getInstance();
        this.sessionPath = path.join(this.frameworkConfig.get('authentication.sessionDirectory', './test-results/sessions'), 'microsoft-session.json');
    }
    /**
     * Authenticate with Microsoft using email and SSO
     * @param page The Playwright page
     * @param email The user email address
     * @param options Authentication options
     */
    async authenticate(page, email, options = {}) {
        const { reuseSession = this.frameworkConfig.get('authentication.reuseSession', true), timeout = 60000, waitForRedirect = true, expectedUrl } = options;
        this.logger.info(`Starting Microsoft authentication for: ${email}`);
        // Try to reuse existing session if enabled
        if (reuseSession && await this.isSessionValid(page)) {
            this.logger.info('Reusing existing Microsoft session');
            await this.loadSession(page);
            return;
        }
        // Perform fresh authentication
        await this.performAuthentication(page, email, timeout, waitForRedirect, expectedUrl);
        // Save session for future use
        if (reuseSession) {
            await this.saveSession(page);
        }
    }
    /**
     * Perform the actual authentication process
     */
    async performAuthentication(page, email, timeout, waitForRedirect, expectedUrl) {
        this.logger.info('Performing fresh Microsoft authentication');
        try {
            // Navigate to Microsoft Online login if not already there
            const currentUrl = page.url();
            if (!this.isMicrosoftLoginPage(currentUrl)) {
                this.logger.info('Navigating to Microsoft Online login');
                await page.goto('https://login.microsoftonline.com/', {
                    waitUntil: 'networkidle',
                    timeout
                });
            }
            // Wait for email input field
            this.logger.info('Waiting for email input field');
            const emailSelector = 'input[type="email"], input[name="loginfmt"], input[placeholder*="email"]';
            await page.waitForSelector(emailSelector, { state: 'visible', timeout });
            // Enter email address
            this.logger.info(`Entering email: ${email}`);
            await page.fill(emailSelector, email);
            // Click Next button
            this.logger.info('Clicking Next button');
            const nextButtonSelector = 'input[type="submit"], button[type="submit"], input[value="Next"], button:has-text("Next")';
            await page.click(nextButtonSelector);
            // Handle different authentication flows
            await this.handleAuthenticationFlow(page, timeout);
            // Wait for authentication to complete and redirect
            if (waitForRedirect) {
                await this.waitForAuthenticationComplete(page, timeout, expectedUrl);
            }
            this.logger.info('Microsoft authentication completed successfully');
        }
        catch (error) {
            this.logger.error(`Microsoft authentication failed: ${error}`);
            throw error;
        }
    }
    /**
     * Handle different authentication flows (password, MFA, SSO, etc.)
     */
    async handleAuthenticationFlow(page, timeout) {
        this.logger.info('Handling authentication flow');
        // Wait for the page to load after entering email
        await page.waitForTimeout(2000);
        const currentUrl = page.url();
        this.logger.info(`Current URL after email entry: ${currentUrl}`);
        // Check for different authentication scenarios
        if (await this.isPasswordRequired(page)) {
            await this.handlePasswordAuthentication(page, timeout);
        }
        else if (await this.isMfaRequired(page)) {
            await this.handleMfaAuthentication(page, timeout);
        }
        else if (await this.isSsoRedirect(page)) {
            await this.handleSsoRedirect(page, timeout);
        }
        else {
            // Default: wait for automatic SSO completion
            this.logger.info('Waiting for automatic SSO completion');
            await page.waitForTimeout(5000);
        }
    }
    /**
     * Check if password authentication is required
     */
    async isPasswordRequired(page) {
        const passwordSelectors = [
            'input[type="password"]',
            'input[name="passwd"]',
            'input[placeholder*="password"]'
        ];
        for (const selector of passwordSelectors) {
            try {
                await page.waitForSelector(selector, { state: 'visible', timeout: 3000 });
                return true;
            }
            catch {
                // Continue to next selector
            }
        }
        return false;
    }
    /**
     * Handle password authentication
     */
    async handlePasswordAuthentication(page, timeout) {
        this.logger.info('Password authentication required');
        const password = this.frameworkConfig.get('authentication.credentials.password');
        if (!password) {
            throw new Error('Password is required but not configured');
        }
        const passwordSelector = 'input[type="password"], input[name="passwd"]';
        await page.fill(passwordSelector, password);
        const signInButtonSelector = 'input[type="submit"], button[type="submit"], input[value*="Sign"], button:has-text("Sign")';
        await page.click(signInButtonSelector);
        // Wait for authentication to process
        await page.waitForTimeout(3000);
    }
    /**
     * Check if MFA is required
     */
    async isMfaRequired(page) {
        const mfaIndicators = [
            'text=Verify your identity',
            'text=Multi-factor authentication',
            'text=Enter code',
            'input[placeholder*="code"]'
        ];
        for (const indicator of mfaIndicators) {
            try {
                await page.waitForSelector(indicator, { state: 'visible', timeout: 3000 });
                return true;
            }
            catch {
                // Continue to next indicator
            }
        }
        return false;
    }
    /**
     * Handle MFA authentication
     */
    async handleMfaAuthentication(page, timeout) {
        this.logger.warn('MFA authentication detected - manual intervention may be required');
        // Wait for MFA to be completed manually or automatically
        await page.waitForTimeout(30000);
        // Check if MFA was completed
        if (await this.isMfaRequired(page)) {
            this.logger.warn('MFA still pending - waiting longer for manual completion');
            await page.waitForTimeout(60000);
        }
    }
    /**
     * Check if SSO redirect is happening
     */
    async isSsoRedirect(page) {
        const currentUrl = page.url();
        return currentUrl.includes('login.microsoftonline.com') ||
            currentUrl.includes('sts.windows.net') ||
            currentUrl.includes('federation');
    }
    /**
     * Handle SSO redirect
     */
    async handleSsoRedirect(page, timeout) {
        this.logger.info('SSO redirect detected - waiting for completion');
        // Wait for SSO to complete
        await page.waitForTimeout(10000);
    }
    /**
     * Wait for authentication to complete and redirect to the target application
     */
    async waitForAuthenticationComplete(page, timeout, expectedUrl) {
        this.logger.info('Waiting for authentication to complete and redirect');
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const currentUrl = page.url();
            // Check if we're no longer on Microsoft login pages
            if (!this.isMicrosoftLoginPage(currentUrl)) {
                if (expectedUrl) {
                    if (currentUrl.includes(expectedUrl)) {
                        this.logger.info(`Successfully redirected to expected URL: ${currentUrl}`);
                        break;
                    }
                }
                else {
                    this.logger.info(`Authentication complete, redirected to: ${currentUrl}`);
                    break;
                }
            }
            // Wait before checking again
            await page.waitForTimeout(1000);
        }
        // Final wait for page to stabilize
        await page.waitForLoadState('networkidle', { timeout: 10000 });
    }
    /**
     * Check if the current URL is a Microsoft login page
     */
    isMicrosoftLoginPage(url) {
        const microsoftDomains = [
            'login.microsoftonline.com',
            'login.microsoft.com',
            'account.microsoft.com',
            'sts.windows.net'
        ];
        return microsoftDomains.some(domain => url.includes(domain));
    }
    /**
     * Save the current session for reuse
     */
    async saveSession(page) {
        try {
            this.logger.info('Saving Microsoft session');
            const sessionDir = path.dirname(this.sessionPath);
            if (!fs.existsSync(sessionDir)) {
                fs.mkdirSync(sessionDir, { recursive: true });
            }
            const context = page.context();
            const cookies = await context.cookies();
            const localStorageData = await page.evaluate(() => JSON.stringify(window.localStorage));
            const sessionStorageData = await page.evaluate(() => JSON.stringify(window.sessionStorage));
            const sessionData = {
                cookies,
                localStorage: JSON.parse(localStorageData),
                sessionStorage: JSON.parse(sessionStorageData),
                timestamp: Date.now(),
                url: page.url()
            };
            fs.writeFileSync(this.sessionPath, JSON.stringify(sessionData, null, 2));
            this.logger.info(`Session saved to: ${this.sessionPath}`);
        }
        catch (error) {
            this.logger.warn(`Failed to save session: ${error}`);
        }
    }
    /**
     * Load a previously saved session
     */
    async loadSession(page) {
        try {
            this.logger.info('Loading Microsoft session');
            if (!fs.existsSync(this.sessionPath)) {
                throw new Error('Session file not found');
            }
            const sessionData = JSON.parse(fs.readFileSync(this.sessionPath, 'utf8'));
            const context = page.context();
            // Add cookies
            await context.addCookies(sessionData.cookies);
            // Navigate to the saved URL
            await page.goto(sessionData.url, { waitUntil: 'networkidle' });
            // Restore localStorage and sessionStorage
            await page.evaluate((data) => {
                for (const [key, value] of Object.entries(data.localStorage)) {
                    localStorage.setItem(key, value);
                }
                for (const [key, value] of Object.entries(data.sessionStorage)) {
                    sessionStorage.setItem(key, value);
                }
            }, sessionData);
            this.logger.info('Session loaded successfully');
        }
        catch (error) {
            this.logger.warn(`Failed to load session: ${error}`);
            throw error;
        }
    }
    /**
     * Check if the saved session is still valid
     */
    async isSessionValid(page) {
        try {
            if (!fs.existsSync(this.sessionPath)) {
                return false;
            }
            const sessionData = JSON.parse(fs.readFileSync(this.sessionPath, 'utf8'));
            const sessionAge = Date.now() - sessionData.timestamp;
            const maxAge = this.frameworkConfig.get('authentication.sessionExpiryMinutes', 30) * 60 * 1000;
            if (sessionAge > maxAge) {
                this.logger.info('Session expired due to age');
                return false;
            }
            // Additional validation could be added here (e.g., test a protected endpoint)
            return true;
        }
        catch (error) {
            this.logger.warn(`Session validation failed: ${error}`);
            return false;
        }
    }
    /**
     * Clear the saved session
     */
    clearSession() {
        try {
            if (fs.existsSync(this.sessionPath)) {
                fs.unlinkSync(this.sessionPath);
                this.logger.info('Session cleared');
            }
        }
        catch (error) {
            this.logger.warn(`Failed to clear session: ${error}`);
        }
    }
    /**
     * Check if user is currently authenticated
     */
    async isAuthenticated(page) {
        try {
            const currentUrl = page.url();
            // If we're on a Microsoft login page, we're not authenticated
            if (this.isMicrosoftLoginPage(currentUrl)) {
                return false;
            }
            // Additional checks could be added here
            return true;
        }
        catch (error) {
            this.logger.warn(`Authentication check failed: ${error}`);
            return false;
        }
    }
}
exports.MicrosoftAuth = MicrosoftAuth;
//# sourceMappingURL=microsoft-auth.js.map