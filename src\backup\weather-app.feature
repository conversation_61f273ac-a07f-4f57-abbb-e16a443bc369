@weather
Feature: Weather.com Application
  As a user
  I want to check weather forecasts, radar maps, and weather alerts
  So that I can plan my activities accordingly

  Background:
    Given I navigate to "https://weather.com"
    And I wait for page to load

  @smoke @search
  Scenario: Search for a location's weather
    When I wait for "#LocationSearch_input" to be visible
    And I fill "#LocationSearch_input" with "New York, NY"
    And I press "Enter"
    And I wait for ".CurrentConditions--location--1YWj_" to be visible
    Then the page should contain text "New York"
    And the page should contain text "°F"
    And the page should contain text "Feels Like"

  @today-forecast
  Scenario: View today's detailed forecast
    When I wait for "#LocationSearch_input" to be visible
    And I fill "#LocationSearch_input" with "Chicago, IL"
    And I press "Enter"
    And I wait for ".CurrentConditions--location--1YWj_" to be visible
    
    When I click on "a:contains('Today')"
    And I wait for ".TodayWeatherCard--TableWrapper--13jpa" to be visible
    Then the page should contain text "Today's Forecast"
    And the page should contain text "Morning"
    And the page should contain text "Afternoon"
    And the page should contain text "Evening"
    And the page should contain text "Overnight"
    
    When I click on "a:contains('Hourly')"
    And I wait for ".HourlyForecast--DisclosureList--3CdxR" to be visible
    Then the page should contain text "Hourly Forecast"
    And there should be more than 5 ".HourlyForecast--DisclosureList--3CdxR li" elements

  @10-day-forecast
  Scenario: Check 10-day weather forecast
    When I wait for "#LocationSearch_input" to be visible
    And I fill "#LocationSearch_input" with "Miami, FL"
    And I press "Enter"
    And I wait for ".CurrentConditions--location--1YWj_" to be visible
    
    When I click on "a:contains('10 Day')"
    And I wait for ".DailyForecast--DisclosureList--msYIJ" to be visible
    Then the page should contain text "10 Day Weather Forecast"
    And there should be 10 ".DailyForecast--DisclosureList--msYIJ li" elements
    
    When I click on ".DailyForecast--DisclosureList--msYIJ li:first-child"
    And I wait for ".DaypartDetails--Content--XQooU" to be visible
    Then the page should contain text "Morning"
    And the page should contain text "Wind"
    And the page should contain text "Humidity"

  @radar-map
  Scenario: View weather radar map
    When I wait for "#LocationSearch_input" to be visible
    And I fill "#LocationSearch_input" with "Seattle, WA"
    And I press "Enter"
    And I wait for ".CurrentConditions--location--1YWj_" to be visible
    
    When I click on "a:contains('Radar')"
    And I wait for ".MapControls--container--mDRkO" to be visible
    Then the page should contain text "Radar Map"
    
    When I click on "button:contains('Zoom In')"
    And I wait for 1000 milliseconds
    And I click on "button:contains('Zoom Out')"
    And I wait for 1000 milliseconds
    Then the ".MapCanvas--map--1yYwX" element should be visible
    
    When I click on "button:contains('Layers')"
    And I wait for ".Layers--container--3vFJz" to be visible
    Then the page should contain text "Radar"
    And the page should contain text "Satellite"

  @weather-alerts
  Scenario: Check weather alerts for a location
    When I wait for "#LocationSearch_input" to be visible
    And I fill "#LocationSearch_input" with "Orlando, FL"
    And I press "Enter"
    And I wait for ".CurrentConditions--location--1YWj_" to be visible
    
    When if ".AlertsButton--alertsButton--3QVZv" is visible, then:
      | I click on ".AlertsButton--alertsButton--3QVZv" |
      | I wait for ".AlertHeadline--alertText--3xAEd" to be visible |
      | I store the text from ".AlertHeadline--alertText--3xAEd" as "alert_text" |
    
    When if ".AlertHeadline--alertText--3xAEd" is visible, then:
      | I click on ".AlertHeadline--alertText--3xAEd" |
      | I wait for ".AlertDetails--container--2EXBn" to be visible |
      | Then the page should contain text stored in variable "alert_text" |

  @air-quality
  Scenario: Check air quality information
    When I wait for "#LocationSearch_input" to be visible
    And I fill "#LocationSearch_input" with "Los Angeles, CA"
    And I press "Enter"
    And I wait for ".CurrentConditions--location--1YWj_" to be visible
    
    When I click on "a:contains('Air Quality')"
    And I wait for ".AirQuality--AirQualityContainer--1K2AH" to be visible
    Then the page should contain text "Air Quality"
    And the page should contain text "Pollutants"
    
    When I click on "button:contains('Health & Activities')"
    And I wait for ".HealthActivities--healthActivitiesContainer--3Jjdm" to be visible
    Then the page should contain text "Health & Activities"
    And the page should contain text "Outdoor Activity"

  @pollen-count
  Scenario: Check pollen count information
    When I wait for "#LocationSearch_input" to be visible
    And I fill "#LocationSearch_input" with "Atlanta, GA"
    And I press "Enter"
    And I wait for ".CurrentConditions--location--1YWj_" to be visible
    
    When I click on "a:contains('Pollen')"
    And I wait for ".PollenBreakdown--container--3vijr" to be visible
    Then the page should contain text "Pollen Breakdown"
    And the page should contain text "Tree Pollen"
    And the page should contain text "Grass Pollen"
    And the page should contain text "Ragweed Pollen"
    
    When I click on "button:contains('Outlook')"
    And I wait for ".PollenOutlook--container--1Qe6q" to be visible
    Then the page should contain text "Pollen Outlook"

  @hurricane-center
  Scenario: Access hurricane information during hurricane season
    When I wait for "#LocationSearch_input" to be visible
    And I fill "#LocationSearch_input" with "Miami, FL"
    And I press "Enter"
    And I wait for ".CurrentConditions--location--1YWj_" to be visible
    
    When I click on "a:contains('Hurricane')"
    And I wait for page to load
    Then the page should contain text "Hurricane"
    And the page should contain text "Atlantic"
    
    When I click on "a:contains('Atlantic')"
    And I wait for ".Tracking--container--2XAjz" to be visible
    Then the page should contain text "Atlantic Basin"

  @saved-locations
  Scenario: Save and manage favorite locations
    When I wait for "#LocationSearch_input" to be visible
    And I fill "#LocationSearch_input" with "Boston, MA"
    And I press "Enter"
    And I wait for ".CurrentConditions--location--1YWj_" to be visible
    
    When I click on "button[aria-label='Favorite Location']"
    And I wait for 1000 milliseconds
    And I click on "button[aria-label='Locations']"
    And I wait for ".SavedLocations--favoritesContainer--1ezJM" to be visible
    Then the page should contain text "Boston"
    
    When I click on "button[aria-label='Remove from favorites']"
    And I wait for 1000 milliseconds
    Then the page should not contain text "Boston"
