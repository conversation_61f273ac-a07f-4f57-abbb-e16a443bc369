
@combined @spo
Feature: SPO Health Check
  Data Source - Health Check - General
  en
  Background:
    # Store credentials for later use
    Given I store "jagannath<PERSON>@kpmg.com" as "USERNAME"

  Scenario: TC0101- Login and Check the Default Data Source Order
    Given I navigate to URL "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/"
    Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000
    When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>"
    And I click on "getByRole('button', { name: 'Next' })"
    Then I wait for element "getByRole('link', { name: 'OI Development', exact: true })" to be visible with timeout 60000
    And I wait for page title to contain "OI Development - Home"
    Then I wait for element "getByRole('combobox', { name: 'Search box. Suggestions' })" to be visible with timeout 60000
    When I fill "getByRole('combobox', { name: 'Search box. Suggestions' })" with "kpmg"
    And I press "Enter"
    And I wait for page title to contain "Results"
    Then I wait for element "getByRole('link', { name: 'Organizational Logo' })" to be visible with timeout 60000
    Then I wait for "getByRole('button', { name: 'All', exact: true })" to be visible with timeout 30000
    When I click on "getByRole('button', { name: 'All', exact: true })"
    Then I wait for "getByRole('heading', { name: 'Datasources' })" to be visible with timeout 30000
    Then "getByRole('heading', { name: 'Datasources' })" should contain text "Datasources"
    And I wait for "getByLabel('Datasources').getByText('Global', { exact: true })" to be visible with timeout 30000
    And "getByLabel('Datasources').getByText('Global', { exact: true })" should contain text "Global"
    When I click on "getByLabel('Datasources').getByText('Global', { exact: true })"
    Then the URL parameter "datasource" should be "intranet"
    Then the URL parameter "origin" should be "global"
    When I click on "getByRole('button', { name: 'All', exact: true })"
    Then I wait for "getByRole('heading', { name: 'Datasources' })" to be visible with timeout 30000
    And I wait for "getByLabel('Datasources').getByText('Portal', { exact: true })" to be visible with timeout 30000
    And "getByLabel('Datasources').getByText('Portal', { exact: true })" should contain text "Portal"
    When I click on "getByLabel('Datasources').getByText('Portal', { exact: true })"
    Then the URL parameter "datasource" should be "intranet"
    Then the URL parameter "origin" should be "portal"
    When I click on "getByRole('button', { name: 'All', exact: true })"
    Then I wait for "getByRole('heading', { name: 'Datasources' })" to be visible with timeout 30000
    And I wait for "getByLabel('Datasources').getByText('Site', { exact: true })" to be visible with timeout 30000
    And "getByLabel('Datasources').getByText('Site', { exact: true })" should contain text "Site"
    When I click on "getByLabel('Datasources').getByText('Site', { exact: true })"
    Then the URL parameter "datasource" should be "intranet"
    Then the URL parameter "origin" should be "site"
    When I click on "getByRole('button', { name: 'All', exact: true })"
    Then I wait for "getByRole('heading', { name: 'Datasources' })" to be visible with timeout 30000
    And I wait for "getByLabel('Datasources').getByText('People', { exact: true })" to be visible with timeout 30000
    And "getByLabel('Datasources').getByText('People', { exact: true })" should contain text "People"
    When I click on "getByLabel('Datasources').getByText('People', { exact: true })"
    Then the URL parameter "datasource" should be "people"
    When I click on "getByRole('button', { name: 'All', exact: true })"
    Then I wait for "getByRole('heading', { name: 'Datasources' })" to be visible with timeout 30000
    And I wait for "getByLabel('Datasources').getByText('Engagements')" to be visible with timeout 30000
    And "getByLabel('Datasources').getByText('Engagements', { exact: true })" should contain text "Engagements"
    When I click on "getByLabel('Datasources').getByText('Engagements', { exact: true })"
    Then the URL parameter "datasource" should be "engagements"
    When I click on "getByRole('button', { name: 'All', exact: true })"
    Then I wait for "getByRole('heading', { name: 'Datasources' })" to be visible with timeout 30000
    And I wait for "getByLabel('Datasources').getByText('Videos')" to be visible with timeout 30000
    And "getByLabel('Datasources').getByText('Videos', { exact: true })" should contain text "Videos"
    When I click on "getByLabel('Datasources').getByText('Videos', { exact: true })"
    Then the URL parameter "datasource" should be "videos"
    When I click on "getByRole('button', { name: 'All', exact: true })"
    Then I wait for "getByRole('heading', { name: 'Datasources' })" to be visible with timeout 30000
    And I wait for "getByLabel('Datasources').getByText('Kpmg.com')" to be visible with timeout 30000
    And "getByLabel('Datasources').getByText('Kpmg.com', { exact: true })" should contain text "Kpmg.com"
    When I click on "getByLabel('Datasources').getByText('Kpmg.com', { exact: true })"
    Then the URL parameter "datasource" should be "kpmg.com"
    When I click on "getByRole('button', { name: 'All', exact: true })"
    Then I wait for "getByRole('heading', { name: 'Datasources' })" to be visible with timeout 30000
    And I wait for "getByLabel('Datasources').getByText('My.Data')" to be visible with timeout 30000
    And "getByLabel('Datasources').getByText('My.Data', { exact: true })" should contain text "My.Data"
    When I click on "getByLabel('Datasources').getByText('My.Data', { exact: true })"
    Then the URL parameter "datasource" should be "office365"
    And the URL parameter "origin" should be "files"
    When I click on "getByRole('button', { name: 'Sites', exact: true })"
    Then the URL parameter "datasource" should be "office365"
    And the URL parameter "origin" should be "sites"
    When I click on "getByRole('button', { name: 'Mails', exact: true })"
    Then the URL parameter "datasource" should be "office365"
    And the URL parameter "origin" should be "mails"
    When I click on "getByRole('button', { name: 'Calendar', exact: true })"
    Then the URL parameter "datasource" should be "office365"
    And the URL parameter "origin" should be "calendar"
    When I click on "getByRole('button', { name: 'Teams', exact: true })"
    Then the URL parameter "datasource" should be "office365"
    And the URL parameter "origin" should be "teams"
    When I click on "getByRole('button', { name: 'All', exact: true })"
    Then I wait for "getByRole('heading', { name: 'Datasources' })" to be visible with timeout 30000
    And I wait for "getByLabel('Datasources').getByText('Manuals')" to be visible with timeout 30000
    And "getByLabel('Datasources').getByText('Manuals', { exact: true })" should contain text "Manuals"
    When I click on "getByLabel('Datasources').getByText('Manuals', { exact: true })"
    Then the URL parameter "datasource" should be "mpp"
    When I click on "getByRole('button', { name: 'All', exact: true })"
    Then I wait for "getByRole('heading', { name: 'Datasources' })" to be visible with timeout 30000
    And I wait for "getByLabel('Datasources').getByText('Market Research')" to be visible with timeout 30000
    And "getByLabel('Datasources').getByText('Market Research', { exact: true })" should contain text "Market Research"
    When I click on "getByLabel('Datasources').getByText('Market Research', { exact: true })"
    Then the URL parameter "datasource" should be "marketresearch"
    When I click on "getByRole('button', { name: 'All', exact: true })"
    Then I wait for "getByRole('heading', { name: 'Datasources' })" to be visible with timeout 30000
    And I wait for "getByLabel('Datasources').getByText('News')" to be visible with timeout 30000
    And "getByLabel('Datasources').getByText('News', { exact: true })" should contain text "News"
    When I click on "getByLabel('Datasources').getByText('News', { exact: true })"
    Then the URL parameter "datasource" should be "news"
    When I click on "getByRole('button', { name: 'All', exact: true })"
    Then I wait for "getByRole('heading', { name: 'Datasources' })" to be visible with timeout 30000
    And I wait for "getByLabel('Datasources').getByText('IBFD')" to be visible with timeout 30000
    And "getByLabel('Datasources').getByText('IBFD', { exact: true })" should contain text "IBFD"
    When I click on "getByLabel('Datasources').getByText('IBFD', { exact: true })"
    Then the URL parameter "datasource" should be "ibfd"
