"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlaywrightWorld = void 0;
const test_1 = require("@playwright/test");
const logger_1 = require("../utils/logger");
/**
 * Playwright World
 *
 * A simplified world class for Playwright tests that doesn't depend on Cucumber.
 * This provides the same interface as CustomWorld but without Cucumber dependencies.
 */
class PlaywrightWorld {
    page;
    context;
    logger;
    constructor() {
        this.logger = new logger_1.Logger('PlaywrightWorld');
    }
    /**
     * Initialize the world with Playwright page and context
     */
    async init(page, context) {
        this.page = page;
        this.context = context;
        this.logger.info('🌍 Playwright world initialized');
    }
    /**
     * Cleanup resources
     */
    async cleanup() {
        this.logger.info('🧹 Cleaning up Playwright world');
        // Page and context cleanup is handled by Playwright test framework
    }
    /**
     * Get current page URL
     */
    getCurrentUrl() {
        return this.page.url();
    }
    /**
     * Get page title
     */
    async getTitle() {
        return await this.page.title();
    }
    /**
     * Take a screenshot
     */
    async takeScreenshot(name) {
        const timestamp = Date.now();
        const filename = name ? `${name}-${timestamp}.png` : `screenshot-${timestamp}.png`;
        const path = `test-results/screenshots/${filename}`;
        await this.page.screenshot({
            path,
            fullPage: true
        });
        this.logger.info(`📸 Screenshot saved: ${path}`);
        return path;
    }
    /**
     * Wait for page to load
     */
    async waitForPageLoad() {
        await this.page.waitForLoadState('networkidle');
    }
    /**
     * Navigate to URL
     */
    async navigateTo(url) {
        this.logger.info(`🌐 Navigating to: ${url}`);
        await this.page.goto(url);
        await this.waitForPageLoad();
    }
    /**
     * Get a Playwright locator from string (public method for step definitions)
     */
    getLocator(selector) {
        return this.parseLocator(selector);
    }
    /**
     * Parse Playwright locator from string
     */
    parseLocator(selector) {
        // Handle chained locators like getByLabel('Datasources').getByText('Global', { exact: true })
        // or locator('#menu-').getByText('FeaturedResult')
        if (selector.includes('.getBy') || selector.startsWith('locator(')) {
            return this.parseChainedLocator(selector);
        }
        // Check if it's a Playwright locator method
        if (selector.startsWith('getByRole(')) {
            return this.parseGetByRole(selector);
        }
        else if (selector.startsWith('getByText(')) {
            return this.parseGetByText(selector);
        }
        else if (selector.startsWith('getByLabel(')) {
            return this.parseGetByLabel(selector);
        }
        else if (selector.startsWith('getByPlaceholder(')) {
            return this.parseGetByPlaceholder(selector);
        }
        else if (selector.startsWith('getByTestId(')) {
            return this.parseGetByTestId(selector);
        }
        else {
            // Regular CSS selector
            return this.page.locator(selector);
        }
    }
    /**
     * Parse getByRole locator
     */
    parseGetByRole(selector) {
        // Extract role and options from getByRole('role', { options })
        const match = selector.match(/getByRole\('([^']+)'(?:,\s*({[^}]+}))?\)/);
        if (!match) {
            throw new Error(`Invalid getByRole syntax: ${selector}`);
        }
        const role = match[1];
        const optionsStr = match[2];
        if (optionsStr) {
            // Parse options like { name: 'text', exact: true }
            try {
                const options = eval(`(${optionsStr})`);
                return this.page.getByRole(role, options);
            }
            catch (error) {
                this.logger.warn(`Failed to parse getByRole options: ${optionsStr}, using role only`);
                return this.page.getByRole(role);
            }
        }
        else {
            return this.page.getByRole(role);
        }
    }
    /**
     * Parse getByText locator
     */
    parseGetByText(selector) {
        const match = selector.match(/getByText\('([^']+)'(?:,\s*({[^}]+}))?\)/);
        if (!match) {
            throw new Error(`Invalid getByText syntax: ${selector}`);
        }
        const text = match[1];
        const optionsStr = match[2];
        if (optionsStr) {
            try {
                const options = eval(`(${optionsStr})`);
                return this.page.getByText(text, options);
            }
            catch (error) {
                return this.page.getByText(text);
            }
        }
        else {
            return this.page.getByText(text);
        }
    }
    /**
     * Parse getByLabel locator
     */
    parseGetByLabel(selector) {
        const match = selector.match(/getByLabel\('([^']+)'(?:,\s*({[^}]+}))?\)/);
        if (!match) {
            throw new Error(`Invalid getByLabel syntax: ${selector}`);
        }
        const label = match[1];
        const optionsStr = match[2];
        if (optionsStr) {
            try {
                const options = eval(`(${optionsStr})`);
                return this.page.getByLabel(label, options);
            }
            catch (error) {
                return this.page.getByLabel(label);
            }
        }
        else {
            return this.page.getByLabel(label);
        }
    }
    /**
     * Parse getByPlaceholder locator
     */
    parseGetByPlaceholder(selector) {
        const match = selector.match(/getByPlaceholder\('([^']+)'(?:,\s*({[^}]+}))?\)/);
        if (!match) {
            throw new Error(`Invalid getByPlaceholder syntax: ${selector}`);
        }
        const placeholder = match[1];
        const optionsStr = match[2];
        if (optionsStr) {
            try {
                const options = eval(`(${optionsStr})`);
                return this.page.getByPlaceholder(placeholder, options);
            }
            catch (error) {
                return this.page.getByPlaceholder(placeholder);
            }
        }
        else {
            return this.page.getByPlaceholder(placeholder);
        }
    }
    /**
     * Parse getByTestId locator
     */
    parseGetByTestId(selector) {
        const match = selector.match(/getByTestId\('([^']+)'\)/);
        if (!match) {
            throw new Error(`Invalid getByTestId syntax: ${selector}`);
        }
        const testId = match[1];
        return this.page.getByTestId(testId);
    }
    /**
     * Parse chained locators like getByLabel('Datasources').getByText('Global', { exact: true })
     * or locator('#menu-').getByText('FeaturedResult')
     */
    parseChainedLocator(selector) {
        try {
            // Handle locator() calls with chained methods
            if (selector.startsWith('locator(')) {
                return this.parseLocatorWithChain(selector);
            }
            // Split the selector into parts for regular chained locators
            const parts = selector.split('.getBy');
            if (parts.length < 2) {
                throw new Error(`Invalid chained locator: ${selector}`);
            }
            // Parse the first part without recursion
            let locator;
            const firstPart = parts[0];
            if (firstPart.startsWith('getByRole(')) {
                locator = this.parseGetByRole(firstPart);
            }
            else if (firstPart.startsWith('getByText(')) {
                locator = this.parseGetByText(firstPart);
            }
            else if (firstPart.startsWith('getByLabel(')) {
                locator = this.parseGetByLabel(firstPart);
            }
            else if (firstPart.startsWith('getByPlaceholder(')) {
                locator = this.parseGetByPlaceholder(firstPart);
            }
            else if (firstPart.startsWith('getByTestId(')) {
                locator = this.parseGetByTestId(firstPart);
            }
            else {
                locator = this.page.locator(firstPart);
            }
            // Chain the remaining parts
            for (let i = 1; i < parts.length; i++) {
                const part = 'getBy' + parts[i];
                if (part.startsWith('getByText(')) {
                    const match = part.match(/getByText\('([^']+)'(?:,\s*({[^}]+}))?\)/);
                    if (match) {
                        const text = match[1];
                        const optionsStr = match[2];
                        if (optionsStr) {
                            try {
                                const options = eval(`(${optionsStr})`);
                                locator = locator.getByText(text, options);
                            }
                            catch {
                                locator = locator.getByText(text);
                            }
                        }
                        else {
                            locator = locator.getByText(text);
                        }
                    }
                }
                else if (part.startsWith('getByRole(')) {
                    const match = part.match(/getByRole\('([^']+)'(?:,\s*({[^}]+}))?\)/);
                    if (match) {
                        const role = match[1];
                        const optionsStr = match[2];
                        if (optionsStr) {
                            try {
                                const options = eval(`(${optionsStr})`);
                                locator = locator.getByRole(role, options);
                            }
                            catch {
                                locator = locator.getByRole(role);
                            }
                        }
                        else {
                            locator = locator.getByRole(role);
                        }
                    }
                }
                // Add more chained locator types as needed
            }
            return locator;
        }
        catch (error) {
            this.logger.warn(`Failed to parse chained locator: ${selector}, falling back to simple locator`);
            return this.page.locator(selector);
        }
    }
    /**
     * Parse locator() calls with chained methods like locator('#menu-').getByText('FeaturedResult')
     */
    parseLocatorWithChain(selector) {
        try {
            // Extract the CSS selector from locator('selector')
            const locatorMatch = selector.match(/^locator\('([^']+)'\)/);
            if (!locatorMatch) {
                throw new Error(`Invalid locator syntax: ${selector}`);
            }
            const cssSelector = locatorMatch[1];
            let locator = this.page.locator(cssSelector);
            // Check if there are chained methods
            const remainingSelector = selector.substring(locatorMatch[0].length);
            if (remainingSelector.startsWith('.getBy')) {
                // Parse chained methods
                const chainedParts = remainingSelector.split('.getBy');
                for (let i = 1; i < chainedParts.length; i++) {
                    const part = 'getBy' + chainedParts[i];
                    if (part.startsWith('getByText(')) {
                        const match = part.match(/getByText\('([^']+)'(?:,\s*({[^}]+}))?\)/);
                        if (match) {
                            const text = match[1];
                            const optionsStr = match[2];
                            if (optionsStr) {
                                try {
                                    const options = eval(`(${optionsStr})`);
                                    locator = locator.getByText(text, options);
                                }
                                catch {
                                    locator = locator.getByText(text);
                                }
                            }
                            else {
                                locator = locator.getByText(text);
                            }
                        }
                    }
                    else if (part.startsWith('getByRole(')) {
                        const match = part.match(/getByRole\('([^']+)'(?:,\s*({[^}]+}))?\)/);
                        if (match) {
                            const role = match[1];
                            const optionsStr = match[2];
                            if (optionsStr) {
                                try {
                                    const options = eval(`(${optionsStr})`);
                                    locator = locator.getByRole(role, options);
                                }
                                catch {
                                    locator = locator.getByRole(role);
                                }
                            }
                            else {
                                locator = locator.getByRole(role);
                            }
                        }
                    }
                    else if (part.startsWith('getByLabel(')) {
                        const match = part.match(/getByLabel\('([^']+)'(?:,\s*({[^}]+}))?\)/);
                        if (match) {
                            const label = match[1];
                            const optionsStr = match[2];
                            if (optionsStr) {
                                try {
                                    const options = eval(`(${optionsStr})`);
                                    locator = locator.getByLabel(label, options);
                                }
                                catch {
                                    locator = locator.getByLabel(label);
                                }
                            }
                            else {
                                locator = locator.getByLabel(label);
                            }
                        }
                    }
                    // Add more chained locator types as needed
                }
            }
            return locator;
        }
        catch (error) {
            this.logger.warn(`Failed to parse locator with chain: ${selector}, falling back to simple locator`);
            return this.page.locator(selector);
        }
    }
    /**
     * Click on element
     */
    async clickElement(selector) {
        this.logger.info(`🖱️ Clicking: ${selector}`);
        const element = this.parseLocator(selector);
        await element.click();
    }
    /**
     * Fill input field
     */
    async fillField(selector, value) {
        this.logger.info(`⌨️ Filling ${selector} with: ${value}`);
        const element = this.parseLocator(selector);
        await element.fill(value);
    }
    /**
     * Type in input field
     */
    async typeInField(selector, value) {
        this.logger.info(`⌨️ Typing ${value} in: ${selector}`);
        const element = this.parseLocator(selector);
        await element.type(value);
    }
    /**
     * Wait for element to be visible
     */
    async waitForElement(selector, timeout = 30000) {
        this.logger.info(`⏳ Waiting for element: ${selector}`);
        const element = this.parseLocator(selector);
        await element.waitFor({ state: 'visible', timeout });
    }
    /**
     * Check if element is visible
     */
    async isElementVisible(selector) {
        try {
            const element = this.parseLocator(selector);
            return await element.isVisible();
        }
        catch {
            return false;
        }
    }
    /**
     * Get element text
     */
    async getElementText(selector) {
        const element = this.parseLocator(selector);
        return await element.textContent() || '';
    }
    /**
     * Press keyboard key
     */
    async pressKey(key) {
        this.logger.info(`⌨️ Pressing key: ${key}`);
        await this.page.keyboard.press(key);
    }
    /**
     * Scroll to element
     */
    async scrollToElement(selector) {
        this.logger.info(`📜 Scrolling to: ${selector}`);
        const element = this.parseLocator(selector);
        await element.scrollIntoViewIfNeeded();
    }
    /**
     * Wait for specified time
     */
    async wait(milliseconds) {
        this.logger.info(`⏳ Waiting for ${milliseconds}ms`);
        await this.page.waitForTimeout(milliseconds);
    }
    /**
     * Verify text is present on page
     */
    async verifyTextPresent(text) {
        this.logger.info(`🔍 Verifying page contains: ${text}`);
        const bodyLocator = this.page.locator('body');
        await (0, test_1.expect)(bodyLocator).toContainText(text);
    }
    /**
     * Verify element is visible
     */
    async verifyElementVisible(selector) {
        this.logger.info(`👁️ Verifying element is visible: ${selector}`);
        const element = this.parseLocator(selector);
        await (0, test_1.expect)(element).toBeVisible();
    }
    /**
     * Verify page title contains text
     */
    async verifyTitleContains(text) {
        this.logger.info(`📄 Verifying title contains: ${text}`);
        const title = await this.getTitle();
        if (!title.includes(text)) {
            throw new Error(`Expected title to contain "${text}", but got "${title}"`);
        }
    }
    /**
     * Verify element contains text
     */
    async verifyElementContainsText(selector, text) {
        this.logger.info(`🔍 Verifying ${selector} contains: ${text}`);
        const element = this.parseLocator(selector);
        await (0, test_1.expect)(element).toContainText(text);
    }
}
exports.PlaywrightWorld = PlaywrightWorld;
//# sourceMappingURL=playwright-world.js.map