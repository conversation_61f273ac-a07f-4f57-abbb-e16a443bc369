import { Given, When, Then } from '@cucumber/cucumber';
import { CustomWorld } from './world';
import { expect } from '@playwright/test';
import { EnvConfig } from '../config/env.config';
import { Logger } from '../utils/logger';
import { retryHelper } from '../utils/retry.helper';
import { LocatorParser } from '../utils/locator-parser';
import path from 'path';
import fs from 'fs';

// Create a logger
const logger = new Logger('UniversalSteps');

/**
 * Universal pre-defined steps that can be used across any web application
 * These steps are designed to be generic and reusable
 */

// ========== NAVIGATION STEPS ==========

/**
 * Navigate to a URL
 */
Given('I navigate to {string}', { timeout: 60000 }, async function(this: CustomWorld, url: string) {
  logger.info(`Navigating to URL: ${url}`);
  await this.getPage().goto(url, { waitUntil: 'networkidle', timeout: EnvConfig.NAVIGATION_TIMEOUT });
});

/**
 * Navigate to a URL (alternative phrasing)
 */
Given('I navigate to URL {string}', { timeout: 120000 }, async function(this: CustomWorld, url: string) {
  logger.info(`Navigating to URL: ${url}`);
  await this.getPage().goto(url, { waitUntil: 'networkidle', timeout: EnvConfig.NAVIGATION_TIMEOUT });
});

/**
 * Navigate to a URL with authentication
 */
Given('I navigate to {string} with basic auth username {string} and password {string}',
  async function(this: CustomWorld, url: string, username: string, password: string) {
    logger.info(`Navigating to URL with basic auth: ${url}`);

    // Create a new context with HTTP credentials
    const context = await this.getBrowserManager().initBrowser().then(browser => browser.newContext({
      httpCredentials: {
        username,
        password
      }
    }));

    // Create a new page in the context
    const page = await context.newPage();

    // Update the page in the world object
    this.setPage(page);

    await this.getPage().goto(url, { waitUntil: 'networkidle' });
});

/**
 * Wait for page to load
 */
When('I wait for page to load', async function(this: CustomWorld) {
  logger.info('Waiting for page to load');
  await this.getPage().waitForLoadState('networkidle');
});

/**
 * Refresh the page
 */
When('I refresh the page', async function(this: CustomWorld) {
  logger.info('Refreshing the page');
  await this.getPage().reload({ waitUntil: 'networkidle' });
});

/**
 * Go back to the previous page
 */
When('I go back', async function(this: CustomWorld) {
  logger.info('Going back to the previous page');
  await this.getPage().goBack({ waitUntil: 'networkidle' });
});

/**
 * Go forward to the next page
 */
When('I go forward', async function(this: CustomWorld) {
  logger.info('Going forward to the next page');
  await this.getPage().goForward({ waitUntil: 'networkidle' });
});

// ========== WAITING STEPS ==========

/**
 * Wait for an element to be visible
 */
When('I wait for {string} to be visible', { timeout: 60000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Waiting for element to be visible: ${selector}`);

  // Use the unified getLocator method
  const locator = this.getLocator(selector);
  await locator.waitFor({ state: 'visible' });
});

/**
 * Wait for an element to be hidden
 */
When('I wait for {string} to be hidden', { timeout: 60000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Waiting for element to be hidden: ${selector}`);

  // Use the unified getLocator method
  const locator = this.getLocator(selector);
  await locator.waitFor({ state: 'hidden' });
});

/**
 * Wait for a specific amount of time
 * Using a fixed timeout of 60 seconds which should be enough for most waits
 */
When('I wait for {int} milliseconds', { timeout: 60000 }, async function(this: CustomWorld, milliseconds: number) {
  logger.info(`Waiting for ${milliseconds} milliseconds`);
  await this.getPage().waitForTimeout(milliseconds);
});

/**
 * Wait for an element with a specific selector to be visible with a custom timeout
 */
Then('I wait for element {string} to be visible with timeout {int}', { timeout: 120000 }, async function(this: CustomWorld, selector: string, timeout: number) {
  logger.info(`Waiting for element to be visible: ${selector} with timeout: ${timeout}ms`);

  // Use the unified getLocator method
  const locator = this.getLocator(selector);
  await locator.waitFor({ state: 'visible', timeout });
});

/**
 * Wait for an element to be visible with custom timeout (alternative syntax)
 */
Then('I wait for {string} to be visible with timeout {int}', { timeout: 120000 }, async function(this: CustomWorld, selector: string, timeout: number) {
  logger.info(`Waiting for element to be visible: ${selector} with timeout: ${timeout}ms`);

  // Use the unified getLocator method
  const locator = this.getLocator(selector);
  await locator.waitFor({ state: 'visible', timeout });
});

/**
 * Wait for an element to contain text
 */
When('I wait for {string} to contain text {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string, text: string) {
  logger.info(`Waiting for element to contain text: ${selector} -> ${text}`);

  // Use the unified getLocator method
  const locator = this.getLocator(selector);
  await locator.waitFor({ state: 'visible' });

  await expect(locator).toContainText(text, { timeout: EnvConfig.DEFAULT_TIMEOUT });
});

/**
 * Wait for an element to have a specific value
 */
When('I wait for {string} to have value {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string, value: string) {
  logger.info(`Waiting for element to have value: ${selector} -> ${value}`);

  // Use the unified getLocator method
  const locator = this.getLocator(selector);
  await locator.waitFor({ state: 'visible' });

  await expect(locator).toHaveValue(value, { timeout: EnvConfig.DEFAULT_TIMEOUT });
});

// ========== INTERACTION STEPS ==========

/**
 * Click on an element
 */
When('I click on {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Clicking on element: ${selector}`);

  // Use the unified getLocator method
  const locator = this.getLocator(selector);

  if (EnvConfig.SMART_INTERACTION) {
    // Use smart interaction for more robust clicking
    await retryHelper.retry(async () => {
      // Wait for element to be visible
      await locator.waitFor({ state: 'visible', timeout: 30000 });

      // Scroll element into view if needed
      await locator.scrollIntoViewIfNeeded();

      // Perform the click
      await locator.click({ timeout: 30000 });
    });
  } else {
    // Use standard Playwright click
    await locator.click({ timeout: 30000 });
  }
});

/**
 * Click on an element containing specific text
 */
When('I click on element {string} containing text {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string, text: string) {
  logger.info(`Clicking on element ${selector} containing text: ${text}`);

  // Use the unified getLocator method with hasText option
  const locator = this.getPage().locator(selector, { hasText: text });

  if (EnvConfig.SMART_INTERACTION) {
    // Use smart interaction for more robust clicking
    await retryHelper.retry(async () => {
      // Wait for element to be visible and stable
      await this.smartInteraction.waitForElementStability(locator);

      // Scroll element into view if needed
      await this.smartInteraction.scrollIntoViewIfNeeded(locator);

      // Perform the click
      await locator.click();
    });
  } else {
    // Use standard Playwright click
    await locator.click();
  }
});

/**
 * Click on a button
 */
When('I click on button {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Clicking on button: ${selector}`);

  // Use the unified getLocator method
  const locator = this.getLocator(selector);

  if (EnvConfig.SMART_INTERACTION) {
    // Use smart interaction for more robust clicking
    await this.smartInteraction.click(locator);
  } else {
    // Use standard Playwright click
    await locator.click();
  }
});

/**
 * Double click on an element
 */
When('I double click on {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Double-clicking on element: ${selector}`);

  // Use the unified getLocator method
  const locator = this.getLocator(selector);

  if (EnvConfig.SMART_INTERACTION) {
    // Use smart interaction for more robust double-clicking
    await retryHelper.retry(async () => {
      // Wait for element to be visible and stable
      await this.smartInteraction.waitForElementStability(locator);

      // Scroll element into view if needed
      await this.smartInteraction.scrollIntoViewIfNeeded(locator);

      // Perform the double-click
      await locator.dblclick();
    });
  } else {
    // Use standard Playwright double-click
    await locator.dblclick();
  }
});

/**
 * Right click on an element
 */
When('I right click on {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Right-clicking on element: ${selector}`);

  // Use the unified getLocator method
  const locator = this.getLocator(selector);

  if (EnvConfig.SMART_INTERACTION) {
    // Use smart interaction for more robust right-clicking
    await retryHelper.retry(async () => {
      // Wait for element to be visible and stable
      await this.smartInteraction.waitForElementStability(locator);

      // Scroll element into view if needed
      await this.smartInteraction.scrollIntoViewIfNeeded(locator);

      // Perform the right-click
      await locator.click({ button: 'right' });
    });
  } else {
    // Use standard Playwright right-click
    await locator.click({ button: 'right' });
  }
});

/**
 * Click on an element at a specific position
 */
When('I click on {string} at position {int},{int}', { timeout: 60000 }, async function(this: CustomWorld, selector: string, x: number, y: number) {
  logger.info(`Clicking on element at position: ${selector} -> (${x},${y})`);

  // Use the unified getLocator method
  const locator = this.getLocator(selector);

  if (EnvConfig.SMART_INTERACTION) {
    // Use smart interaction for more robust clicking
    await retryHelper.retry(async () => {
      // Wait for element to be visible and stable
      await this.smartInteraction.waitForElementStability(locator);

      // Scroll element into view if needed
      await this.smartInteraction.scrollIntoViewIfNeeded(locator);

      // Perform the click at position
      await locator.click({ position: { x, y } });
    });
  } else {
    // Use standard Playwright click
    await locator.click({ position: { x, y } });
  }
});

/**
 * Fill an input field with text
 */
When('I fill {string} with {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string, text: string) {
  logger.info(`Filling input field: ${selector} -> ${text}`);

  // Get the locator using the unified getLocator method
  const locator = this.getLocator(selector);

  if (EnvConfig.SMART_INTERACTION) {
    // Use smart interaction for more robust filling
    await this.smartInteraction.fill(locator, text);
  } else {
    // Use standard Playwright fill
    await locator.fill(text);
  }
});

/**
 * Clear an input field
 */
When('I clear {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Clearing input field: ${selector}`);

  // Use the unified getLocator method
  const locator = this.getLocator(selector);

  if (EnvConfig.SMART_INTERACTION) {
    // Use smart interaction for more robust clearing
    await retryHelper.retry(async () => {
      // Wait for element to be visible and stable
      await this.smartInteraction.waitForElementStability(locator);

      // Scroll element into view if needed
      await this.smartInteraction.scrollIntoViewIfNeeded(locator);

      // Clear the field
      await locator.clear();

      // Verify the field was cleared
      const value = await locator.inputValue();
      if (value !== '') {
        throw new Error(`Failed to clear input field: ${selector}`);
      }
    });
  } else {
    // Use standard Playwright clear
    await locator.clear();
  }
});

/**
 * Select an option from a dropdown
 */
When('I select {string} from {string}', { timeout: 60000 }, async function(this: CustomWorld, optionText: string, selector: string) {
  logger.info(`Selecting option from dropdown: ${selector} -> ${optionText}`);

  // Use the unified getLocator method
  const locator = this.getLocator(selector);

  if (EnvConfig.SMART_INTERACTION) {
    // Use smart interaction for more robust dropdown selection
    await retryHelper.retry(async () => {
      // Wait for element to be visible and stable
      await this.smartInteraction.waitForElementStability(locator);

      // Scroll element into view if needed
      await this.smartInteraction.scrollIntoViewIfNeeded(locator);

      try {
        // Try to select by label
        await locator.selectOption({ label: optionText });
      } catch (error) {
        // If selecting by label fails, try by value
        try {
          await locator.selectOption({ value: optionText });
        } catch (innerError) {
          // If that fails too, try by index if it's a number
          if (!isNaN(Number(optionText))) {
            await locator.selectOption({ index: Number(optionText) });
          } else {
            // If all attempts fail, throw the original error
            throw error;
          }
        }
      }

      // Verify the selection was made
      const selectedValue = await locator.locator('option:checked').innerText();
      if (!selectedValue) {
        throw new Error(`Failed to select option: ${optionText} from dropdown: ${selector}`);
      }
    });
  } else {
    // Use standard Playwright selectOption
    await locator.selectOption({ label: optionText });
  }
});

/**
 * Check a checkbox
 */
When('I check {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Checking checkbox: ${selector}`);

  // Use the unified getLocator method
  const locator = this.getLocator(selector);

  if (EnvConfig.SMART_INTERACTION) {
    // Use smart interaction for more robust checkbox checking
    await retryHelper.retry(async () => {
      // Wait for element to be visible and stable
      await this.smartInteraction.waitForElementStability(locator);

      // Scroll element into view if needed
      await this.smartInteraction.scrollIntoViewIfNeeded(locator);

      // Check if already checked
      const isChecked = await locator.isChecked();
      if (isChecked) {
        logger.info(`Checkbox ${selector} is already checked`);
        return;
      }

      // Check the checkbox
      await locator.check();

      // Verify the checkbox was checked
      const checkedAfter = await locator.isChecked();
      if (!checkedAfter) {
        throw new Error(`Failed to check checkbox: ${selector}`);
      }
    });
  } else {
    // Use standard Playwright check
    await locator.check();
  }
});

/**
 * Uncheck a checkbox
 */
When('I uncheck {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Unchecking checkbox: ${selector}`);

  // Use the unified getLocator method
  const locator = this.getLocator(selector);

  if (EnvConfig.SMART_INTERACTION) {
    // Use smart interaction for more robust checkbox unchecking
    await retryHelper.retry(async () => {
      // Wait for element to be visible and stable
      await this.smartInteraction.waitForElementStability(locator);

      // Scroll element into view if needed
      await this.smartInteraction.scrollIntoViewIfNeeded(locator);

      // Check if already unchecked
      const isChecked = await locator.isChecked();
      if (!isChecked) {
        logger.info(`Checkbox ${selector} is already unchecked`);
        return;
      }

      // Uncheck the checkbox
      await locator.uncheck();

      // Verify the checkbox was unchecked
      const checkedAfter = await locator.isChecked();
      if (checkedAfter) {
        throw new Error(`Failed to uncheck checkbox: ${selector}`);
      }
    });
  } else {
    // Use standard Playwright uncheck
    await locator.uncheck();
  }
});

/**
 * Hover over an element
 */
When('I hover over {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Hovering over element: ${selector}`);

  // Use the unified getLocator method
  const locator = this.getLocator(selector);

  if (EnvConfig.SMART_INTERACTION) {
    // Use smart interaction for more robust hovering
    await retryHelper.retry(async () => {
      // Wait for element to be visible and stable
      await this.smartInteraction.waitForElementStability(locator);

      // Scroll element into view if needed
      await this.smartInteraction.scrollIntoViewIfNeeded(locator);

      // Perform the hover
      await locator.hover();
    });
  } else {
    // Use standard Playwright hover
    await locator.hover();
  }
});

/**
 * Drag and drop an element
 */
When('I drag {string} and drop it on {string}', { timeout: 60000 }, async function(this: CustomWorld, sourceSelector: string, targetSelector: string) {
  logger.info(`Dragging element ${sourceSelector} to ${targetSelector}`);

  // Use the unified getLocator method
  const sourceLocator = this.getLocator(sourceSelector);
  const targetLocator = this.getLocator(targetSelector);

  if (EnvConfig.SMART_INTERACTION) {
    // Use smart interaction for more robust drag and drop
    await retryHelper.retry(async () => {
      // Wait for source element to be visible and stable
      await this.smartInteraction.waitForElementStability(sourceLocator);

      // Wait for target element to be visible and stable
      await this.smartInteraction.waitForElementStability(targetLocator);

      // Scroll source element into view if needed
      await this.smartInteraction.scrollIntoViewIfNeeded(sourceLocator);

      try {
        // Try using Playwright's dragTo method
        await sourceLocator.dragTo(targetLocator);
      } catch (error) {
        // If the built-in method fails, try a manual approach
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.warn(`Built-in drag and drop failed, trying manual approach: ${errorMessage}`);

        // Get the source and target element positions
        const sourceBoundingBox = await sourceLocator.boundingBox();
        const targetBoundingBox = await targetLocator.boundingBox();

        if (!sourceBoundingBox || !targetBoundingBox) {
          throw new Error('Could not get element positions for drag and drop');
        }

        // Calculate source and target centers
        const sourceX = sourceBoundingBox.x + sourceBoundingBox.width / 2;
        const sourceY = sourceBoundingBox.y + sourceBoundingBox.height / 2;
        const targetX = targetBoundingBox.x + targetBoundingBox.width / 2;
        const targetY = targetBoundingBox.y + targetBoundingBox.height / 2;

        // Perform manual drag and drop
        await this.getPage().mouse.move(sourceX, sourceY);
        await this.getPage().mouse.down();
        await this.getPage().mouse.move(targetX, targetY, { steps: 10 }); // Move in steps for smoother drag
        await this.getPage().mouse.up();
      }
    });
  } else {
    // Use standard Playwright dragTo
    await sourceLocator.dragTo(targetLocator);
  }
});

/**
 * Move mouse to a specific position
 */
When('I move mouse to position {int},{int}', async function(this: CustomWorld, x: number, y: number) {
  logger.info(`Moving mouse to position: (${x},${y})`);
  await this.getPage().mouse.move(x, y);
});

/**
 * Press a keyboard key
 */
When('I press {string}', { timeout: 60000 }, async function(this: CustomWorld, key: string) {
  logger.info(`Pressing key: ${key}`);

  // Handle special keys like "Enter", "Tab", "Escape", etc.
  const specialKeys = ["Enter", "Tab", "Escape", "ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"];

  if (specialKeys.includes(key)) {
    await this.getPage().keyboard.press(key);
  } else {
    // For other keys, just type them
    await this.getPage().keyboard.type(key);
  }
});

/**
 * Type text without focusing on a specific element
 */
When('I type {string}', async function(this: CustomWorld, text: string) {
  logger.info(`Typing text: ${text}`);
  await this.getPage().keyboard.type(text);
});

/**
 * Press a combination of keys
 */
When('I press keyboard combination {string}', async function(this: CustomWorld, keys: string) {
  logger.info(`Pressing keyboard combination: ${keys}`);

  // Split the combination by '+' and press all keys together
  const keyArray = keys.split('+');
  for (let i = 0; i < keyArray.length - 1; i++) {
    await this.getPage().keyboard.down(keyArray[i].trim());
  }

  // Press the last key
  await this.getPage().keyboard.press(keyArray[keyArray.length - 1].trim());

  // Release all keys except the last one
  for (let i = 0; i < keyArray.length - 1; i++) {
    await this.getPage().keyboard.up(keyArray[i].trim());
  }
});

// ========== VALIDATION STEPS ==========

/**
 * Verify an element is visible
 */
Then('{string} should be visible', { timeout: 30000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Verifying element is visible: ${selector}`);
  // Use the unified getLocator method
  const element = this.getLocator(selector);
  await expect(element).toBeVisible();
});

/**
 * Verify an element is visible (alternative phrasing)
 */
Then('element {string} should be visible', { timeout: 30000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Verifying element is visible: ${selector}`);
  // Use the unified getLocator method
  const element = this.getLocator(selector);
  await expect(element).toBeVisible();
});

/**
 * Verify an element is not visible
 */
Then('{string} should not be visible', { timeout: 30000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Verifying element is not visible: ${selector}`);
  // Use the unified getLocator method
  const element = this.getLocator(selector);
  await expect(element).not.toBeVisible();
});

/**
 * Verify an element contains specific text
 */
Then('{string} should contain text {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string, text: string) {
  logger.info(`Verifying element contains text: ${selector} -> ${text}`);

  // Use the unified getLocator method
  const element = this.getLocator(selector);

  await expect(element).toContainText(text, { timeout: EnvConfig.DEFAULT_TIMEOUT });
});

/**
 * Verify an element has exact text
 */
Then('{string} should have exact text {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string, text: string) {
  logger.info(`Verifying element has exact text: ${selector} -> ${text}`);

  // Use the unified getLocator method
  const element = this.getLocator(selector);

  await expect(element).toHaveText(text, { timeout: EnvConfig.DEFAULT_TIMEOUT });
});

/**
 * Verify an element has exact text (alternative phrasing with "the" and "element")
 */
Then('the {string} element should have exact text {string}', { timeout: 60000 }, async function(this: CustomWorld, selector: string, text: string) {
  logger.info(`Verifying element has exact text: ${selector} -> ${text}`);

  // Use the unified getLocator method
  const element = this.getLocator(selector);

  await expect(element).toHaveText(text, { timeout: EnvConfig.DEFAULT_TIMEOUT });
});

/**
 * Verify an element is enabled
 */
Then('{string} should be enabled', { timeout: 30000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Verifying element is enabled: ${selector}`);
  // Use the unified getLocator method
  const element = this.getLocator(selector);
  await expect(element).toBeEnabled();
});

/**
 * Verify an element is disabled
 */
Then('{string} should be disabled', { timeout: 30000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Verifying element is disabled: ${selector}`);
  // Use the unified getLocator method
  const element = this.getLocator(selector);
  await expect(element).toBeDisabled();
});

/**
 * Verify the URL contains specific text
 */
Then('the URL should contain {string}', async function(this: CustomWorld, urlPart: string) {
  logger.info(`Verifying URL contains: ${urlPart}`);
  await expect(this.getPage()).toHaveURL(new RegExp(urlPart));
});

/**
 * Verify the URL includes specific text (alternative phrasing)
 */
Then('the URL should include {string}', async function(this: CustomWorld, urlPart: string) {
  logger.info(`Verifying URL includes: ${urlPart}`);
  await expect(this.getPage()).toHaveURL(new RegExp(urlPart));
});

/**
 * Verify the page title contains specific text
 */
Then('the page title should contain {string}', { timeout: 30000 }, async function(this: CustomWorld, titlePart: string) {
  logger.info(`Verifying page title contains: ${titlePart}`);
  await expect(this.getPage()).toHaveTitle(new RegExp(titlePart), { timeout: 30000 });
});

/**
 * Verify an element has a specific attribute value
 */
Then('{string} should have attribute {string} with value {string}', { timeout: 30000 }, async function(this: CustomWorld, selector: string, attribute: string, value: string) {
  logger.info(`Verifying element has attribute: ${selector} -> ${attribute}=${value}`);
  // Use the unified getLocator method
  const element = this.getLocator(selector);
  const attributeValue = await element.getAttribute(attribute);
  expect(attributeValue).toBe(value);
});

/**
 * Verify an element exists in the DOM
 */
Then('{string} should exist', { timeout: 30000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Verifying element exists: ${selector}`);
  // Use the unified getLocator method
  const element = this.getLocator(selector);
  await expect(element).toHaveCount(1);
});

/**
 * Verify an element does not exist in the DOM
 */
Then('{string} should not exist', { timeout: 30000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Verifying element does not exist: ${selector}`);
  // Use the unified getLocator method
  const element = this.getLocator(selector);
  await expect(element).toHaveCount(0);
});

/**
 * Verify the count of elements matching a selector
 */
Then('{string} should have count {int}', { timeout: 30000 }, async function(this: CustomWorld, selector: string, count: number) {
  logger.info(`Verifying element count: ${selector} -> ${count}`);
  // Use the unified getLocator method
  const elements = this.getLocator(selector);
  await expect(elements).toHaveCount(count);
});

/**
 * Verify an input field has a specific value
 */
Then('{string} should have value {string}', { timeout: 30000 }, async function(this: CustomWorld, selector: string, value: string) {
  logger.info(`Verifying input field has value: ${selector} -> ${value}`);
  // Use the unified getLocator method
  const element = this.getLocator(selector);
  await expect(element).toHaveValue(value);
});

/**
 * Verify a checkbox is checked
 */
Then('{string} should be checked', { timeout: 30000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Verifying checkbox is checked: ${selector}`);
  // Use the unified getLocator method
  const element = this.getLocator(selector);
  await expect(element).toBeChecked();
});

/**
 * Verify a checkbox is not checked
 */
Then('{string} should not be checked', { timeout: 30000 }, async function(this: CustomWorld, selector: string) {
  logger.info(`Verifying checkbox is not checked: ${selector}`);
  // Use the unified getLocator method
  const element = this.getLocator(selector);
  await expect(element).not.toBeChecked();
});

// ========== FILE UPLOAD STEPS ==========

/**
 * Upload a file to a file input
 */
When('I upload file {string} to {string}', async function(this: CustomWorld, filePath: string, selector: string) {
  logger.info(`Uploading file: ${filePath} to ${selector}`);

  // Resolve the file path relative to the test data directory
  const testDataDir = EnvConfig.TEST_DATA_DIR || 'src/test-data';
  const absoluteFilePath = path.resolve(process.cwd(), testDataDir, filePath);

  // Check if the file exists
  if (!fs.existsSync(absoluteFilePath)) {
    throw new Error(`File not found: ${absoluteFilePath}`);
  }

  if (EnvConfig.SMART_INTERACTION) {
    // Use smart interaction for more robust file upload
    await retryHelper.retry(async () => {
      try {
        // Wait for element to be visible and stable
        await this.smartInteraction.waitForElementStability(selector);

        // Scroll element into view if needed
        await this.smartInteraction.scrollIntoViewIfNeeded(selector);

        // Upload the file
        await this.getPage().setInputFiles(selector, absoluteFilePath);

        // Verify the file was uploaded by checking the input value
        const fileName = path.basename(absoluteFilePath);
        const inputValue = await this.getPage().locator(selector).inputValue();

        // Some browsers only show the file name, others show the full path
        // We'll check if the file name is included in the input value
        if (!inputValue.includes(fileName) && !inputValue.includes(path.basename(fileName))) {
          throw new Error(`File upload verification failed. Expected file name ${fileName} not found in input value: ${inputValue}`);
        }
      } catch (error) {
        // If standard upload fails, try using JavaScript
        if (error instanceof Error && error.message.includes('Element is not an <input>')) {
          logger.warn('Standard file upload failed, trying JavaScript approach');

          // Create a temporary input element
          await this.getPage().evaluate(() => {
            const input = document.createElement('input');
            input.type = 'file';
            input.id = 'temp-file-input';
            input.style.position = 'fixed';
            input.style.top = '0';
            input.style.left = '0';
            input.style.opacity = '0';
            document.body.appendChild(input);
          });

          // Upload the file to the temporary input
          await this.getPage().setInputFiles('#temp-file-input', absoluteFilePath);

          // Get the file from the temporary input and set it to the target input
          await this.getPage().evaluate((selector) => {
            const tempInput = document.getElementById('temp-file-input') as HTMLInputElement;
            const targetInput = document.querySelector(selector) as HTMLInputElement;

            if (tempInput && tempInput.files && tempInput.files.length > 0 && targetInput) {
              // Create a DataTransfer object and add the file
              const dataTransfer = new DataTransfer();
              dataTransfer.items.add(tempInput.files[0]);

              // Set the files property of the target input
              targetInput.files = dataTransfer.files;

              // Dispatch change event
              targetInput.dispatchEvent(new Event('change', { bubbles: true }));

              // Remove the temporary input
              tempInput.remove();
            }
          }, selector);
        } else {
          throw error;
        }
      }
    });
  } else {
    // Use standard Playwright setInputFiles
    await this.getPage().setInputFiles(selector, absoluteFilePath);
  }
});

// ========== ALERT HANDLING STEPS ==========

/**
 * Accept an alert dialog
 */
When('I accept the alert', async function(this: CustomWorld) {
  logger.info('Accepting alert dialog');
  this.getPage().on('dialog', dialog => dialog.accept());
});

/**
 * Dismiss an alert dialog
 */
When('I dismiss the alert', async function(this: CustomWorld) {
  logger.info('Dismissing alert dialog');
  this.getPage().on('dialog', dialog => dialog.dismiss());
});

/**
 * Fill a prompt dialog with text
 */
When('I fill alert prompt with {string}', async function(this: CustomWorld, text: string) {
  logger.info(`Filling alert prompt with: ${text}`);
  this.getPage().on('dialog', dialog => dialog.accept(text));
});

// ========== DATA-DRIVEN STEPS ==========

/**
 * Load test data from a JSON file
 */
Given('I have loaded user credentials from JSON file {string}', async function(this: CustomWorld, filename: string) {
  logger.info(`Loading user credentials from JSON file: ${filename}`);

  // Resolve the file path relative to the test data directory
  const testDataDir = EnvConfig.TEST_DATA_DIR || 'src/test-data';
  const filePath = path.resolve(process.cwd(), testDataDir, filename);

  // Check if the file exists
  if (!fs.existsSync(filePath)) {
    throw new Error(`JSON file not found: ${filePath}`);
  }

  // Load and parse the JSON file
  const fileContent = fs.readFileSync(filePath, 'utf8');
  const userData = JSON.parse(fileContent);

  // Store the data in the world object for later use
  this.testData = userData;

  logger.info(`Loaded ${Object.keys(userData).length} user credentials from ${filename}`);
});

/**
 * Load test data from a CSV file
 */
Given('I have loaded user credentials from CSV file {string}', async function(this: CustomWorld, filename: string) {
  logger.info(`Loading user credentials from CSV file: ${filename}`);

  // Resolve the file path relative to the test data directory
  const testDataDir = EnvConfig.TEST_DATA_DIR || 'src/test-data';
  const filePath = path.resolve(process.cwd(), testDataDir, filename);

  // Check if the file exists
  if (!fs.existsSync(filePath)) {
    throw new Error(`CSV file not found: ${filePath}`);
  }

  // Load and parse the CSV file
  const fileContent = fs.readFileSync(filePath, 'utf8');
  const lines = fileContent.split('\n').filter(line => line.trim() !== '');

  if (lines.length < 2) {
    throw new Error('CSV file must contain at least a header row and one data row');
  }

  // Parse header row
  const headers = lines[0].split(',').map(header => header.trim());

  // Parse data rows
  const userData: Record<string, any>[] = [];
  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map(value => value.trim());
    const user: Record<string, any> = {};

    for (let j = 0; j < headers.length; j++) {
      user[headers[j]] = values[j] || '';
    }

    userData.push(user);
  }

  // Store the data in the world object for later use
  this.testData = userData;

  logger.info(`Loaded ${userData.length} user credentials from ${filename}`);
});

/**
 * Generate random user credentials
 */
Given('I have generated random user credentials', async function(this: CustomWorld) {
  logger.info('Generating random user credentials');

  // Generate a random username and password
  const randomUsername = `user_${Math.floor(Math.random() * 10000)}`;
  const randomPassword = `pass_${Math.floor(Math.random() * 10000)}`;

  // Store the generated credentials in the world object
  this.testData = {
    username: randomUsername,
    password: randomPassword
  } as Record<string, any>;

  logger.info(`Generated random username: ${randomUsername} and password: ${randomPassword}`);
});

/**
 * Fill a field with the generated username
 */
When('I fill {string} with the generated username', async function(this: CustomWorld, selector: string) {
  if (!this.testData) {
    throw new Error('No test data found. Please use "I have generated random user credentials" step first.');
  }

  // Handle both Map and Record types
  let username: string;
  if (this.testData instanceof Map) {
    username = this.testData.get('username');
  } else {
    username = (this.testData as Record<string, any>).username;
  }

  if (!username) {
    throw new Error('No generated username found. Please use "I have generated random user credentials" step first.');
  }

  logger.info(`Filling ${selector} with generated username: ${username}`);
  await this.getPage().fill(selector, username);
});

/**
 * Fill a field with the generated password
 */
When('I fill {string} with the generated password', async function(this: CustomWorld, selector: string) {
  if (!this.testData) {
    throw new Error('No test data found. Please use "I have generated random user credentials" step first.');
  }

  // Handle both Map and Record types
  let password: string;
  if (this.testData instanceof Map) {
    password = this.testData.get('password');
  } else {
    password = (this.testData as Record<string, any>).password;
  }

  if (!password) {
    throw new Error('No generated password found. Please use "I have generated random user credentials" step first.');
  }

  logger.info(`Filling ${selector} with generated password: ${password}`);
  await this.getPage().fill(selector, password);
});

// ========== SCREENSHOT STEPS ==========

/**
 * Take a screenshot of the entire page
 */
When('I take a screenshot', async function(this: CustomWorld) {
  logger.info('Taking a screenshot of the entire page');

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const screenshotDir = EnvConfig.SCREENSHOT_DIR || './test-results/screenshots';

  // Create the directory if it doesn't exist
  if (!fs.existsSync(screenshotDir)) {
    fs.mkdirSync(screenshotDir, { recursive: true });
  }

  const screenshotPath = path.join(screenshotDir, `screenshot-${timestamp}.png`);
  await this.getPage().screenshot({ path: screenshotPath, fullPage: true });

  logger.info(`Screenshot saved to: ${screenshotPath}`);
});

/**
 * Take a screenshot of a specific element
 */
When('I take a screenshot of {string}', async function(this: CustomWorld, selector: string) {
  logger.info(`Taking a screenshot of element: ${selector}`);

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const screenshotDir = EnvConfig.SCREENSHOT_DIR || './test-results/screenshots';

  // Create the directory if it doesn't exist
  if (!fs.existsSync(screenshotDir)) {
    fs.mkdirSync(screenshotDir, { recursive: true });
  }

  const element = this.getPage().locator(selector);
  const screenshotPath = path.join(screenshotDir, `screenshot-${selector.replace(/[^a-zA-Z0-9]/g, '-')}-${timestamp}.png`);
  await element.screenshot({ path: screenshotPath });

  logger.info(`Screenshot saved to: ${screenshotPath}`);
});

/**
 * Take a screenshot with a custom name
 */
When('I take a screenshot named {string}', async function(this: CustomWorld, name: string) {
  logger.info(`Taking a screenshot named: ${name}`);

  const screenshotDir = EnvConfig.SCREENSHOT_DIR || './test-results/screenshots';

  // Create the directory if it doesn't exist
  if (!fs.existsSync(screenshotDir)) {
    fs.mkdirSync(screenshotDir, { recursive: true });
  }

  const screenshotPath = path.join(screenshotDir, `${name.replace(/[^a-zA-Z0-9]/g, '-')}.png`);
  await this.getPage().screenshot({ path: screenshotPath, fullPage: true });

  logger.info(`Screenshot saved to: ${screenshotPath}`);
});

// ========== BROWSER CONTROL STEPS ==========

/**
 * Set the viewport size
 */
When('I set viewport size to {int}x{int}', async function(this: CustomWorld, width: number, height: number) {
  logger.info(`Setting viewport size to: ${width}x${height}`);
  await this.getPage().setViewportSize({ width, height });
});

/**
 * Emulate a specific device
 */
When('I emulate device {string}', async function(this: CustomWorld, deviceName: string) {
  logger.info(`Emulating device: ${deviceName}`);

  // Get the device descriptor from Playwright's device descriptors
  const { devices } = require('@playwright/test');
  const device = devices[deviceName];

  if (!device) {
    throw new Error(`Device "${deviceName}" not found in Playwright's device descriptors`);
  }

  // Apply the device emulation by setting viewport and user agent
  await this.getPage().setViewportSize({
    width: device.viewport.width,
    height: device.viewport.height
  });

  // Set user agent
  await this.getBrowserContext().addInitScript(() => {
    Object.defineProperty(navigator, 'userAgent', {
      get: () => device.userAgent
    });
  });
});

/**
 * Switch to a specific browser tab
 */
When('I switch to tab {int}', async function(this: CustomWorld, index: number) {
  logger.info(`Switching to tab: ${index}`);

  // getBrowserContext().pages() returns a Promise<Page[]>
  const pages = await Promise.resolve(this.getBrowserContext().pages());

  if (index < 1 || index > pages.length) {
    throw new Error(`Tab index ${index} is out of range. Available tabs: ${pages.length}`);
  }

  // Switch to the specified tab (index is 1-based in the step, but 0-based in the array)
  const page = pages[index - 1];
  await page.bringToFront();

  // Update the page in the world object
  this.setPage(page);
});

/**
 * Close the current browser tab
 */
When('I close current tab', async function(this: CustomWorld) {
  logger.info('Closing current tab');

  // getBrowserContext().pages() returns a Promise<Page[]>
  const pages = await Promise.resolve(this.getBrowserContext().pages());

  if (pages.length <= 1) {
    throw new Error('Cannot close the last tab');
  }

  // Get the current page
  const currentPage = this.getPage();

  // Switch to another page before closing the current one
  const otherPage = pages.find(page => page !== currentPage);

  if (!otherPage) {
    throw new Error('Could not find another tab to switch to');
  }

  await otherPage.bringToFront();

  // Close the current page
  await currentPage.close();

  // Update the page in the world object
  this.setPage(otherPage);
});

/**
 * Open a new browser tab
 */
When('I open new tab', async function(this: CustomWorld) {
  logger.info('Opening new tab');

  // Create a new page in the same browser context
  const newPage = await this.getBrowserContext().newPage();

  // Switch to the new page
  await newPage.bringToFront();

  // Update the page in the world object
  this.setPage(newPage);
});

// ========== ACCESSIBILITY STEPS ==========

/**
 * Test the entire page for accessibility issues
 */
Then('the page should be accessible', async function(this: CustomWorld) {
  logger.info('Testing page for accessibility issues');

  // Note: This is a placeholder for accessibility testing
  // In a real implementation, you would use a library like axe-playwright
  // npm install axe-playwright

  // For demonstration purposes, we'll just log the request
  logger.info('Accessibility test for the entire page would be performed here');
  logger.info('To implement this step, install axe-playwright and use it to run the test');

  // Example implementation with axe-playwright would be:
  // const { AxeBuilder } = require('@axe-core/playwright');
  // const results = await new AxeBuilder({ page: this.getPage() }).analyze();
  // expect(results.violations.length).toBe(0);
});

/**
 * Test a specific element for accessibility issues
 */
Then('the element {string} should be accessible', async function(this: CustomWorld, selector: string) {
  logger.info(`Testing element for accessibility issues: ${selector}`);

  // Get the element
  const element = this.getPage().locator(selector);

  // Wait for the element to be visible
  await element.waitFor({ state: 'visible' });

  // Note: This is a placeholder for accessibility testing
  // In a real implementation, you would use a library like axe-playwright
  // npm install axe-playwright

  // For demonstration purposes, we'll just log the request
  logger.info(`Accessibility test for element ${selector} would be performed here`);
  logger.info('To implement this step, install axe-playwright and use it to run the test');

  // Example implementation with axe-playwright would be:
  // const { AxeBuilder } = require('@axe-core/playwright');
  // const results = await new AxeBuilder({ page: this.getPage() })
  //   .include(selector)
  //   .analyze();
  // expect(results.violations.length).toBe(0);
});

/**
 * Test for accessibility issues with a specific impact level
 */
Then('the page should have no accessibility violations with impact {string}', async function(this: CustomWorld, impact: string) {
  logger.info(`Testing page for accessibility violations with impact: ${impact}`);

  // This step requires the axe-playwright library to be installed
  // npm install axe-playwright

  // For demonstration purposes, we'll just log the request
  // In a real implementation, you would use axe-playwright to run the test
  logger.info(`Accessibility test for impact level ${impact} would be performed here`);
  logger.info('To implement this step, install axe-playwright and use it to run the test');
});

/**
 * Test for accessibility issues related to a specific rule
 */
Then('the page should have no accessibility violations for rule {string}', async function(this: CustomWorld, ruleId: string) {
  logger.info(`Testing page for accessibility violations for rule: ${ruleId}`);

  // This step requires the axe-playwright library to be installed
  // npm install axe-playwright

  // For demonstration purposes, we'll just log the request
  // In a real implementation, you would use axe-playwright to run the test
  logger.info(`Accessibility test for rule ${ruleId} would be performed here`);
  logger.info('To implement this step, install axe-playwright and use it to run the test');
});

/**
 * Test for accessibility issues related to multiple rules
 */
Then('the page should have no accessibility violations for rules:', async function(this: CustomWorld, dataTable) {
  logger.info('Testing page for accessibility violations for multiple rules');

  // Get the rules from the data table
  const rules = dataTable.raw().map((row: string[]) => row[0]);

  // This step requires the axe-playwright library to be installed
  // npm install axe-playwright

  // For demonstration purposes, we'll just log the request
  // In a real implementation, you would use axe-playwright to run the test
  logger.info(`Accessibility test for rules would be performed here: ${rules.join(', ')}`);
  logger.info('To implement this step, install axe-playwright and use it to run the test');

  // Example implementation with axe-playwright would be:
  // const { AxeBuilder } = require('@axe-core/playwright');
  // const results = await new AxeBuilder({ page: this.getPage() })
  //   .withRules(rules)
  //   .analyze();
  // expect(results.violations.length).toBe(0);
});

// ========== API TESTING STEPS ==========

/**
 * Send a GET request to an API endpoint
 */
When('I send a GET request to {string}', async function(this: CustomWorld, url: string) {
  logger.info(`Sending GET request to: ${url}`);

  // Send the request using Playwright's API request context
  const apiContext = this.getBrowserContext().request;
  const response = await apiContext.get(url);

  // Store the response in the test data for later use
  this.storeData('apiResponse', response);
  this.storeData('apiResponseBody', await response.json());
  this.storeData('apiResponseStatus', response.status());

  logger.info(`Received response with status: ${response.status()}`);
});

/**
 * Send a POST request to an API endpoint with a JSON body
 */
When('I send a POST request to {string} with body:', async function(this: CustomWorld, url: string, docString: string) {
  logger.info(`Sending POST request to: ${url}`);

  // Parse the JSON body
  const body = JSON.parse(docString);

  // Send the request using Playwright's API request context
  const apiContext = this.getBrowserContext().request;
  const response = await apiContext.post(url, { data: body });

  // Store the response in the test data for later use
  this.storeData('apiResponse', response);
  this.storeData('apiResponseBody', await response.json());
  this.storeData('apiResponseStatus', response.status());

  logger.info(`Received response with status: ${response.status()}`);
});

/**
 * Verify the API response status code
 */
Then('the API response status should be {int}', async function(this: CustomWorld, status: number) {
  logger.info(`Verifying API response status: ${status}`);

  const responseStatus = this.getData('apiResponseStatus');
  expect(responseStatus).toBe(status);
});

/**
 * Verify the API response contains a specific field
 */
Then('the API response should contain field {string}', async function(this: CustomWorld, field: string) {
  logger.info(`Verifying API response contains field: ${field}`);

  const responseBody = this.getData('apiResponseBody');

  // Split the field path by dots to handle nested objects
  const fieldPath = field.split('.');
  let value = responseBody;

  // Traverse the object to find the field
  for (const key of fieldPath) {
    value = value[key];
    if (value === undefined) {
      break;
    }
  }

  expect(value).not.toBeUndefined();
});

/**
 * Verify the API response field has a specific value
 */
Then('the API response field {string} should be {string}', async function(this: CustomWorld, field: string, expectedValue: string) {
  logger.info(`Verifying API response field ${field} has value: ${expectedValue}`);

  const responseBody = this.getData('apiResponseBody');

  // Split the field path by dots to handle nested objects
  const fieldPath = field.split('.');
  let value = responseBody;

  // Traverse the object to find the field
  for (const key of fieldPath) {
    value = value[key];
    if (value === undefined) {
      break;
    }
  }

  // Convert to string for comparison
  const actualValue = String(value);
  expect(actualValue).toBe(expectedValue);
});

// ========== DATA STORAGE AND RETRIEVAL STEPS ==========

/**
 * Store a value directly
 */
Given('I store {string} as {string}', async function(this: CustomWorld, value: string, key: string) {
  logger.info(`Storing value ${value} as ${key}`);
  this.storeData(key, value);
  logger.info(`Stored value: ${value}`);
});

/**
 * Store a value for later use
 */
When('I store the text of {string} as {string}', async function(this: CustomWorld, selector: string, key: string) {
  logger.info(`Storing text of ${selector} as ${key}`);

  const element = this.getPage().locator(selector);
  const text = await element.innerText();

  this.storeData(key, text);

  logger.info(`Stored value: ${text}`);
});

/**
 * Store a value from an attribute for later use
 */
When('I store the attribute {string} of {string} as {string}', async function(this: CustomWorld, attribute: string, selector: string, key: string) {
  logger.info(`Storing attribute ${attribute} of ${selector} as ${key}`);

  const element = this.getPage().locator(selector);
  const value = await element.getAttribute(attribute);

  this.storeData(key, value);

  logger.info(`Stored value: ${value}`);
});

/**
 * Use a stored value in a step
 */
When('I fill {string} with stored value {string}', async function(this: CustomWorld, selector: string, key: string) {
  const value = this.getData(key);

  if (!value) {
    throw new Error(`No stored value found for key: ${key}`);
  }

  logger.info(`Filling ${selector} with stored value: ${value}`);
  await this.getPage().fill(selector, value);
});

/**
 * Verify a stored value
 */
Then('the stored value {string} should be {string}', async function(this: CustomWorld, key: string, expectedValue: string) {
  const value = this.getData(key);

  if (!value) {
    throw new Error(`No stored value found for key: ${key}`);
  }

  logger.info(`Verifying stored value ${key}: ${value} equals ${expectedValue}`);
  expect(value).toBe(expectedValue);
});

// ========== URL VALIDATION STEPS ==========

/**
 * Verify the current URL
 */
Then('the URL should be {string}', async function(this: CustomWorld, expectedUrl: string) {
  logger.info(`Verifying URL is: ${expectedUrl}`);

  const currentUrl = this.getPage().url();
  expect(currentUrl).toBe(expectedUrl);
});

/**
 * Verify the URL path
 */
Then('the URL path should be {string}', async function(this: CustomWorld, expectedPath: string) {
  logger.info(`Verifying URL path is: ${expectedPath}`);

  const url = new URL(this.getPage().url());
  expect(url.pathname).toBe(expectedPath);
});

/**
 * Verify a URL parameter
 */
Then('the URL parameter {string} should be {string}', async function(this: CustomWorld, param: string, expectedValue: string) {
  logger.info(`Verifying URL parameter ${param} is: ${expectedValue}`);

  const url = new URL(this.getPage().url());
  const value = url.searchParams.get(param);
  expect(value).toBe(expectedValue);
});

// ========== CONDITIONAL EXECUTION STEPS ==========

/**
 * Conditionally execute a step
 */
When('I {string} click on {string} based on condition {string}', async function(this: CustomWorld, action: string, selector: string, condition: string) {
  logger.info(`Conditional action: ${action} on ${selector} based on ${condition}`);

  if (action === 'do' && condition === 'yes') {
    logger.info(`Clicking on ${selector}`);
    await this.getPage().click(selector);
  } else {
    logger.info(`Skipping click on ${selector}`);
  }
});

/**
 * Conditionally verify an element
 */
Then('I {string} expect {string} to be visible based on condition {string}', async function(this: CustomWorld, expectation: string, selector: string, condition: string) {
  logger.info(`Conditional verification: ${expectation} expect ${selector} to be visible based on ${condition}`);

  const element = this.getPage().locator(selector);

  if (expectation === 'do' && condition === 'yes') {
    logger.info(`Verifying ${selector} is visible`);
    await expect(element).toBeVisible();
  } else if (expectation === 'do not' && condition === 'yes') {
    logger.info(`Verifying ${selector} is not visible`);
    await expect(element).not.toBeVisible();
  }
});

// ========== SCROLLING AND JAVASCRIPT EXECUTION STEPS ==========

/**
 * Scroll to an element
 */
When('I scroll to {string}', async function(this: CustomWorld, selector: string) {
  logger.info(`Scrolling to element: ${selector}`);

  const element = this.getPage().locator(selector);
  await element.scrollIntoViewIfNeeded();
});

/**
 * Scroll to the top of the page
 */
When('I scroll to the top of the page', async function(this: CustomWorld) {
  logger.info('Scrolling to the top of the page');

  await this.getPage().evaluate(() => {
    window.scrollTo(0, 0);
  });
});

/**
 * Scroll to the bottom of the page
 */
When('I scroll to the bottom of the page', async function(this: CustomWorld) {
  logger.info('Scrolling to the bottom of the page');

  await this.getPage().evaluate(() => {
    window.scrollTo(0, document.body.scrollHeight);
  });
});

/**
 * Execute JavaScript
 */
When('I execute JavaScript {string}', async function(this: CustomWorld, script: string) {
  logger.info(`Executing JavaScript: ${script}`);

  await this.getPage().evaluate((js) => {
    return eval(js);
  }, script);
});

/**
 * Execute JavaScript with a return value
 */
When('I store the result of JavaScript {string} as {string}', async function(this: CustomWorld, script: string, key: string) {
  logger.info(`Executing JavaScript and storing result: ${script}`);

  const result = await this.getPage().evaluate((js) => {
    return eval(js);
  }, script);

  this.storeData(key, result);

  logger.info(`Stored JavaScript result: ${result}`);
});

// ========== TABLE DATA HANDLING STEPS ==========

/**
 * Verify a table contains specific rows
 */
Then('the table {string} should contain:', async function(this: CustomWorld, selector: string, dataTable) {
  logger.info(`Verifying table ${selector} contains specific rows`);

  // Get the table element
  const table = this.getPage().locator(selector);

  // Get all rows from the table
  const rows = await table.locator('tr').all();

  // Get the expected rows from the data table
  const expectedRows = dataTable.rows();

  // For each expected row, check if it exists in the table
  for (const expectedRow of expectedRows) {
    let rowFound = false;

    // Check each actual row
    for (const row of rows) {
      // Get all cells in the row
      const cells = await row.locator('td, th').all();

      // Extract text from each cell
      const cellTexts = await Promise.all(cells.map(cell => cell.innerText()));

      // Check if all expected cells match the actual cells
      const allCellsMatch = expectedRow.every((expectedCell: string) =>
        cellTexts.some(cellText => cellText.includes(expectedCell))
      );

      if (allCellsMatch) {
        rowFound = true;
        break;
      }
    }

    // Assert that the row was found
    expect(rowFound).toBeTruthy();
  }
});

/**
 * Verify a list contains specific items
 */
Then('the list {string} should contain:', async function(this: CustomWorld, selector: string, dataTable) {
  logger.info(`Verifying list ${selector} contains specific items`);

  // Get the list element
  const list = this.getPage().locator(selector);

  // Get all items from the list
  const items = await list.locator('li').all();

  // Get the expected items from the data table
  const expectedItems = dataTable.raw().map((row: string[]) => row[0]);

  // Extract text from each list item
  const itemTexts = await Promise.all(items.map(item => item.innerText()));

  // Check if all expected items are in the list
  for (const expectedItem of expectedItems) {
    const itemFound = itemTexts.some(itemText => itemText.includes(expectedItem));
    expect(itemFound).toBeTruthy();
  }
});

/**
 * Verify a list does not contain specific items
 */
Then('the list {string} should not contain:', async function(this: CustomWorld, selector: string, dataTable) {
  logger.info(`Verifying list ${selector} does not contain specific items`);

  // Get the list element
  const list = this.getPage().locator(selector);

  // Get all items from the list
  const items = await list.locator('li').all();

  // Get the expected items from the data table
  const unexpectedItems = dataTable.raw().map((row: string[]) => row[0]);

  // Extract text from each list item
  const itemTexts = await Promise.all(items.map(item => item.innerText()));

  // Check that none of the unexpected items are in the list
  for (const unexpectedItem of unexpectedItems) {
    const itemFound = itemTexts.some(itemText => itemText.includes(unexpectedItem));
    expect(itemFound).toBeFalsy();
  }
});

/**
 * Click on a specific item in a list
 */
When('I click on item {string} in list {string}', async function(this: CustomWorld, itemText: string, selector: string) {
  logger.info(`Clicking on item ${itemText} in list ${selector}`);

  // Get the list element
  const list = this.getPage().locator(selector);

  // Get all items from the list
  const items = await list.locator('li').all();

  // Find the item with the matching text
  let itemFound = false;
  for (const item of items) {
    const text = await item.innerText();
    if (text.includes(itemText)) {
      await item.click();
      itemFound = true;
      break;
    }
  }

  // Assert that the item was found and clicked
  expect(itemFound).toBeTruthy();
});

/**
 * Store all items from a list
 */
When('I store all items from list {string} as {string}', async function(this: CustomWorld, selector: string, key: string) {
  logger.info(`Storing all items from list ${selector} as ${key}`);

  // Get the list element
  const list = this.getPage().locator(selector);

  // Get all items from the list
  const items = await list.locator('li').all();

  // Extract text from each list item
  const itemTexts = await Promise.all(items.map(item => item.innerText()));

  // Store the list of texts
  this.storeData(key, itemTexts);

  logger.info(`Stored ${itemTexts.length} items from list`);
});

// ========== FILE DOWNLOAD STEPS ==========

/**
 * Verify a file download
 */
Then('I verify file {string} is downloaded', async function(this: CustomWorld, fileName: string) {
  logger.info(`Verifying file download: ${fileName}`);

  // Get the download directory from config or use default
  const downloadDir = EnvConfig.DOWNLOAD_DIR || './test-results/downloads';

  // Create a promise that resolves when the download is complete
  const downloadPromise = this.getPage().waitForEvent('download');

  // Wait for the download to complete
  const download = await downloadPromise;

  // Get the suggested filename
  const suggestedFileName = download.suggestedFilename();

  // Verify the filename matches
  expect(suggestedFileName).toBe(fileName);

  // Save the download to the download directory
  const filePath = path.join(downloadDir, suggestedFileName);
  await download.saveAs(filePath);

  // Verify the file exists
  expect(fs.existsSync(filePath)).toBeTruthy();

  logger.info(`File downloaded successfully: ${filePath}`);
});

/**
 * Download a file by clicking on a link
 */
When('I download file by clicking on {string}', async function(this: CustomWorld, selector: string) {
  logger.info(`Downloading file by clicking on: ${selector}`);

  // Get the download directory from config or use default
  const downloadDir = EnvConfig.DOWNLOAD_DIR || './test-results/downloads';

  // Create the directory if it doesn't exist
  if (!fs.existsSync(downloadDir)) {
    fs.mkdirSync(downloadDir, { recursive: true });
  }

  // Start waiting for the download before clicking
  const downloadPromise = this.getPage().waitForEvent('download');

  // Click the download link
  await this.getPage().click(selector);

  // Wait for the download to complete
  const download = await downloadPromise;

  // Get the suggested filename
  const suggestedFileName = download.suggestedFilename();

  // Save the download to the download directory
  const filePath = path.join(downloadDir, suggestedFileName);
  await download.saveAs(filePath);

  // Store the download info for later use
  this.storeData('lastDownloadedFile', {
    fileName: suggestedFileName,
    filePath: filePath
  });

  logger.info(`File downloaded successfully: ${filePath}`);
});

// ========== ELEMENT SELECTION BY INDEX STEPS ==========

/**
 * Click on an element at a specific index
 */
When('I click on element {string} at index {int}', async function(this: CustomWorld, selector: string, index: number) {
  logger.info(`Clicking on element ${selector} at index ${index}`);

  // Get all elements matching the selector
  const elements = this.getPage().locator(selector);

  // Get the count of elements
  const count = await elements.count();

  // Verify the index is valid
  if (index < 0 || index >= count) {
    throw new Error(`Index ${index} is out of range. Available elements: ${count}`);
  }

  // Click on the element at the specified index
  await elements.nth(index).click();
});

/**
 * Get text from an element at a specific index
 */
When('I store text from element {string} at index {int} as {string}', async function(this: CustomWorld, selector: string, index: number, key: string) {
  logger.info(`Storing text from element ${selector} at index ${index} as ${key}`);

  // Get all elements matching the selector
  const elements = this.getPage().locator(selector);

  // Get the count of elements
  const count = await elements.count();

  // Verify the index is valid
  if (index < 0 || index >= count) {
    throw new Error(`Index ${index} is out of range. Available elements: ${count}`);
  }

  // Get the text from the element at the specified index
  const text = await elements.nth(index).innerText();

  // Store the text
  this.storeData(key, text);

  logger.info(`Stored text: ${text}`);
});

// ========== WEB STORAGE STEPS ==========

/**
 * Set a localStorage item
 */
When('I set localStorage item {string} to {string}', async function(this: CustomWorld, key: string, value: string) {
  logger.info(`Setting localStorage item: ${key} = ${value}`);

  await this.getPage().evaluate(([k, v]) => {
    localStorage.setItem(k, v);
  }, [key, value]);
});

/**
 * Get a localStorage item
 */
When('I store localStorage item {string} as {string}', async function(this: CustomWorld, key: string, storeKey: string) {
  logger.info(`Storing localStorage item: ${key}`);

  const value = await this.getPage().evaluate((k) => {
    return localStorage.getItem(k);
  }, key);

  this.storeData(storeKey, value);

  logger.info(`Stored localStorage item: ${key} = ${value}`);
});

/**
 * Clear localStorage
 */
When('I clear localStorage', async function(this: CustomWorld) {
  logger.info('Clearing localStorage');

  await this.getPage().evaluate(() => {
    localStorage.clear();
  });
});

/**
 * Set a sessionStorage item
 */
When('I set sessionStorage item {string} to {string}', async function(this: CustomWorld, key: string, value: string) {
  logger.info(`Setting sessionStorage item: ${key} = ${value}`);

  await this.getPage().evaluate(([k, v]) => {
    sessionStorage.setItem(k, v);
  }, [key, value]);
});

// ========== COOKIE HANDLING STEPS ==========

/**
 * Set a cookie
 */
When('I set cookie {string} to {string}', async function(this: CustomWorld, name: string, value: string) {
  logger.info(`Setting cookie: ${name} = ${value}`);

  await this.getBrowserContext().addCookies([{
    name,
    value,
    url: this.getPage().url()
  }]);
});

/**
 * Get a cookie
 */
When('I store cookie {string} as {string}', async function(this: CustomWorld, name: string, key: string) {
  logger.info(`Storing cookie: ${name}`);

  const cookies = await this.getBrowserContext().cookies();
  const cookie = cookies.find(c => c.name === name);

  if (cookie) {
    this.storeData(key, cookie.value);
    logger.info(`Stored cookie: ${name} = ${cookie.value}`);
  } else {
    logger.warn(`Cookie not found: ${name}`);
    this.storeData(key, null);
  }
});

/**
 * Clear all cookies
 */
When('I clear all cookies', async function(this: CustomWorld) {
  logger.info('Clearing all cookies');

  await this.getBrowserContext().clearCookies();
});

// ========== SORTING AND FILTERING STEPS ==========

/**
 * Verify a list is sorted in ascending order
 */
Then('the list {string} should be sorted in ascending order', async function(this: CustomWorld, selector: string) {
  logger.info(`Verifying list ${selector} is sorted in ascending order`);

  // Get the list element
  const list = this.getPage().locator(selector);

  // Get all items from the list
  const items = await list.locator('li').all();

  // Extract text from each list item
  const itemTexts = await Promise.all(items.map(item => item.innerText()));

  // Create a copy of the array and sort it
  const sortedTexts = [...itemTexts].sort();

  // Compare the original array with the sorted array
  for (let i = 0; i < itemTexts.length; i++) {
    expect(itemTexts[i]).toBe(sortedTexts[i]);
  }
});

/**
 * Verify a list is sorted in descending order
 */
Then('the list {string} should be sorted in descending order', async function(this: CustomWorld, selector: string) {
  logger.info(`Verifying list ${selector} is sorted in descending order`);

  // Get the list element
  const list = this.getPage().locator(selector);

  // Get all items from the list
  const items = await list.locator('li').all();

  // Extract text from each list item
  const itemTexts = await Promise.all(items.map(item => item.innerText()));

  // Create a copy of the array and sort it in descending order
  const sortedTexts = [...itemTexts].sort().reverse();

  // Compare the original array with the sorted array
  for (let i = 0; i < itemTexts.length; i++) {
    expect(itemTexts[i]).toBe(sortedTexts[i]);
  }
});

/**
 * Filter a list by text
 */
When('I filter list {string} by text {string}', async function(this: CustomWorld, selector: string, filterText: string) {
  logger.info(`Filtering list ${selector} by text: ${filterText}`);

  // This is a generic step that assumes there's a filter input field
  // The actual implementation will depend on the application

  // Find a filter input field (this is a common pattern, but may need to be adjusted)
  const filterInput = this.getPage().locator('input[placeholder*="filter"], input[placeholder*="search"], input[type="search"]').first();

  // Clear the input and type the filter text
  await filterInput.clear();
  await filterInput.fill(filterText);

  // Wait for the list to update
  await this.getPage().waitForTimeout(500);
});

// ========== TEXT HIGHLIGHTING STEPS ==========

/**
 * Verify text highlighting
 */
Then('the text {string} should be highlighted in element {string}', async function(this: CustomWorld, text: string, selector: string) {
  logger.info(`Verifying text "${text}" is highlighted in element ${selector}`);

  // Get the element
  const element = this.getPage().locator(selector);

  // Get the HTML content of the element
  const html = await element.innerHTML();

  // Check if the text is wrapped in a highlight element (common patterns)
  const isHighlighted = html.includes(`<mark>${text}</mark>`) ||
                        html.includes(`<span class="highlight">${text}</span>`) ||
                        html.includes(`<span class="highlighted">${text}</span>`) ||
                        html.includes(`<strong>${text}</strong>`) ||
                        html.includes(`<em>${text}</em>`);

  expect(isHighlighted).toBeTruthy();
});

// ========== APPLICATION-SPECIFIC NAVIGATION STEPS ==========

/**
 * Navigate to a specific application
 * This step uses a configuration map to determine the URL for each application
 */
Given('I navigate to {string} application', async function(this: CustomWorld, appName: string) {
  logger.info(`Navigating to application: ${appName}`);

  // This is a generic step that uses a configuration map to determine the URL
  // The actual implementation will depend on the application

  // Create a map of application names to URLs
  // In a real implementation, this would be loaded from a configuration file
  const appUrls: Record<string, string> = {
    'home': EnvConfig.BASE_URL,
    'admin': `${EnvConfig.BASE_URL}/admin`,
    'dashboard': `${EnvConfig.BASE_URL}/dashboard`,
    'reports': `${EnvConfig.BASE_URL}/reports`,
    'settings': `${EnvConfig.BASE_URL}/settings`,
    'profile': `${EnvConfig.BASE_URL}/profile`,
    'login': `${EnvConfig.BASE_URL}/login`,
    'register': `${EnvConfig.BASE_URL}/register`,
    'api': EnvConfig.API_URL
  };

  // Get the URL for the specified application
  const url = appUrls[appName.toLowerCase()];

  if (!url) {
    throw new Error(`Unknown application: ${appName}`);
  }

  // Navigate to the URL
  await this.getPage().goto(url, { waitUntil: 'networkidle' });
});

/**
 * Navigate to a specific application with a specific path
 */
Given('I navigate to {string} application with path {string}', async function(this: CustomWorld, appName: string, path: string) {
  logger.info(`Navigating to application: ${appName} with path: ${path}`);

  // This is a generic step that uses a configuration map to determine the URL
  // The actual implementation will depend on the application

  // Create a map of application names to base URLs
  // In a real implementation, this would be loaded from a configuration file
  const appUrls: Record<string, string> = {
    'home': EnvConfig.BASE_URL,
    'admin': `${EnvConfig.BASE_URL}/admin`,
    'dashboard': `${EnvConfig.BASE_URL}/dashboard`,
    'reports': `${EnvConfig.BASE_URL}/reports`,
    'settings': `${EnvConfig.BASE_URL}/settings`,
    'profile': `${EnvConfig.BASE_URL}/profile`,
    'login': `${EnvConfig.BASE_URL}/login`,
    'register': `${EnvConfig.BASE_URL}/register`,
    'api': EnvConfig.API_URL
  };

  // Get the base URL for the specified application
  const baseUrl = appUrls[appName.toLowerCase()];

  if (!baseUrl) {
    throw new Error(`Unknown application: ${appName}`);
  }

  // Combine the base URL with the path
  const url = new URL(path, baseUrl).toString();

  // Navigate to the URL
  await this.getPage().goto(url, { waitUntil: 'networkidle' });
});

/**
 * Navigate to a specific application based on a condition
 */
Given('I navigate to {string} application based on condition {string}', async function(this: CustomWorld, appName: string, condition: string) {
  logger.info(`Conditionally navigating to application: ${appName} based on condition: ${condition}`);

  if (condition === 'yes') {
    // This is a generic step that uses a configuration map to determine the URL
    // The actual implementation will depend on the application

    // Create a map of application names to URLs
    // In a real implementation, this would be loaded from a configuration file
    const appUrls: Record<string, string> = {
      'home': EnvConfig.BASE_URL,
      'admin': `${EnvConfig.BASE_URL}/admin`,
      'dashboard': `${EnvConfig.BASE_URL}/dashboard`,
      'reports': `${EnvConfig.BASE_URL}/reports`,
      'settings': `${EnvConfig.BASE_URL}/settings`,
      'profile': `${EnvConfig.BASE_URL}/profile`,
      'login': `${EnvConfig.BASE_URL}/login`,
      'register': `${EnvConfig.BASE_URL}/register`,
      'api': EnvConfig.API_URL
    };

    // Get the URL for the specified application
    const url = appUrls[appName.toLowerCase()];

    if (!url) {
      throw new Error(`Unknown application: ${appName}`);
    }

    // Navigate to the URL
    await this.getPage().goto(url, { waitUntil: 'networkidle' });
  } else {
    logger.info(`Skipping navigation to ${appName} because condition is not met`);
  }
});