import { Locator, Page } from '@playwright/test';
import { EnvConfig } from '../config/env.config';
import { Logger } from './logger';

/**
 * UI Helper class
 * Provides methods for common UI interactions
 */
export class UiHelper {
  private logger: Logger;

  /**
   * Constructor
   * @param page The Playwright page
   */
  constructor(private page: Page) {
    this.logger = new Logger('UiHelper');
  }

  /**
   * Highlight an element on the page using Playwright locator
   * @param locator The Playwright locator
   * @param options Highlight options (optional)
   */
  public async highlightLocator(locator: Locator, options?: { color?: string, duration?: number }): Promise<void> {
    if (!EnvConfig.HIGHLIGHT_ELEMENTS) {
      return;
    }

    try {
      const color = options?.color || EnvConfig.HIGHLIGHT_COLOR;
      const duration = options?.duration || EnvConfig.HIGHLIGHT_DURATION;

      // Use a more efficient highlighting approach with timeout
      await Promise.race([
        locator.evaluate(
          (element: HTMLElement, color: string) => {
            if (element) {
              const originalOutline = element.style.outline;
              const originalOutlineOffset = element.style.outlineOffset;
              const originalBoxShadow = element.style.boxShadow;

              // Apply highlight effects for better visibility
              element.style.outline = `3px solid ${color}`;
              element.style.outlineOffset = '2px';
              element.style.boxShadow = `0 0 8px ${color}`;
              element.style.transition = 'all 0.2s ease';

              // Store original values as data attributes
              element.setAttribute('data-original-outline', originalOutline);
              element.setAttribute('data-original-outline-offset', originalOutlineOffset);
              element.setAttribute('data-original-box-shadow', originalBoxShadow);
            }
          },
          color
        ),
        // Timeout after 2 seconds to prevent hanging
        new Promise((_, reject) => setTimeout(() => reject(new Error('Highlight timeout')), 2000))
      ]);

      // Wait for the specified duration (but shorter for performance)
      if (duration > 0) {
        const actualDuration = Math.min(duration, 800); // Max 800ms
        await this.page.waitForTimeout(actualDuration);

        // Remove the highlight if duration is specified
        await Promise.race([
          locator.evaluate((element: HTMLElement) => {
            if (element) {
              // Restore original values
              element.style.outline = element.getAttribute('data-original-outline') || '';
              element.style.outlineOffset = element.getAttribute('data-original-outline-offset') || '';
              element.style.boxShadow = element.getAttribute('data-original-box-shadow') || '';
              element.style.transition = '';

              // Remove data attributes
              element.removeAttribute('data-original-outline');
              element.removeAttribute('data-original-outline-offset');
              element.removeAttribute('data-original-box-shadow');
            }
          }),
          // Timeout after 1 second
          new Promise((_, reject) => setTimeout(() => reject(new Error('Unhighlight timeout')), 1000))
        ]);
      }
    } catch (error) {
      // Ignore errors during highlighting to not disrupt test execution
      this.logger.warn(`Failed to highlight locator: ${error.message || error}`);
    }
  }

  /**
   * Highlight an element on the page (legacy method for CSS selectors)
   * @param selector The element selector
   * @param options Highlight options (optional)
   */
  public async highlightElement(selector: string, options?: { color?: string, duration?: number }): Promise<void> {
    if (!EnvConfig.HIGHLIGHT_ELEMENTS) {
      return;
    }

    try {
      const locator = this.page.locator(selector);
      await this.highlightLocator(locator, options);
    } catch (error) {
      // Ignore errors during highlighting to not disrupt test execution
      this.logger.warn(`Failed to highlight element ${selector}: ${error}`);
    }
  }

  /**
   * Decorator method to wrap UI interactions with element highlighting
   * @param selector The element selector
   * @param action The action to perform on the element
   * @param actionName The name of the action (for logging)
   */
  private async withHighlight<T>(selector: string, action: () => Promise<T>, actionName: string): Promise<T> {
    this.logger.debug(`${actionName} on element: ${selector}`);

    // Highlight the element before the action
    await this.highlightElement(selector);

    // Perform the action
    return await action();
  }

  /**
   * Wait for page load state
   * @param state The load state to wait for (default: 'networkidle')
   * @param timeout Timeout in milliseconds (optional)
   */
  public async waitForLoadState(state: 'load' | 'domcontentloaded' | 'networkidle' = 'networkidle', timeout?: number): Promise<void> {
    this.logger.debug(`Waiting for page load state: ${state}`);
    await this.page.waitForLoadState(state, { timeout: timeout || EnvConfig.NAVIGATION_TIMEOUT });
  }

  /**
   * Wait for URL to contain a specific string
   * @param urlSubstring The URL substring to wait for
   * @param timeout Timeout in milliseconds (optional)
   */
  public async waitForUrl(urlSubstring: string, timeout?: number): Promise<void> {
    this.logger.debug(`Waiting for URL to contain: ${urlSubstring}`);
    await this.page.waitForURL(url => url.toString().includes(urlSubstring), { timeout: timeout || EnvConfig.NAVIGATION_TIMEOUT });
  }

  /**
   * Wait for an element to be visible
   * @param selector The element selector
   * @param timeout Timeout in milliseconds (optional)
   * @returns The element locator
   */
  public async waitForVisible(selector: string, timeout?: number): Promise<Locator> {
    this.logger.debug(`Waiting for element to be visible: ${selector}`);
    const locator = this.page.locator(selector);
    await locator.waitFor({ state: 'visible', timeout: timeout || EnvConfig.DEFAULT_TIMEOUT });
    return locator;
  }

  /**
   * Wait for an element to be hidden
   * @param selector The element selector
   * @param timeout Timeout in milliseconds (optional)
   * @returns The element locator
   */
  public async waitForHidden(selector: string, timeout?: number): Promise<Locator> {
    this.logger.debug(`Waiting for element to be hidden: ${selector}`);
    const locator = this.page.locator(selector);
    await locator.waitFor({ state: 'hidden', timeout: timeout || EnvConfig.DEFAULT_TIMEOUT });
    return locator;
  }

  /**
   * Wait for an element to be enabled
   * @param selector The element selector
   * @param timeout Timeout in milliseconds (optional)
   * @returns The element locator
   */
  public async waitForEnabled(selector: string, timeout?: number): Promise<Locator> {
    this.logger.debug(`Waiting for element to be enabled: ${selector}`);
    const locator = this.page.locator(selector);
    await locator.waitFor({ state: 'attached', timeout: timeout || EnvConfig.DEFAULT_TIMEOUT });
    await this.page.waitForSelector(`${selector}:not([disabled])`, { timeout: timeout || EnvConfig.DEFAULT_TIMEOUT });
    return locator;
  }

  /**
   * Click on an element
   * @param selector The element selector
   * @param options Click options (optional)
   */
  public async click(selector: string, options?: { force?: boolean, timeout?: number }): Promise<void> {
    await this.withHighlight(
      selector,
      async () => {
        const locator = await this.waitForEnabled(selector, options?.timeout || 10000);
        await locator.click({ force: options?.force, timeout: 10000 });
      },
      'Clicking'
    );
  }

  /**
   * Double click on an element
   * @param selector The element selector
   * @param options Click options (optional)
   */
  public async doubleClick(selector: string, options?: { force?: boolean, timeout?: number }): Promise<void> {
    await this.withHighlight(
      selector,
      async () => {
        const locator = await this.waitForEnabled(selector, options?.timeout);
        await locator.dblclick({ force: options?.force });
      },
      'Double-clicking'
    );
  }

  /**
   * Right click on an element
   * @param selector The element selector
   * @param options Click options (optional)
   */
  public async rightClick(selector: string, options?: { force?: boolean, timeout?: number }): Promise<void> {
    await this.withHighlight(
      selector,
      async () => {
        const locator = await this.waitForEnabled(selector, options?.timeout);
        await locator.click({ button: 'right', force: options?.force });
      },
      'Right-clicking'
    );
  }

  /**
   * Fill a form field
   * @param selector The element selector
   * @param value The value to fill
   * @param options Fill options (optional)
   */
  public async fill(selector: string, value: string, options?: { timeout?: number }): Promise<void> {
    await this.withHighlight(
      selector,
      async () => {
        const locator = await this.waitForEnabled(selector, options?.timeout);
        await locator.fill(value);
      },
      `Filling with "${value}"`
    );
  }

  /**
   * Type text into a form field
   * @param selector The element selector
   * @param text The text to type
   * @param options Type options (optional)
   */
  public async type(selector: string, text: string, options?: { delay?: number, timeout?: number }): Promise<void> {
    await this.withHighlight(
      selector,
      async () => {
        const locator = await this.waitForEnabled(selector, options?.timeout);

        // Clear the field first
        await locator.clear();

        // Use fill with delay instead of deprecated type method
        if (options?.delay) {
          // For delay effect, type character by character
          for (const char of text) {
            await locator.pressSequentially(char, { delay: options.delay });
          }
        } else {
          // Use fill for faster input when no delay is needed
          await locator.fill(text);
        }
      },
      `Typing "${text}"`
    );
  }

  /**
   * Clear a form field
   * @param selector The element selector
   * @param options Clear options (optional)
   */
  public async clear(selector: string, options?: { timeout?: number }): Promise<void> {
    await this.withHighlight(
      selector,
      async () => {
        const locator = await this.waitForEnabled(selector, options?.timeout);
        await locator.clear();
      },
      'Clearing'
    );
  }

  /**
   * Select an option from a dropdown
   * @param selector The dropdown selector
   * @param option The option to select (value, label, or index)
   * @param options Select options (optional)
   */
  public async selectOption(selector: string, option: string | { value?: string, label?: string, index?: number }, options?: { timeout?: number }): Promise<void> {
    await this.withHighlight(
      selector,
      async () => {
        const locator = await this.waitForEnabled(selector, options?.timeout);
        await locator.selectOption(option);
      },
      `Selecting option ${typeof option === 'string' ? option : JSON.stringify(option)}`
    );
  }

  /**
   * Check a checkbox or radio button
   * @param selector The element selector
   * @param options Check options (optional)
   */
  public async check(selector: string, options?: { force?: boolean, timeout?: number }): Promise<void> {
    await this.withHighlight(
      selector,
      async () => {
        const locator = await this.waitForEnabled(selector, options?.timeout);
        await locator.check({ force: options?.force });
      },
      'Checking'
    );
  }

  /**
   * Uncheck a checkbox
   * @param selector The element selector
   * @param options Uncheck options (optional)
   */
  public async uncheck(selector: string, options?: { force?: boolean, timeout?: number }): Promise<void> {
    await this.withHighlight(
      selector,
      async () => {
        const locator = await this.waitForEnabled(selector, options?.timeout);
        await locator.uncheck({ force: options?.force });
      },
      'Unchecking'
    );
  }

  /**
   * Get text from an element
   * @param selector The element selector
   * @param options Get text options (optional)
   * @returns The element text
   */
  public async getText(selector: string, options?: { timeout?: number }): Promise<string> {
    return await this.withHighlight(
      selector,
      async () => {
        const locator = await this.waitForVisible(selector, options?.timeout);
        return await locator.innerText();
      },
      'Getting text'
    );
  }

  /**
   * Get value from a form field
   * @param selector The element selector
   * @param options Get value options (optional)
   * @returns The element value
   */
  public async getValue(selector: string, options?: { timeout?: number }): Promise<string> {
    return await this.withHighlight(
      selector,
      async () => {
        const locator = await this.waitForVisible(selector, options?.timeout);
        return await locator.inputValue();
      },
      'Getting value'
    );
  }

  /**
   * Check if an element is visible
   * @param selector The element selector
   * @returns True if the element is visible
   */
  public async isVisible(selector: string): Promise<boolean> {
    this.logger.debug(`Checking if element is visible: ${selector}`);
    const locator = this.page.locator(selector);
    return await locator.isVisible();
  }

  /**
   * Check if an element is enabled
   * @param selector The element selector
   * @returns True if the element is enabled
   */
  public async isEnabled(selector: string): Promise<boolean> {
    this.logger.debug(`Checking if element is enabled: ${selector}`);
    const locator = this.page.locator(selector);
    return await locator.isEnabled();
  }

  /**
   * Check if an element is checked
   * @param selector The element selector
   * @returns True if the element is checked
   */
  public async isChecked(selector: string): Promise<boolean> {
    this.logger.debug(`Checking if element is checked: ${selector}`);
    const locator = this.page.locator(selector);
    return await locator.isChecked();
  }

  /**
   * Hover over an element
   * @param selector The element selector
   * @param options Hover options (optional)
   */
  public async hover(selector: string, options?: { force?: boolean, timeout?: number }): Promise<void> {
    await this.withHighlight(
      selector,
      async () => {
        const locator = await this.waitForVisible(selector, options?.timeout || 10000);
        await locator.hover({ force: options?.force, timeout: 10000 });
      },
      'Hovering'
    );
  }

  /**
   * Drag and drop an element
   * @param sourceSelector The source element selector
   * @param targetSelector The target element selector
   * @param options Drag and drop options (optional)
   */
  public async dragAndDrop(sourceSelector: string, targetSelector: string, options?: { timeout?: number }): Promise<void> {
    // Highlight source element
    await this.highlightElement(sourceSelector);

    // Highlight target element
    await this.highlightElement(targetSelector);

    this.logger.debug(`Dragging element ${sourceSelector} to ${targetSelector}`);
    const sourceLocator = await this.waitForVisible(sourceSelector, options?.timeout);
    const targetLocator = await this.waitForVisible(targetSelector, options?.timeout);

    await sourceLocator.dragTo(targetLocator);
  }

  /**
   * Press a key or key combination
   * @param selector The element selector (optional)
   * @param key The key or key combination to press
   */
  public async press(selector: string | null, key: string): Promise<void> {
    this.logger.debug(`Pressing key: ${key}`);

    if (selector) {
      await this.withHighlight(
        selector,
        async () => {
          const locator = await this.waitForVisible(selector);
          await locator.press(key);
        },
        `Pressing ${key}`
      );
    } else {
      await this.page.keyboard.press(key);
    }
  }

  /**
   * Take a screenshot
   * @param name Screenshot name (optional)
   * @returns The screenshot path
   */
  public async takeScreenshot(name?: string): Promise<string> {
    const screenshotName = name || `screenshot_${Date.now()}.png`;
    this.logger.info(`Taking screenshot: ${screenshotName}`);

    const screenshotPath = `./test-results/screenshots/${screenshotName}`;
    await this.page.screenshot({ path: screenshotPath, fullPage: true });

    return screenshotPath;
  }
}
