@travel @booking
Feature: Booking.com Travel Website
  As a traveler
  I want to search for accommodations, compare prices, and manage bookings
  So that I can plan my trips efficiently

  Background:
    Given I navigate to "https://www.booking.com"
    And I wait for page to load

  @smoke @hotel-search
  Scenario: Search for hotels in a destination
    When I wait for "#ss" to be visible
    And I fill "#ss" with "New York"
    And I click on ".xp__dates-inner"
    
    # Select check-in date (30 days from now)
    And I click on ".bui-calendar__date:not(.bui-calendar__date--disabled):nth-child(30)"
    
    # Select check-out date (35 days from now)
    And I click on ".bui-calendar__date:not(.bui-calendar__date--disabled):nth-child(35)"
    
    # Set 2 adults, 1 room
    And I click on ".xp__guests__count"
    And I wait for ".sb-group__field-adults" to be visible
    
    And I click on "button[data-bui-ref='input-stepper-add-button']:first-child"
    And I click on "button.sb-searchbox__button"
    And I wait for "#search_results_table" to be visible
    
    Then the page should contain text "New York"
    And the page should contain text "properties found"
    And there should be more than 0 ".sr_property_block" elements

  @filter-results
  Scenario: Filter hotel search results
    When I wait for "#ss" to be visible
    And I fill "#ss" with "Paris"
    And I click on ".xp__dates-inner"
    
    # Select check-in date (30 days from now)
    And I click on ".bui-calendar__date:not(.bui-calendar__date--disabled):nth-child(30)"
    
    # Select check-out date (35 days from now)
    And I click on ".bui-calendar__date:not(.bui-calendar__date--disabled):nth-child(35)"
    
    And I click on "button.sb-searchbox__button"
    And I wait for "#search_results_table" to be visible
    
    # Filter by 4-star hotels
    When I click on "div[data-filters-group='class'] input[name='class=4']"
    And I wait for "#search_results_table" to be visible
    Then the page should contain text "4 stars"
    
    # Filter by review score
    When I click on "div[data-filters-group='review_score'] input[name='review_score=80']"
    And I wait for "#search_results_table" to be visible
    Then the page should contain text "Very Good"
    
    # Filter by price range
    When I click on "div[data-filters-group='price'] input[name='pri=3']"
    And I wait for "#search_results_table" to be visible
    Then the page should contain text "US$"

  @sort-results
  Scenario: Sort hotel search results
    When I wait for "#ss" to be visible
    And I fill "#ss" with "London"
    And I click on ".xp__dates-inner"
    
    # Select check-in date (30 days from now)
    And I click on ".bui-calendar__date:not(.bui-calendar__date--disabled):nth-child(30)"
    
    # Select check-out date (35 days from now)
    And I click on ".bui-calendar__date:not(.bui-calendar__date--disabled):nth-child(35)"
    
    And I click on "button.sb-searchbox__button"
    And I wait for "#search_results_table" to be visible
    
    # Sort by price (lowest first)
    When I click on "button:contains('Price (lowest first)')"
    And I wait for "#search_results_table" to be visible
    Then the page should contain text "Price (lowest first)"
    
    # Sort by review score
    When I click on "button:contains('Review score')"
    And I wait for "#search_results_table" to be visible
    Then the page should contain text "Review score"

  @hotel-details
  Scenario: View hotel details and room options
    When I wait for "#ss" to be visible
    And I fill "#ss" with "Rome"
    And I click on ".xp__dates-inner"
    
    # Select check-in date (30 days from now)
    And I click on ".bui-calendar__date:not(.bui-calendar__date--disabled):nth-child(30)"
    
    # Select check-out date (35 days from now)
    And I click on ".bui-calendar__date:not(.bui-calendar__date--disabled):nth-child(35)"
    
    And I click on "button.sb-searchbox__button"
    And I wait for "#search_results_table" to be visible
    
    When I click on ".sr_property_block:first-child .hotel_name_link"
    And I wait for "#hp_hotel_name" to be visible
    Then the page should contain text "Rome"
    And the page should contain text "Overview"
    And the page should contain text "Select your room"
    
    When I click on "a:contains('See availability')"
    And I wait for "#hprt-table" to be visible
    Then the page should contain text "Select your room"
    And there should be more than 0 ".hprt-table-cell-roomtype" elements

  @map-view
  Scenario: View hotels on a map
    When I wait for "#ss" to be visible
    And I fill "#ss" with "Barcelona"
    And I click on ".xp__dates-inner"
    
    # Select check-in date (30 days from now)
    And I click on ".bui-calendar__date:not(.bui-calendar__date--disabled):nth-child(30)"
    
    # Select check-out date (35 days from now)
    And I click on ".bui-calendar__date:not(.bui-calendar__date--disabled):nth-child(35)"
    
    And I click on "button.sb-searchbox__button"
    And I wait for "#search_results_table" to be visible
    
    When I click on "a:contains('Show on map')"
    And I wait for "#map_full_overlay" to be visible
    Then the page should contain text "Map view"
    And the "#map_full_overlay" element should be visible
    
    When I click on ".map_full_overlay__close"
    Then the "#search_results_table" element should be visible

  @reviews
  Scenario: Read hotel reviews
    When I wait for "#ss" to be visible
    And I fill "#ss" with "Amsterdam"
    And I click on ".xp__dates-inner"
    
    # Select check-in date (30 days from now)
    And I click on ".bui-calendar__date:not(.bui-calendar__date--disabled):nth-child(30)"
    
    # Select check-out date (35 days from now)
    And I click on ".bui-calendar__date:not(.bui-calendar__date--disabled):nth-child(35)"
    
    And I click on "button.sb-searchbox__button"
    And I wait for "#search_results_table" to be visible
    
    When I click on ".sr_property_block:first-child .hotel_name_link"
    And I wait for "#hp_hotel_name" to be visible
    
    When I click on "a:contains('Show all reviews')"
    And I wait for "#review_list_page_container" to be visible
    Then the page should contain text "Reviews"
    And there should be more than 0 ".review_list_new_item_block" elements
    
    When I click on "button:contains('Facilities')"
    And I wait for "#review_list_page_container" to be visible
    Then the page should contain text "Facilities"

  @alternative-properties
  Scenario: View alternative property types
    When I wait for "#ss" to be visible
    And I fill "#ss" with "Miami"
    And I click on ".xp__dates-inner"
    
    # Select check-in date (30 days from now)
    And I click on ".bui-calendar__date:not(.bui-calendar__date--disabled):nth-child(30)"
    
    # Select check-out date (35 days from now)
    And I click on ".bui-calendar__date:not(.bui-calendar__date--disabled):nth-child(35)"
    
    And I click on "button.sb-searchbox__button"
    And I wait for "#search_results_table" to be visible
    
    # Filter by apartments
    When I click on "div[data-filters-group='property_type'] input[name='property_type=3']"
    And I wait for "#search_results_table" to be visible
    Then the page should contain text "Apartment"
    
    # Filter by villas
    When I click on "div[data-filters-group='property_type'] input[name='property_type=11']"
    And I wait for "#search_results_table" to be visible
    Then the page should contain text "Villa"

  @flexible-dates
  Scenario: Search with flexible dates
    When I wait for "#ss" to be visible
    And I fill "#ss" with "Bali"
    And I click on ".xp__dates-inner"
    
    # Click on flexible dates option if available
    When if "a:contains('I'm flexible')" is visible, then:
      | I click on "a:contains('I'm flexible')" |
      | I wait for ".sb-date-field__flexible-dates" to be visible |
      | I click on ".sb-date-field__flexible-dates-month:first-child" |
      | I click on "button:contains('Search')" |
    
    # If flexible dates not available, use regular date picker
    When if "a:contains('I'm flexible')" is not visible, then:
      | I click on ".bui-calendar__date:not(.bui-calendar__date--disabled):nth-child(30)" |
      | I click on ".bui-calendar__date:not(.bui-calendar__date--disabled):nth-child(35)" |
      | I click on "button.sb-searchbox__button" |
    
    And I wait for "#search_results_table" to be visible
    Then the page should contain text "Bali"
    And there should be more than 0 ".sr_property_block" elements
