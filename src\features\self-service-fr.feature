
@combined @self-service @fr
Feature: Self Service Featured Results
  en
  Background:
    # Store credentials for later use
    Given I store "<EMAIL>" as "USERNAME"

  Scenario: TC0101- Login and Check the Default Data Source Order
    Given I navigate to URL "https://es-settings-staging.kpmg.com/"
    Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000
    When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>"
    And I click on "getByRole('button', { name: 'Next' })"
    Then I wait for element "getByRole('button', { name: 'Settings' })" to be visible with timeout 60000
    And I wait for element "getByRole('button', { name: 'Create a request on behalf of' })" to be visible with timeout 30000
    And I wait for page title to contain "KPMG Find - Settings"
    When I click on "getByRole('button', { name: 'Create a request on behalf of' })"
    Then I wait for element "getByRole('heading', { name: 'Create a requests on behalf' })" to be visible with timeout 10000
    Then the "locator('#displayName-label')" element should have exact text "Selected User"
    And element "getByRole('combobox', { name: 'FeaturedResult' })" should be visible
    And element "getByRole('button', { name: 'Cancel' })" should be visible
    When I click on "getByRole('combobox', { name: 'FeaturedResult' })"
    Then I wait for "locator('#menu-').getByText('FeaturedResult')" to be visible with timeout 30000
    When I click on "locator('#menu-').getByText('FeaturedResult')"
    When I click on "getByRole('textbox', { name: 'Selected User' })"
    Then I wait for element "getByRole('heading', { name: 'Select user for the request' })" to be visible with timeout 10000
    And element "getByRole('dialog', { name: 'Select user for the request' }).locator('svg')" should be visible
    And element "getByRole('searchbox', { name: 'description' })" should be visible
    When I fill "getByRole('searchbox', { name: 'description' })" with "<EMAIL>"
    Then I wait for element "getByLabel('K, Jagannatha')" to be visible with timeout 10000
    And I wait for element "getByLabel('<EMAIL>')" to be visible with timeout 10000
    When I click on "getByLabel('K, Jagannatha')"
    Then I wait for element "getByRole('button', { name: 'Select' })" to be visible with timeout 10000
    When I click on "getByRole('button', { name: 'Select' })"
    Then I wait for element "getByRole('button', { name: 'Create' })" to be visible with timeout 10000
    When I click on "getByRole('button', { name: 'Create' })"
    Then I wait for element "getByText('New Featured Result for K,')" to be visible with timeout 10000
    When I fill "getByRole('textbox', { name: 'Title *' })" with "Test Title"
    And I click on "getByRole('textbox', { name: 'Description' })"
    And I fill "getByRole('textbox', { name: 'Description' })" with "Test Description"
    And I fill "getByRole('textbox', { name: 'Url', exact: true })" with "https://www.google.com"
    And I fill "getByRole('textbox', { name: 'Search terms (provide a comma' })" with "test, terms"
    And I fill "getByRole('textbox', { name: 'Comments' })" with "Test Comments"
    Then element "getByRole('button', { name: 'Publish' })" should be visible
    And element "getByRole('button', { name: 'Create' })" should be visible
    When I click on "getByRole('button', { name: 'Create' })"
    Then I wait for element "getByRole('heading', { name: 'Confirm creating request!' })" to be visible with timeout 10000
    And element "getByRole('button', { name: 'No' })" should be visible
    And element "getByRole('button', { name: 'Yes' })" should be visible
    When I click on "getByRole('button', { name: 'Yes' })"
    Then I wait for element "getByRole('heading', { name: 'Created request' })" to be visible with timeout 10000
    And element "getByText('Successfully created')" should be visible
    And element "getByRole('button', { name: 'Close' })" should be visible
    And I click on "getByRole('button', { name: 'Close' })"
    Then the "(//table)[1]//tr[2]/td[4]//span" element should have exact text "FeaturedResult"
    And the "(//table)[1]//tr[2]/td[5]/div" element should have exact text "Test Title"
    And the "(//table)[1]//tr[2]/td[6]/div" element should have exact text "Test Description"
    And the "(//table)[1]//tr[2]/td[8]/div" element should have exact text "https://www.google.com"
    And the "((//table)[1]//tr[2]/td[9]//span)[1]" element should have exact text "test"
    And the "((//table)[1]//tr[2]/td[9]//span)[2]" element should have exact text "terms"
    And the "(//table)[1]//tr[2]/td[10]//span" element should have exact text "Submitted"
    And the "(//table)[1]//tr[2]/td[13]" element should have exact text "K,  Jagannatha"
    
