
@combined @self-service @fr
Feature: Self Service Featured Results
  As a KPMG user
  I want to create and verify featured results
  So that I can manage search results effectively

  Background:
    Given I navigate to URL "https://es-settings-staging.kpmg.com/"
    And I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000
    When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "j<PERSON><PERSON><PERSON>@kpmg.com"
    And I click on "getByRole('button', { name: 'Next' })"
    Then I wait for element "getByRole('button', { name: 'Settings' })" to be visible with timeout 60000
    And I wait for element "getByRole('button', { name: 'Create a request on behalf of' })" to be visible with timeout 30000
    And I wait for page title to contain "KPMG Find - Settings"

  @smoke @create-featured-result
  Scenario Outline: TC0101- Create Featured Result with Different Data Sets
    # Background should execute first - login and navigate to settings page
    When I click on "getByRole('button', { name: 'Create a request on behalf of' })"
    Then I wait for element "getByRole('heading', { name: 'Create a requests on behalf' })" to be visible with timeout 10000
    And the "locator('#displayName-label')" element should have exact text "Selected User"
    And element "getByRole('combobox', { name: 'FeaturedResult' })" should be visible
    And element "getByRole('button', { name: 'Cancel' })" should be visible

    # Select request type and user
    When I click on "getByRole('combobox', { name: 'FeaturedResult' })"
    And I wait for "locator('#menu-').getByText('FeaturedResult')" to be visible with timeout 30000
    And I click on "locator('#menu-').getByText('FeaturedResult')"
    And I click on "getByRole('textbox', { name: 'Selected User' })"
    Then I wait for element "getByRole('heading', { name: 'Select user for the request' })" to be visible with timeout 10000
    And element "getByRole('dialog', { name: 'Select user for the request' }).locator('svg')" should be visible
    And element "getByRole('searchbox', { name: 'description' })" should be visible

    # Search and select user
    When I fill "getByRole('searchbox', { name: 'description' })" with "<username>"
    Then I wait for element "getByLabel('K, Jagannatha')" to be visible with timeout 10000
    And I wait for element "getByLabel('<username>')" to be visible with timeout 10000
    When I click on "getByLabel('K, Jagannatha')"
    And I wait for element "getByRole('button', { name: 'Select' })" to be visible with timeout 10000
    And I click on "getByRole('button', { name: 'Select' })"

    # Create request and fill form
    Then I wait for element "getByRole('button', { name: 'Create' })" to be visible with timeout 10000
    When I click on "getByRole('button', { name: 'Create' })"
    Then I wait for element "getByText('New Featured Result for K,')" to be visible with timeout 10000

    # Fill form with parameterized data
    When I fill "getByRole('textbox', { name: 'Title *' })" with "<title>"
    And I click on "getByRole('textbox', { name: 'Description' })"
    And I fill "getByRole('textbox', { name: 'Description' })" with "<description>"
    And I fill "getByRole('textbox', { name: 'Url', exact: true })" with "<url>"
    And I fill "getByRole('textbox', { name: 'Search terms (provide a comma' })" with "<searchTerms>"
    And I fill "getByRole('textbox', { name: 'Comments' })" with "<comments>"

    # Verify form elements and submit
    Then element "getByRole('button', { name: 'Publish' })" should be visible
    And element "getByRole('button', { name: 'Create' })" should be visible
    When I click on "getByRole('button', { name: 'Create' })"
    Then I wait for element "getByRole('heading', { name: 'Confirm creating request!' })" to be visible with timeout 10000
    And element "getByRole('button', { name: 'No' })" should be visible
    And element "getByRole('button', { name: 'Yes' })" should be visible

    # Confirm creation
    When I click on "getByRole('button', { name: 'Yes' })"
    Then I wait for element "getByRole('heading', { name: 'Created request' })" to be visible with timeout 10000
    And element "getByText('Successfully created')" should be visible
    And element "getByRole('button', { name: 'Close' })" should be visible
    And I click on "getByRole('button', { name: 'Close' })"

    # Verify table data with parameterized values
    Then the "(//table)[1]//tr[2]/td[4]//span" element should have exact text "FeaturedResult"
    And the "(//table)[1]//tr[2]/td[5]/div" element should have exact text "<title>"
    And the "(//table)[1]//tr[2]/td[6]/div" element should have exact text "<description>"
    And the "(//table)[1]//tr[2]/td[8]/div" element should have exact text "<url>"
    And the "((//table)[1]//tr[2]/td[9]//span)[1]" element should have exact text "<searchTerm1>"
    And the "((//table)[1]//tr[2]/td[9]//span)[2]" element should have exact text "<searchTerm2>"
    And the "(//table)[1]//tr[2]/td[10]//span" element should have exact text "Submitted"
    And the "(//table)[1]//tr[2]/td[13]" element should have exact text "K, Jagannatha"

    Examples:
      | username              | title         | description         | url                      | searchTerms    | comments         | searchTerm1 | searchTerm2 |
      | <EMAIL>  | Test Title    | Test Description    | https://www.google.com   | test, terms    | Test Comments    | test        | terms       |
      | <EMAIL>  | Demo Title    | Demo Description    | https://www.bing.com     | demo, search   | Demo Comments    | demo        | search      |
      | <EMAIL>  | Sample Data   | Sample Info         | https://www.yahoo.com    | sample, test   | Sample Notes     | sample      | test        |