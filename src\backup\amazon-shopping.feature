@ecommerce @amazon
Feature: Amazon Shopping
  As an online shopper
  I want to search for products, view details, and manage my cart
  So that I can make purchases efficiently

  Background:
    Given I navigate to "https://www.amazon.com"
    And I wait for page to load

  @smoke @search
  Scenario: Search for a product on Amazon
    When I fill "#twotabsearchtextbox" with "wireless headphones"
    And I click on "#nav-search-submit-button"
    And I wait for ".s-result-list" to be visible
    Then the page should contain text "Results"
    And there should be more than 5 ".s-result-item" elements
    And the page should contain text "wireless headphones"

  @product-details
  Scenario: View product details
    When I fill "#twotabsearchtextbox" with "kindle paperwhite"
    And I click on "#nav-search-submit-button"
    And I wait for ".s-result-list" to be visible
    And I click on ".s-result-item .a-link-normal.s-no-outline"
    And I wait for "#productTitle" to be visible
    Then the "#productTitle" element should contain text "Kindle"
    And the page should contain text "Buy now"
    And the page should contain text "Add to Cart"
    
    When I store the text from "#productTitle" as "product_title"
    Then the page title should contain stored variable "product_title"

  @shopping-cart
  Scenario: Add product to cart and verify cart contents
    When I fill "#twotabsearchtextbox" with "echo dot"
    And I click on "#nav-search-submit-button"
    And I wait for ".s-result-list" to be visible
    And I click on ".s-result-item .a-link-normal.s-no-outline"
    And I wait for "#productTitle" to be visible
    And I store the text from "#productTitle" as "product_title"
    And I click on "#add-to-cart-button"
    And I wait for "#sw-atc-confirmation" to be visible with timeout 10000
    Then the page should contain text "Added to Cart"
    
    When I click on "#nav-cart"
    And I wait for ".sc-list-item" to be visible
    Then the page should contain text stored in variable "product_title"
    And the ".sc-list-item" element should be visible

  @product-filters
  Scenario: Apply filters to product search results
    When I fill "#twotabsearchtextbox" with "laptop"
    And I click on "#nav-search-submit-button"
    And I wait for ".s-result-list" to be visible
    
    # Filter by brand
    When I click on "span:contains('Apple')"
    And I wait for ".s-result-list" to be visible
    Then the page should contain text "Apple"
    
    # Filter by price range
    When I click on "span:contains('$1,000 to $1,500')"
    And I wait for ".s-result-list" to be visible
    Then the page should contain text "Apple"
    And the page should contain text "$1,000 to $1,500"
    
    # Sort by customer reviews
    When I click on "#a-autoid-0-announce"
    And I wait for "#s-result-sort-select_4" to be visible
    And I click on "#s-result-sort-select_4"
    And I wait for ".s-result-list" to be visible
    Then the page should contain text "Avg. Customer Review"

  @product-reviews
  Scenario: Read product reviews
    When I fill "#twotabsearchtextbox" with "fire tv stick"
    And I click on "#nav-search-submit-button"
    And I wait for ".s-result-list" to be visible
    And I click on ".s-result-item .a-link-normal.s-no-outline"
    And I wait for "#productTitle" to be visible
    
    When I click on "a:contains('customer reviews')"
    And I wait for "#cm_cr-review_list" to be visible
    Then there should be more than 0 ".review" elements
    And the page should contain text "Top reviews"
    
    When I click on "a:contains('5 star')"
    And I wait for "#cm_cr-review_list" to be visible
    Then the page should contain text "5.0 out of 5 stars"

  @deal-of-the-day
  Scenario: Check deal of the day
    When I click on "a:contains('Deals')"
    And I wait for page to load
    And I wait for ".DealGridItem" to be visible
    Then there should be more than 0 ".DealGridItem" elements
    
    When I click on ".DealGridItem-module__dealItemContent_1vFddcq1F8pUxM8dd9FW32:first-child"
    And I wait for "#productTitle" to be visible
    Then the page should contain text "Deal"
    And the page should contain text "% off"

  @wishlist
  Scenario: Add product to wishlist
    When I fill "#twotabsearchtextbox" with "bluetooth speaker"
    And I click on "#nav-search-submit-button"
    And I wait for ".s-result-list" to be visible
    And I click on ".s-result-item .a-link-normal.s-no-outline"
    And I wait for "#productTitle" to be visible
    And I store the text from "#productTitle" as "product_title"
    
    When I click on "#add-to-wishlist-button-submit"
    And I wait for "#WLHUC_result_success" to be visible with timeout 10000
    Then the page should contain text "Added to"
    And the page should contain text "Wish List"
    
    When I click on "#WLHUC_viewlist"
    And I wait for "#wl-item-view" to be visible
    Then the page should contain text stored in variable "product_title"

  @product-comparison
  Scenario: Compare similar products
    When I fill "#twotabsearchtextbox" with "gaming laptop"
    And I click on "#nav-search-submit-button"
    And I wait for ".s-result-list" to be visible
    
    # Select first product for comparison
    When I click on ".s-result-item:nth-child(1) .a-checkbox label"
    
    # Select second product for comparison
    And I click on ".s-result-item:nth-child(2) .a-checkbox label"
    
    # Click compare button
    And I click on "input[value='Compare selected']"
    And I wait for "#comparison_table" to be visible
    
    Then the page should contain text "Product Comparison"
    And there should be 2 ".comparison_table_product_image" elements
