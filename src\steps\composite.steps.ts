import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { CustomWorld } from '../core/custom-world';
import { Logger } from '../utils/logger';
import { EnvConfig } from '../config/env.config';

const logger = new Logger('CompositeSteps');

// ========== AUTHENTICATION COMPOSITE STEPS ==========

/**
 * Complete Microsoft SSO login process
 */
Given('I login with Microsoft SSO using {string}', { timeout: 120000 }, async function(this: CustomWorld, email: string) {
  logger.info(`Performing Microsoft SSO login for: ${email}`);
  
  // Wait for email input and fill
  await this.getLocator("getByRole('textbox', { name: 'Sign in with your KPMG email' })").waitFor({ state: 'visible', timeout: 10000 });
  await this.getLocator("getByRole('textbox', { name: 'Sign in with your KPMG email' })").fill(email);
  
  // Click Next button
  await this.getLocator("getByRole('button', { name: 'Next' })").click();
  
  logger.info('Microsoft SSO login completed');
});

/**
 * Wait for settings page to fully load
 */
Given('I wait for the settings page to load', { timeout: 90000 }, async function(this: CustomWorld) {
  logger.info('Waiting for settings page to load');
  
  // Wait for key elements to be visible
  await this.getLocator("getByRole('button', { name: 'Settings' })").waitFor({ state: 'visible', timeout: 60000 });
  await this.getLocator("getByRole('button', { name: 'Create a request on behalf of' })").waitFor({ state: 'visible', timeout: 30000 });
  
  // Verify page title
  await expect(this.getPage()).toHaveTitle(/KPMG Find - Settings/);
  
  logger.info('Settings page loaded successfully');
});

// ========== FEATURED RESULT CREATION COMPOSITE STEPS ==========

/**
 * Start the featured result creation process
 */
When('I start creating a featured result request', { timeout: 60000 }, async function(this: CustomWorld) {
  logger.info('Starting featured result creation process');
  
  // Click on "Create a request on behalf of"
  await this.getLocator("getByRole('button', { name: 'Create a request on behalf of' })").click();
  
  // Wait for dialog and verify elements
  await this.getLocator("getByRole('heading', { name: 'Create a requests on behalf' })").waitFor({ state: 'visible', timeout: 10000 });
  await expect(this.getLocator("locator('#displayName-label')")).toHaveText('Selected User');
  
  // Select FeaturedResult type
  await this.getLocator("getByRole('combobox', { name: 'FeaturedResult' })").click();
  await this.getLocator("locator('#menu-').getByText('FeaturedResult')").waitFor({ state: 'visible', timeout: 30000 });
  await this.getLocator("locator('#menu-').getByText('FeaturedResult')").click();
  
  // Select user
  await this.getLocator("getByRole('textbox', { name: 'Selected User' })").click();
  await this.getLocator("getByRole('heading', { name: 'Select user for the request' })").waitFor({ state: 'visible', timeout: 10000 });
  
  // Search and select user
  await this.getLocator("getByRole('searchbox', { name: 'description' })").fill('<EMAIL>');
  await this.getLocator("getByLabel('K, Jagannatha')").waitFor({ state: 'visible', timeout: 10000 });
  await this.getLocator("getByLabel('K, Jagannatha')").click();
  await this.getLocator("getByRole('button', { name: 'Select' })").click();
  
  // Click Create to proceed to form
  await this.getLocator("getByRole('button', { name: 'Create' })").waitFor({ state: 'visible', timeout: 10000 });
  await this.getLocator("getByRole('button', { name: 'Create' })").click();
  
  // Wait for form to load
  await this.getLocator("getByText('New Featured Result for K,')").waitFor({ state: 'visible', timeout: 10000 });
  
  logger.info('Featured result creation process started successfully');
});

/**
 * Create featured result with data table
 */
When('I create a featured result request with the following details:', { timeout: 60000 }, async function(this: CustomWorld, dataTable) {
  logger.info('Creating featured result with provided details');
  
  // Start the creation process
  await this.step('I start creating a featured result request');
  
  // Fill form with data from table
  const data = dataTable.rowsHash();
  
  if (data.title) {
    await this.getLocator("getByRole('textbox', { name: 'Title *' })").fill(data.title);
  }
  
  if (data.description) {
    await this.getLocator("getByRole('textbox', { name: 'Description' })").click();
    await this.getLocator("getByRole('textbox', { name: 'Description' })").fill(data.description);
  }
  
  if (data.url) {
    await this.getLocator("getByRole('textbox', { name: 'Url', exact: true })").fill(data.url);
  }
  
  if (data.searchTerms) {
    await this.getLocator("getByRole('textbox', { name: 'Search terms (provide a comma' })").fill(data.searchTerms);
  }
  
  if (data.comments) {
    await this.getLocator("getByRole('textbox', { name: 'Comments' })").fill(data.comments);
  }
  
  // Verify form buttons are visible
  await expect(this.getLocator("getByRole('button', { name: 'Publish' })")).toBeVisible();
  await expect(this.getLocator("getByRole('button', { name: 'Create' })")).toBeVisible();
  
  logger.info('Featured result form filled successfully');
});

/**
 * Confirm the creation of featured result
 */
When('I confirm the creation', { timeout: 30000 }, async function(this: CustomWorld) {
  logger.info('Confirming featured result creation');
  
  // Click Create button
  await this.getLocator("getByRole('button', { name: 'Create' })").click();
  
  // Wait for confirmation dialog
  await this.getLocator("getByRole('heading', { name: 'Confirm creating request!' })").waitFor({ state: 'visible', timeout: 10000 });
  
  // Verify confirmation buttons
  await expect(this.getLocator("getByRole('button', { name: 'No' })")).toBeVisible();
  await expect(this.getLocator("getByRole('button', { name: 'Yes' })")).toBeVisible();
  
  // Confirm creation
  await this.getLocator("getByRole('button', { name: 'Yes' })").click();
  
  // Wait for success message
  await this.getLocator("getByRole('heading', { name: 'Created request' })").waitFor({ state: 'visible', timeout: 10000 });
  await expect(this.getLocator("getByText('Successfully created')")).toBeVisible();
  
  // Close the dialog
  await this.getLocator("getByRole('button', { name: 'Close' })").click();
  
  logger.info('Featured result creation confirmed successfully');
});

// ========== VERIFICATION COMPOSITE STEPS ==========

/**
 * Verify featured result appears in table with expected data
 */
Then('I should see the featured result in the table with:', { timeout: 30000 }, async function(this: CustomWorld, dataTable) {
  logger.info('Verifying featured result in table');
  
  const expectedData = dataTable.rowsHash();
  
  // Verify table data using XPath selectors
  if (expectedData.type) {
    await expect(this.getLocator("(//table)[1]//tr[2]/td[4]//span")).toHaveText(expectedData.type);
  }
  
  if (expectedData.title) {
    await expect(this.getLocator("(//table)[1]//tr[2]/td[5]/div")).toHaveText(expectedData.title);
  }
  
  if (expectedData.description) {
    await expect(this.getLocator("(//table)[1]//tr[2]/td[6]/div")).toHaveText(expectedData.description);
  }
  
  if (expectedData.url) {
    await expect(this.getLocator("(//table)[1]//tr[2]/td[8]/div")).toHaveText(expectedData.url);
  }
  
  if (expectedData.status) {
    await expect(this.getLocator("(//table)[1]//tr[2]/td[10]//span")).toHaveText(expectedData.status);
  }
  
  if (expectedData.requestor) {
    await expect(this.getLocator("(//table)[1]//tr[2]/td[13]")).toHaveText(expectedData.requestor);
  }
  
  logger.info('Featured result verified in table successfully');
});

/**
 * Verify form elements with data table
 */
Then('I should see all required form elements:', { timeout: 30000 }, async function(this: CustomWorld, dataTable) {
  logger.info('Verifying form elements');
  
  const elements = dataTable.hashes();
  
  for (const element of elements) {
    const selector = `getByRole('${element.type}', { name: '${element.element}' })`;
    
    // Check if element is visible
    await expect(this.getLocator(selector)).toBeVisible();
    
    // Additional checks based on element properties
    if (element.required === 'true') {
      // For required fields, you might want to check for required attribute
      logger.info(`Verified required element: ${element.element}`);
    }
  }
  
  logger.info('All form elements verified successfully');
});

// ========== WORKFLOW COMPOSITE STEPS ==========

/**
 * Complete workflow with documentation string
 */
When('I perform the complete featured result workflow:', { timeout: 180000 }, async function(this: CustomWorld, docString: string) {
  logger.info('Performing complete featured result workflow');
  logger.info(`Workflow steps: ${docString}`);
  
  // Execute the complete workflow
  await this.step('I create a featured result request with the following details:', [
    ['field', 'value'],
    ['title', 'Workflow Test Title'],
    ['description', 'Workflow Test Description'],
    ['url', 'https://www.example.com'],
    ['searchTerms', 'workflow, test'],
    ['comments', 'Workflow Test Comments']
  ]);
  
  await this.step('I confirm the creation');
  
  logger.info('Complete workflow executed successfully');
});

/**
 * Verify workflow completion
 */
Then('the workflow should complete successfully', { timeout: 30000 }, async function(this: CustomWorld) {
  logger.info('Verifying workflow completion');
  
  // Verify the result appears in table
  await this.step('I should see the featured result in the table with:', [
    ['column', 'expectedValue'],
    ['type', 'FeaturedResult'],
    ['title', 'Workflow Test Title'],
    ['description', 'Workflow Test Description'],
    ['url', 'https://www.example.com'],
    ['status', 'Submitted'],
    ['requestor', 'K, Jagannatha']
  ]);
  
  logger.info('Workflow completed successfully');
});
