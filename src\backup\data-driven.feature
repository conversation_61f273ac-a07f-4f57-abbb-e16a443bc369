@data-driven
Feature: Data-Driven Testing
  As a QA engineer
  I want to run the same test with different data sets
  So that I can test multiple scenarios efficiently

  @data-source:users.json
  Scenario Outline: Login with different credentials
    Given I navigate to "<login_url>"
    And I wait for page to load
    When I fill "<username_field>" with "<username>"
    And I fill "<password_field>" with "<password>"
    And I click on "<submit_button>"
    Then I should see the appropriate message for "<description>"

    Examples:
      | login_url                                | username_field | password_field | submit_button         | username    | password             | description                      |
      | https://the-internet.herokuapp.com/login | #username      | #password      | button[type='submit'] | tomsmith    | SuperSecretPassword! | Valid credentials                |
      | https://the-internet.herokuapp.com/login | #username      | #password      | button[type='submit'] | invaliduser | invalidpassword      | Invalid credentials              |
      | https://the-internet.herokuapp.com/login | #username      | #password      | button[type='submit'] | tomsmith    | wrongpassword        | Valid username, invalid password |
      | https://the-internet.herokuapp.com/login | #username      | #password      | button[type='submit'] | wronguser   | SuperSecretPassword! | Invalid username, valid password |

  @data-source:generated
  Scenario: Login with generated credentials
    Given I have generated random user credentials
    When I navigate to "https://the-internet.herokuapp.com/login"
    And I wait for page to load
    And I fill "#username" with the generated username
    And I fill "#password" with the generated password
    And I click on "button[type='submit']"
    Then I should see an element ".flash.error" containing error message

  @data-source:csv
  Scenario: Login with credentials from CSV file
    Given I have loaded user credentials from CSV file "users.csv"
    When I navigate to "https://the-internet.herokuapp.com/login"
    And I wait for page to load
    And I login with each set of credentials using "#username" and "#password" fields and "button[type='submit']" button
    Then I should verify login results based on expected outcomes
