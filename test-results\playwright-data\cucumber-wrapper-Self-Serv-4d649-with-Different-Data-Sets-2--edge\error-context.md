# Test info

- Name: Self Service Featured Results >> TC0101- Create Featured Result with Different Data Sets [2]
- Location: C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:34:11

# Error details

```
TimeoutError: locator.waitFor: Timeout 60000ms exceeded.
Call log:
  - waiting for getBy<PERSON><PERSON>('button', { name: 'Setting<PERSON>' }) to be visible
    - waiting for" https://gfs2.glb.kworld.kpmg.com/adfs/ls/?client-request-id=01975df9-3aac-759a-805f-c719ba970861&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%26es…" navigation to finish...
    - navigated to "https://gfs2.glb.kworld.kpmg.com/adfs/ls/?client-request-id=01975df9-3aac-759a-805f-c719ba970861&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%26es…"
    - waiting for" https://gfs2.glb.kworld.kpmg.com/adfs/ls/wia?client-request-id=01975df9-3aac-759a-805f-c719ba970861&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%2…" navigation to finish...
    - navigated to "https://gfs2.glb.kworld.kpmg.com/adfs/ls/wia?client-request-id=01975df9-3aac-759a-805f-c719ba970861&wa=wsignin1.0&wtrealm=urn%3afederation%3aMicrosoftOnline&wctx=LoginOptions%3D3%26estsredirect%3d2%2…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P9RhuBdezgL7_Gl8qfXAGZrVn66bnfTQY1G7CuFfX-mIC4VLRc9UvCInpg…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P-SgmicaCrmDcg4WemRYPu9_W6eYjlqNEeqKdp0nOlO1wbMK4ITEqhzqGO…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P-cLx0Kxd8leWjoBN-drFbXMQUPcbzBiIVGxDeAc_Y-uEbr_Ld_deAnv90…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P_n3kujRBfUrdpgaFz3wyye40SEjiNENLEiZlP3qeeghqOsMXPMtLZ6xts…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P_bFCP6BQEQL-h0A6lPrLxln0TM5at2_dQNOTgtWyGF556wULj2l2DdWCz…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P8aQyD0XFE-yMunEL5MQ_bkxUid9bEK-DcVjqwWowDsFK-nHukNanIu2no…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P-_825jrK1ombmGq-WxqcE2NPUnJA5r3jxwOyKowrfyXVNZupgDocO6Wll…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P8cYrLI6LF7QP_qk0HmacANVPF_p3kfwwMzX_kTU4IwqY8ibXAsUFu_DZv…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P-9-Bg7vJV9_qsKJAMM_gz8I7ex6HQe03xjh2KYd6TYwTTjFMkxUAGKgeY…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P9fEGiKZ9O3Hz566o04kp3IBf1Qw0pm0oRV9V0AakpwyDd_i0CcuX9DcCJ…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P95NePQpuMim5wxENTryOx_ezUck1GSaDAratnqcKmE724VYq5nAsFDiN6…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P8P3ws25-huGL4rjCyaSGbgMu0r-Nh6BWcEBr1nC2486i_vdh9jqtkmmz4…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P8IgDJYwRSLQwzV4rv9-myfww3mjNoNw1-ydZIaGyZiTAHg9sxPfaqE-Q9…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P8mNrt7jObcSES-twcv32r7gzNbplUZxI9UTBFk0Ry1MYzbCV-MPfiDtuK…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P_SfvlFnWzhsek_uzA3xtbpjJ2zAgNxye_eKYe8kkQJ2pZGQH50fpChXFW…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P8qyAVE_7QsJ2-LwcU9Myl62XAOxlVQdNgVCe3H0SfLxF8ZHA4Y_jc4lZP…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P8VEz3XllUNDIuwLIMGLWD2_Wh7-FypZBquqEBWZQvLDD7dtGAPSvmssVG…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P8P6ZAN5UeZ53oPtKpgWH44Q6nzOtDNPrw8FBLUjkqwnn_NdtlSwbVgsQR…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P_JTZm30dpVUgde7chTuI9hxv1vYqKhN6-fZGWIoYZepghimNZ9Vz5Zclo…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://es-settings-staging.kpmg.com/#code=1.AREAuyT_3okgAESMjvceaAN4sl2M7K_TJzZGgZ6L5OvMmj0RAAARAA.AgABBAIAAABVrSpeuWamRam2jAF1XRQEAwDs_wUA9P9kHtHEtJILmilkAX-1VhWfKehWMRnCMIcRF4wO8Wa120yS45gL-9C9VkB…"
    - waiting for" https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…" navigation to finish...
    - navigated to "https://login.microsoftonline.com/deff24bb-2089-4400-8c8e-f71e680378b2/oauth2/v2.0/authorize?client_id=afec8c5d-27d3-4636-819e-8be4ebcc9a3d&scope=api%3A%2F%2Fkpmg.com%2Fafec8c5d-27d3-4636-819e-8be4eb…"

    at PlaywrightWorld.waitForElement (C:\workspace\Playwright_Automation_Framework\src\core\playwright-world.ts:697:19)
    at Object.handler (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:189:21)
    at StepDefinitionRegistry.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:361:23)
    at PlaywrightCucumberRunner.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:280:31)
    at PlaywrightCucumberRunner.executeScenario (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:321:22)
    at C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:45:9
```

# Page snapshot

```yaml
- img "Organization background image"
- img "Microsoft"
- main:
  - heading "Pick an account" [level=1]
  - alert: We couldn't sign you in. Please try again.
  - list "We couldn't sign you in. Please try again. Pick an account":
    - listitem:
      - button "Sign <NAME_EMAIL> work or school account.": K, Jagannatha <EMAIL> Signed in
      - button "Open menu"
    - listitem:
      - button "Use another account"
- contentinfo:
  - link "Terms of use":
    - /url: https://www.microsoft.com/en-US/servicesagreement/
  - link "Privacy & cookies":
    - /url: https://privacy.microsoft.com/en-US/privacystatement
  - button "Click here for troubleshooting information": ...
```

# Test source

```ts
  597 |           } else if (part.startsWith('getByRole(')) {
  598 |             const match = part.match(/getByRole\('([^']+)'(?:,\s*({[^}]+}))?\)/);
  599 |             if (match) {
  600 |               const role = match[1];
  601 |               const optionsStr = match[2];
  602 |
  603 |               if (optionsStr) {
  604 |                 try {
  605 |                   const options = eval(`(${optionsStr})`);
  606 |                   locator = locator.getByRole(role as any, options);
  607 |                 } catch {
  608 |                   locator = locator.getByRole(role as any);
  609 |                 }
  610 |               } else {
  611 |                 locator = locator.getByRole(role as any);
  612 |               }
  613 |             }
  614 |           } else if (part.startsWith('getByLabel(')) {
  615 |             const match = part.match(/getByLabel\('([^']+)'(?:,\s*({[^}]+}))?\)/);
  616 |             if (match) {
  617 |               const label = match[1];
  618 |               const optionsStr = match[2];
  619 |
  620 |               if (optionsStr) {
  621 |                 try {
  622 |                   const options = eval(`(${optionsStr})`);
  623 |                   locator = locator.getByLabel(label, options);
  624 |                 } catch {
  625 |                   locator = locator.getByLabel(label);
  626 |                 }
  627 |               } else {
  628 |                 locator = locator.getByLabel(label);
  629 |               }
  630 |             }
  631 |           }
  632 |           // Add more chained locator types as needed
  633 |         }
  634 |       }
  635 |
  636 |       return locator;
  637 |
  638 |     } catch (error) {
  639 |       this.logger.warn(`Failed to parse locator with chain: ${selector}, falling back to simple locator`);
  640 |       return this.page.locator(selector);
  641 |     }
  642 |   }
  643 |
  644 |   /**
  645 |    * Click on element with action highlighting (red)
  646 |    */
  647 |   async clickElement(selector: string): Promise<void> {
  648 |     this.logger.info(`🖱️ Clicking: ${selector}`);
  649 |     const element = this.parseLocator(selector);
  650 |
  651 |     // Action highlight before clicking (red)
  652 |     if (EnvConfig.HIGHLIGHT_ELEMENTS) {
  653 |       try {
  654 |         await this.quickHighlight(element);
  655 |       } catch {
  656 |         // Ignore highlighting errors
  657 |       }
  658 |     }
  659 |
  660 |     await element.click();
  661 |   }
  662 |
  663 |   /**
  664 |    * Fill input field with action highlighting (red)
  665 |    */
  666 |   async fillField(selector: string, value: string): Promise<void> {
  667 |     this.logger.info(`⌨️ Filling ${selector} with: ${value}`);
  668 |     const element = this.parseLocator(selector);
  669 |
  670 |     // Action highlight before filling (red)
  671 |     if (EnvConfig.HIGHLIGHT_ELEMENTS) {
  672 |       try {
  673 |         await this.quickHighlight(element);
  674 |       } catch {
  675 |         // Ignore highlighting errors
  676 |       }
  677 |     }
  678 |
  679 |     await element.fill(value);
  680 |   }
  681 |
  682 |   /**
  683 |    * Type in input field
  684 |    */
  685 |   async typeInField(selector: string, value: string): Promise<void> {
  686 |     this.logger.info(`⌨️ Typing ${value} in: ${selector}`);
  687 |     const element = this.parseLocator(selector);
  688 |     await element.type(value);
  689 |   }
  690 |
  691 |   /**
  692 |    * Wait for element to be visible with action highlighting (red)
  693 |    */
  694 |   async waitForElement(selector: string, timeout: number = 30000): Promise<void> {
  695 |     this.logger.info(`⏳ Waiting for element: ${selector}`);
  696 |     const element = this.parseLocator(selector);
> 697 |     await element.waitFor({ state: 'visible', timeout });
      |                   ^ TimeoutError: locator.waitFor: Timeout 60000ms exceeded.
  698 |
  699 |     // Action highlight after element becomes visible (red)
  700 |     if (EnvConfig.HIGHLIGHT_ELEMENTS) {
  701 |       try {
  702 |         await this.quickHighlight(element);
  703 |       } catch {
  704 |         // Ignore highlighting errors
  705 |       }
  706 |     }
  707 |   }
  708 |
  709 |   /**
  710 |    * Check if element is visible
  711 |    */
  712 |   async isElementVisible(selector: string): Promise<boolean> {
  713 |     try {
  714 |       const element = this.parseLocator(selector);
  715 |       return await element.isVisible();
  716 |     } catch {
  717 |       return false;
  718 |     }
  719 |   }
  720 |
  721 |   /**
  722 |    * Get element text
  723 |    */
  724 |   async getElementText(selector: string): Promise<string> {
  725 |     const element = this.parseLocator(selector);
  726 |     return await element.textContent() || '';
  727 |   }
  728 |
  729 |   /**
  730 |    * Press keyboard key
  731 |    */
  732 |   async pressKey(key: string): Promise<void> {
  733 |     this.logger.info(`⌨️ Pressing key: ${key}`);
  734 |     await this.page.keyboard.press(key);
  735 |   }
  736 |
  737 |   /**
  738 |    * Scroll to element
  739 |    */
  740 |   async scrollToElement(selector: string): Promise<void> {
  741 |     this.logger.info(`📜 Scrolling to: ${selector}`);
  742 |     const element = this.parseLocator(selector);
  743 |     await element.scrollIntoViewIfNeeded();
  744 |   }
  745 |
  746 |   /**
  747 |    * Wait for specified time
  748 |    */
  749 |   async wait(milliseconds: number): Promise<void> {
  750 |     this.logger.info(`⏳ Waiting for ${milliseconds}ms`);
  751 |     await this.page.waitForTimeout(milliseconds);
  752 |   }
  753 |
  754 |   /**
  755 |    * Verify text is present on page
  756 |    */
  757 |   async verifyTextPresent(text: string): Promise<void> {
  758 |     this.logger.info(`🔍 Verifying page contains: ${text}`);
  759 |     const bodyLocator = this.page.locator('body');
  760 |     await expect(bodyLocator).toContainText(text);
  761 |   }
  762 |
  763 |   /**
  764 |    * Verify element is visible with assertion highlighting (green)
  765 |    */
  766 |   async verifyElementVisible(selector: string): Promise<void> {
  767 |     this.logger.info(`👁️ Verifying element is visible: ${selector}`);
  768 |     const element = this.parseLocator(selector);
  769 |
  770 |     await expect(element).toBeVisible();
  771 |
  772 |     // Assertion highlight for verification (green)
  773 |     try {
  774 |       await this.assertionHighlight(element);
  775 |     } catch {
  776 |       // Ignore highlighting errors
  777 |     }
  778 |   }
  779 |
  780 |   /**
  781 |    * Verify page title contains text
  782 |    */
  783 |   async verifyTitleContains(text: string): Promise<void> {
  784 |     this.logger.info(`📄 Verifying title contains: ${text}`);
  785 |     const title = await this.getTitle();
  786 |     if (!title.includes(text)) {
  787 |       throw new Error(`Expected title to contain "${text}", but got "${title}"`);
  788 |     }
  789 |   }
  790 |
  791 |   /**
  792 |    * Verify element contains text with assertion highlighting (green)
  793 |    */
  794 |   async verifyElementContainsText(selector: string, text: string): Promise<void> {
  795 |     this.logger.info(`🔍 Verifying ${selector} contains: ${text}`);
  796 |     const element = this.parseLocator(selector);
  797 |
```