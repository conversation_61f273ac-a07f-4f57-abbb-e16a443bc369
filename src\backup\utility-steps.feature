@utility
Feature: Utility Steps
  As a QA engineer
  I want to use utility steps
  So that I can perform advanced testing operations

  Background:
    Given I navigate to "https://example.com"
    And I wait for page to load

  @utility @screenshot
  Scenario: Take screenshots
    When I navigate to "https://example.com/login"
    And I wait for page to load
    When I take a screenshot named "login-page"
    And I take a screenshot of "#login-form" named "login-form"

  @utility @variables
  Scenario: Store and use variables
    When I navigate to "https://example.com/profile"
    And I wait for page to load
    When I store the text from "#username" as "username"
    And I store the attribute "href" from "#profile-link" as "profile-url"
    And I navigate to "https://example.com/search"
    And I wait for page to load
    And I fill "#search-input" with stored variable "username"
    And I click on "#search-button"
    Then the "#search-results" element should contain text stored in variable "username"

  @utility @random-data
  Scenario: Generate and use random data
    When I navigate to "https://example.com/register"
    And I wait for page to load
    When I generate a random email as "email"
    And I generate a random username as "username"
    And I generate a random password as "password"
    And I generate a random name as "name"
    And I generate a random phone number as "phone"
    And I generate a random address as "address"
    And I generate a random number between 18 and 65 as "age"
    And I fill "#email" with stored variable "email"
    And I fill "#username" with stored variable "username"
    And I fill "#password" with stored variable "password"
    And I fill "#name" with stored variable "name"
    And I fill "#phone" with stored variable "phone"
    And I fill "#address" with stored variable "address"
    And I fill "#age" with stored variable "age"
    Then the "#email" input should have value stored in variable "email"

  @utility @conditional
  Scenario: Use conditional steps
    When I navigate to "https://example.com/dashboard"
    And I wait for page to load
    When if "#welcome-popup" is visible, then:
      | I click on "#close-popup" |
    And if "#login-form" is not visible, then:
      | I click on "#login-link" |
      | I wait for "#login-form" to be visible |
    And if page contains text "Welcome", then:
      | I click on "#dashboard-link" |
      | I wait for "#dashboard" to be visible |

  @utility @browser
  Scenario: Control browser settings
    When I set viewport size to width 1920 and height 1080
    And I navigate to "https://example.com/responsive"
    And I wait for page to load
    Then the "#desktop-view" element should be visible
    When I set viewport size to width 375 and height 667
    Then the "#mobile-view" element should be visible
    When I emulate device "iPhone 12"
    Then the "#ios-view" element should be visible
