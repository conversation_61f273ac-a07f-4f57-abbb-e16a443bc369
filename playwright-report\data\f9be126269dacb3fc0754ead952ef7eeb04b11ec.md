# Test info

- Name: Self Service Featured Results >> TC0101- Create Featured Result with Test Data
- Location: C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:34:11

# Error details

```
TimeoutError: locator.click: Timeout 15000ms exceeded.
Call log:
  - waiting for getByR<PERSON>('button', { name: 'Create a request on behalf of' })

    at PlaywrightWorld.clickElement (C:\workspace\Playwright_Automation_Framework\src\core\playwright-world.ts:660:19)
    at Object.handler (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:62:9)
    at StepDefinitionRegistry.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\step-definition-registry.ts:361:9)
    at PlaywrightCucumberRunner.executeStep (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:197:7)
    at PlaywrightCucumberRunner.executeScenario (C:\workspace\Playwright_Automation_Framework\src\core\playwright-cucumber-runner.ts:232:9)
    at C:\workspace\Playwright_Automation_Framework\tests\cucumber-wrapper.spec.ts:45:9
```

# Test source

```ts
  560 |   private parseLocatorWithChain(selector: string): any {
  561 |     try {
  562 |       // Extract the CSS selector from locator('selector')
  563 |       const locatorMatch = selector.match(/^locator\('([^']+)'\)/);
  564 |       if (!locatorMatch) {
  565 |         throw new Error(`Invalid locator syntax: ${selector}`);
  566 |       }
  567 |
  568 |       const cssSelector = locatorMatch[1];
  569 |       let locator = this.page.locator(cssSelector);
  570 |
  571 |       // Check if there are chained methods
  572 |       const remainingSelector = selector.substring(locatorMatch[0].length);
  573 |       if (remainingSelector.startsWith('.getBy')) {
  574 |         // Parse chained methods
  575 |         const chainedParts = remainingSelector.split('.getBy');
  576 |
  577 |         for (let i = 1; i < chainedParts.length; i++) {
  578 |           const part = 'getBy' + chainedParts[i];
  579 |
  580 |           if (part.startsWith('getByText(')) {
  581 |             const match = part.match(/getByText\('([^']+)'(?:,\s*({[^}]+}))?\)/);
  582 |             if (match) {
  583 |               const text = match[1];
  584 |               const optionsStr = match[2];
  585 |
  586 |               if (optionsStr) {
  587 |                 try {
  588 |                   const options = eval(`(${optionsStr})`);
  589 |                   locator = locator.getByText(text, options);
  590 |                 } catch {
  591 |                   locator = locator.getByText(text);
  592 |                 }
  593 |               } else {
  594 |                 locator = locator.getByText(text);
  595 |               }
  596 |             }
  597 |           } else if (part.startsWith('getByRole(')) {
  598 |             const match = part.match(/getByRole\('([^']+)'(?:,\s*({[^}]+}))?\)/);
  599 |             if (match) {
  600 |               const role = match[1];
  601 |               const optionsStr = match[2];
  602 |
  603 |               if (optionsStr) {
  604 |                 try {
  605 |                   const options = eval(`(${optionsStr})`);
  606 |                   locator = locator.getByRole(role as any, options);
  607 |                 } catch {
  608 |                   locator = locator.getByRole(role as any);
  609 |                 }
  610 |               } else {
  611 |                 locator = locator.getByRole(role as any);
  612 |               }
  613 |             }
  614 |           } else if (part.startsWith('getByLabel(')) {
  615 |             const match = part.match(/getByLabel\('([^']+)'(?:,\s*({[^}]+}))?\)/);
  616 |             if (match) {
  617 |               const label = match[1];
  618 |               const optionsStr = match[2];
  619 |
  620 |               if (optionsStr) {
  621 |                 try {
  622 |                   const options = eval(`(${optionsStr})`);
  623 |                   locator = locator.getByLabel(label, options);
  624 |                 } catch {
  625 |                   locator = locator.getByLabel(label);
  626 |                 }
  627 |               } else {
  628 |                 locator = locator.getByLabel(label);
  629 |               }
  630 |             }
  631 |           }
  632 |           // Add more chained locator types as needed
  633 |         }
  634 |       }
  635 |
  636 |       return locator;
  637 |
  638 |     } catch (error) {
  639 |       this.logger.warn(`Failed to parse locator with chain: ${selector}, falling back to simple locator`);
  640 |       return this.page.locator(selector);
  641 |     }
  642 |   }
  643 |
  644 |   /**
  645 |    * Click on element with action highlighting (red)
  646 |    */
  647 |   async clickElement(selector: string): Promise<void> {
  648 |     this.logger.info(`🖱️ Clicking: ${selector}`);
  649 |     const element = this.parseLocator(selector);
  650 |
  651 |     // Action highlight before clicking (red)
  652 |     if (EnvConfig.HIGHLIGHT_ELEMENTS) {
  653 |       try {
  654 |         await this.quickHighlight(element);
  655 |       } catch {
  656 |         // Ignore highlighting errors
  657 |       }
  658 |     }
  659 |
> 660 |     await element.click();
      |                   ^ TimeoutError: locator.click: Timeout 15000ms exceeded.
  661 |   }
  662 |
  663 |   /**
  664 |    * Fill input field with action highlighting (red)
  665 |    */
  666 |   async fillField(selector: string, value: string): Promise<void> {
  667 |     this.logger.info(`⌨️ Filling ${selector} with: ${value}`);
  668 |     const element = this.parseLocator(selector);
  669 |
  670 |     // Action highlight before filling (red)
  671 |     if (EnvConfig.HIGHLIGHT_ELEMENTS) {
  672 |       try {
  673 |         await this.quickHighlight(element);
  674 |       } catch {
  675 |         // Ignore highlighting errors
  676 |       }
  677 |     }
  678 |
  679 |     await element.fill(value);
  680 |   }
  681 |
  682 |   /**
  683 |    * Type in input field
  684 |    */
  685 |   async typeInField(selector: string, value: string): Promise<void> {
  686 |     this.logger.info(`⌨️ Typing ${value} in: ${selector}`);
  687 |     const element = this.parseLocator(selector);
  688 |     await element.type(value);
  689 |   }
  690 |
  691 |   /**
  692 |    * Wait for element to be visible with action highlighting (red)
  693 |    */
  694 |   async waitForElement(selector: string, timeout: number = 30000): Promise<void> {
  695 |     this.logger.info(`⏳ Waiting for element: ${selector}`);
  696 |     const element = this.parseLocator(selector);
  697 |     await element.waitFor({ state: 'visible', timeout });
  698 |
  699 |     // Action highlight after element becomes visible (red)
  700 |     if (EnvConfig.HIGHLIGHT_ELEMENTS) {
  701 |       try {
  702 |         await this.quickHighlight(element);
  703 |       } catch {
  704 |         // Ignore highlighting errors
  705 |       }
  706 |     }
  707 |   }
  708 |
  709 |   /**
  710 |    * Check if element is visible
  711 |    */
  712 |   async isElementVisible(selector: string): Promise<boolean> {
  713 |     try {
  714 |       const element = this.parseLocator(selector);
  715 |       return await element.isVisible();
  716 |     } catch {
  717 |       return false;
  718 |     }
  719 |   }
  720 |
  721 |   /**
  722 |    * Get element text
  723 |    */
  724 |   async getElementText(selector: string): Promise<string> {
  725 |     const element = this.parseLocator(selector);
  726 |     return await element.textContent() || '';
  727 |   }
  728 |
  729 |   /**
  730 |    * Press keyboard key
  731 |    */
  732 |   async pressKey(key: string): Promise<void> {
  733 |     this.logger.info(`⌨️ Pressing key: ${key}`);
  734 |     await this.page.keyboard.press(key);
  735 |   }
  736 |
  737 |   /**
  738 |    * Scroll to element
  739 |    */
  740 |   async scrollToElement(selector: string): Promise<void> {
  741 |     this.logger.info(`📜 Scrolling to: ${selector}`);
  742 |     const element = this.parseLocator(selector);
  743 |     await element.scrollIntoViewIfNeeded();
  744 |   }
  745 |
  746 |   /**
  747 |    * Wait for specified time
  748 |    */
  749 |   async wait(milliseconds: number): Promise<void> {
  750 |     this.logger.info(`⏳ Waiting for ${milliseconds}ms`);
  751 |     await this.page.waitForTimeout(milliseconds);
  752 |   }
  753 |
  754 |   /**
  755 |    * Verify text is present on page
  756 |    */
  757 |   async verifyTextPresent(text: string): Promise<void> {
  758 |     this.logger.info(`🔍 Verifying page contains: ${text}`);
  759 |     const bodyLocator = this.page.locator('body');
  760 |     await expect(bodyLocator).toContainText(text);
```