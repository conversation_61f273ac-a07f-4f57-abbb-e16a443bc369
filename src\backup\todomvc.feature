@todomvc @demo @showcase
Feature: TodoMVC Application - Framework Showcase
  As a QA engineer using the Playwright automation framework
  I want to test the TodoMVC application
  So that I can demonstrate the framework's capabilities

  Background:
    Given I navigate to "https://demo.playwright.dev/todomvc/#/"
    And I wait for page to load

  @smoke @basic
  Scenario: Basic TodoMVC Functionality
    # Verify page elements are present
    Then ".todoapp" should be visible
    And ".new-todo" should be visible
    And the page title should contain "TodoMVC"
    And the page URL should contain "todomvc"

  @forms @input
  Scenario: Add New Todo Items
    # Add a new todo item
    When I fill ".new-todo" with "Learn Playwright automation"
    And I press "Enter"
    Then ".todo-list li" should be visible
    And ".todo-list li" should contain text "Learn Playwright automation"
    And ".todo-count" should be visible

  @interaction @checkboxes
  Scenario: Complete Todo Items
    # Add a todo and mark it as complete
    Given I fill ".new-todo" with "Complete this task"
    And I press "Enter"
    When I click on ".todo-list li .toggle"
    Then ".todo-list li.completed" should be visible
    And ".todo-count" should contain text "0 items left"

  @editing @double-click
  Scenario: Edit Todo Items
    # Add a todo and edit it
    Given I fill ".new-todo" with "Original task"
    And I press "Enter"
    When I double click on ".todo-list li label"
    And I clear ".todo-list li .edit"
    And I fill ".todo-list li .edit" with "Updated task"
    And I press "Enter"
    Then ".todo-list li" should contain text "Updated task"

  @delete @hover
  Scenario: Delete Todo Items
    # Add a todo and delete it
    Given I fill ".new-todo" with "Delete this task"
    And I press "Enter"
    When I hover over ".todo-list li"
    And I click on ".todo-list li .destroy"
    Then ".todo-list li" should not be visible

  @filters @navigation
  Scenario: Filter Todo Items
    # Add todos in different states
    Given I fill ".new-todo" with "Active task"
    And I press "Enter"
    And I fill ".new-todo" with "Completed task"
    And I press "Enter"
    And I click on ".todo-list li:last-child .toggle"

    # Test filters
    When I click on "a[href='#/active']"
    Then ".todo-list li" should be visible
    When I click on "a[href='#/completed']"
    Then ".todo-list li" should be visible
    When I click on "a[href='#/']"
    Then ".todo-list li" should be visible

  @bulk @toggle-all
  Scenario: Toggle All Todos
    # Add multiple todos
    Given I fill ".new-todo" with "Task 1"
    And I press "Enter"
    And I fill ".new-todo" with "Task 2"
    And I press "Enter"

    # Toggle all to complete
    When I click on ".toggle-all"
    Then ".todo-list li.completed" should be visible
    And ".todo-count" should contain text "0 items left"

  @clear @cleanup
  Scenario: Clear Completed Todos
    # Add todos and mark some as completed
    Given I fill ".new-todo" with "Task 1"
    And I press "Enter"
    And I fill ".new-todo" with "Task 2"
    And I press "Enter"
    And I click on ".todo-list li:first-child .toggle"

    # Clear completed
    When I wait for ".clear-completed" to be visible
    And I click on ".clear-completed"
    Then ".todo-list li" should be visible
    And ".todo-count" should contain text "1 item left"


